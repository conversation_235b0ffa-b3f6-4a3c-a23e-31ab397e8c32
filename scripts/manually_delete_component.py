"""
Make sure the blackduck module is installed before running the script
pip install -U blackduck
Check https://github.com/blackducksoftware/hub-rest-api-python for more details
Example
python ./manually_delete_component.py -pid 5ce51d83-00e5-4c41-a48f-10bbaab7d78e -pvid f4166f9a-c903-41ae-9326-e7928f109c36 -cid 08b461c4-5c21-4839-a503-1ae6e43d1585 -vid 185cf532-5227-4cbf-bdd2-87a2d9341b7d -u https://cn58sigkb01/ -t OTNmNTMxNWItMzk0ZC00NjgzLTg4ZDQtZDgyZDdlNjc3YWVjOmM5NGFlYWQ4LTdjZGMtNDNlYy1iOGRiLWJkNzUyNGQ0NmIzYw==
"""

import argparse

from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)


def get_parser():
    parser = argparse.ArgumentParser('Manually add a component to a project version')
    parser.add_argument('-pid', '--project_id',
                        help='Project id of destination project')
    parser.add_argument('-pvid', '--project_version_id',
                        help='Version id of destination project')
    parser.add_argument('-cid', '--component_id',
                        help='Component id to be deleted')
    parser.add_argument('-vid', '--component_version_id', nargs='?',
                        help='[Optional] Component version id to be deleted', default=None)
    parser.add_argument('-u', '--url_base',
                        help='Base URL of Black Duck host. e.g. https://blackduck-hostname')
    parser.add_argument('-t', '--token', help='API token for Black Duck host')

    return parser


def command_line_runner():
    parser = get_parser()
    args = vars(parser.parse_args())

    if not args['project_id'] or not args['project_version_id'] or not \
            args['component_id'] or not args['url_base'] or not args['token']:
        parser.print_help()
        return

    bd = Client(
        token=args['token'],
        base_url=args['url_base'],
        verify=False  # TLS certificate verification
    )

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }

    del_component_api = f'/api/projects/{args["project_id"]}/versions/' \
                        f'{args["project_version_id"]}/components/' \
                        f'{args["component_id"]}'

    if args["component_version_id"] is not None:
        del_component_api += f'/versions/{args["component_version_id"]}'

    r = bd.session.delete(del_component_api, headers=headers)
    if r.status_code == 204:
        logging.info('The Component was deleted successfully.')
    else:
        logging.error(f'Failed to delete the component. The status code is '
                      f'{r.status_code}. The returned text is "{r.text}".')


if __name__ == '__main__':
    command_line_runner()
