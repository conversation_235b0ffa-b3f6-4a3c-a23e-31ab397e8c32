from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='NTU5NTBmNTUtNjdkYi00ZDE4LTliMTAtNzBkMjBjZWQ5MjRjOmM3NmE1YWM0LTFhZDktNDBlZC1hYjE5LWIxNGE4MDU3Mzc0NA==',
    base_url='https://lobsterapj.app.blackduck.com/',
    verify=False  # TLS certificate verification
)

# for project in bd.get_resource('projects'):
#     for project_version in bd.get_resource('versions', parent=project):
#         # print(project_version)
#         url = project_version['_meta']['href'] + '/bom-status'
#         data = bd.get_json(url)
#         if data['status'] != 'UP_TO_DATE':
#             print(f'{url} : bom status {data["status"]}')
    # break

for project in bd.get_resource('projects'):
    for project_version in bd.get_resource('versions', parent=project):
        # print(project_version)
        url = project_version['_meta']['href'] + '/bom-events'
        print(url)
        data = bd.get_json(url)
        if data['totalCount'] != 0:
            print(f'{url} : bom events {data}')

# traverse report status
# for project in bd.get_resource('projects'):
#     for project_version in bd.get_resource('versions', parent=project):
#         # print(project_version)
#         url = project_version['_meta']['href'] + '/reports'
#         headers = {
#             'Accept': 'application/vnd.blackducksoftware.report-4+json'
#         }
#         data = bd.get_json(url, headers=headers)
#         for it in data['items']:
#             href = it['_meta']['href']
#             report_id = href.split('/')[-1]
#             if report_id in ['cb9cf60a-9d5d-4bd9-9fcd-e23b645b7f6a', '330d7aa9-7885-4b12-8241-8fb44181ac41']:
#                 print(report_id, href)
