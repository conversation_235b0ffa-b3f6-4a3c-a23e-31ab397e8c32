from time import sleep

import pandas as pd
from blackduck import Client
import logging
import json


def manually_add_component(bd, project_id, project_version_id, component_id,
                           component_version_id=None, origin_id=None):
    component_url = bd.base_url + f'api/components/{component_id}'
    if component_version_id is not None:
        component_url += f'/versions/{component_version_id}'
        if origin_id is not None:
            component_url += f'/origins/{origin_id}'
    # print(component_url)
    payload = {
        'component': component_url
    }

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }

    add_component_api = f'/api/projects/{project_id}/versions/' \
                        f'{project_version_id}/components'
    # print(add_component_api)

    r = bd.session.post(add_component_api, data=json.dumps(payload),
                        headers=headers)
    if r.status_code == 200:
        print(f'The Component {component_id} / {component_version_id} was added successfully.')
    else:
        print(f'Failed to add the component. The status code is '
              f'{r.status_code}. The returned text is "{r.text}".')


# def main(token, base_url, project_id, project_version_id, csv_path):
#     bd = Client(
#         token=token,
#         base_url=base_url,
#         verify=False  # TLS certificate verification
#     )
#
#     df = pd.read_csv(csv_path, encoding='utf-8')
#     for i, r in df.iterrows():
#         try:
#             manually_add_component(bd, project_id, project_version_id, r['组件 id'],
#                                    r['版本 id'], r['组件来源 id'])
#         except:
#             pass


def main(token, base_url, project_id, project_version_id, file_path):
    bd = Client(
            token=token,
            base_url=base_url,
            verify=False  # TLS certificate verification
        )

    data = json.load(open(file_path, 'r', encoding='utf-8'))
    for it in data['items']:
        if it['origins']:
            for origin in it['origins']:
                url = origin['origin']
                url_split = url.split('/')
                component_id = url_split[-5]
                component_version_id = url_split[-3]
                origin_id = url_split[-1]
                manually_add_component(bd, project_id, project_version_id, component_id, component_version_id,
                                       origin_id)
        elif 'componentVersion' in it:
            url = it['componentVersion']
            url_split = url.split('/')
            component_id = url_split[-3]
            component_version_id = url_split[-1]
            origin_id = None
            manually_add_component(bd, project_id, project_version_id, component_id, component_version_id,
                                   origin_id)
        else:
            url = it['component']
            url_split = url.split('/')
            component_id = url_split[-1]
            component_version_id = None
            origin_id = None
            manually_add_component(bd, project_id, project_version_id, component_id, component_version_id,
                                   origin_id)

        sleep(1)


if __name__ == '__main__':
    token = 'YjczYWYzMTktZTIzYy00YTc0LWI0YzUtMTA5ZjNlMGFmNGJhOjg5MTQ1NzE5LWFlOGMtNDYzNS1iZDEzLWMzNDJiMmUwNWIwYw=='
    base_url = 'https://lobsterapj.app.blackduck.com/'
    project_id = '9a6b9485-508a-424a-8146-5170aa9beca5'
    project_version_id = '55ede173-d010-43a9-9d85-24b7e549f4c0'
    component_id = 'dadd6f50-32a8-4db8-830e-bcc95277767a'
    component_version_id = '281bddc8-687f-4422-8099-00058dad8a56'
    origin_id = '99959da6-77ed-4837-b02a-094ed800f687'

    bd = Client(
            token=token,
            base_url=base_url,
            verify=False  # TLS certificate verification
        )

    manually_add_component(bd, project_id, project_version_id, component_id, component_version_id, )
    # file_path = r'C:\Users\<USER>\Downloads\1.txt'
    # csv_path = r'C:\Projects\Blackduck\Support Cases\2022\ZTE Corporation - 【紧急】HUB服务器任务卡住及报告创建失败\attachment\HUB93-CSV.tar\HUB93-CSV\components_2022-05-31_104001.csv'

    # main(token, base_url, project_id, project_version_id, file_path)
