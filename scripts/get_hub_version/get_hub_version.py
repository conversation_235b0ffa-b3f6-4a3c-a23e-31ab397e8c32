from typing import NamedTuple

import requests
import json
from urllib.parse import urljoin
import urllib3
from concurrent import futures

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class HubServerInfo(NamedTuple):
    url: str
    version: str
    status: str


def get_hub_server_info(base_url):
    url = urljoin(base_url, 'api/current-version')
    try:
        r = requests.get(url, verify=False, timeout=3)
        data = json.loads(r.text)
        return HubServerInfo(base_url, data['version'], 'OK')
    except requests.exceptions.ConnectionError:
        return HubServerInfo(base_url, 'Unknown', 'Down')
    except:
        return HubServerInfo(base_url, 'Unknown', 'Unknown')


def get_hub_server_list(filename):
    server_list = []
    with open(filename, encoding='utf-8') as f:
        for line in f:
            server_list.append(line.strip())
    return server_list


def write_to_file(server_info_list, filename):
    with open(filename, mode='wt', encoding='utf-8') as f:
        for server_info in server_info_list:
            print(f'{server_info.url:70} {server_info.version}')
            print(f'{server_info.url:70} {server_info.version}', file=f)


def main():
    server_list = get_hub_server_list('hub_server_list.txt')
    with futures.ThreadPoolExecutor() as executor:
        res = executor.map(get_hub_server_info, sorted(server_list))
    write_to_file(res, 'hub_server_version.txt')


if __name__ == '__main__':
    main()
