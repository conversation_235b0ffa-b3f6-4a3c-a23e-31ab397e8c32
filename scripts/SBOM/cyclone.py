import json
import sys

from cyclonedx.model.bom import Bom
from cyclonedx.output.json import JsonV1Dot5
from cyclonedx.schema import SchemaVersion
from cyclonedx.validation.json import JsonStrictValidator
from cyclonedx.exception import MissingOptionalDependencyException

if __name__ == '__main__':
    cdx_bom = ''
    with open(r'C:\Users\<USER>\Downloads\SWM_NPort6000-G2-F2E-1.17.5_CYCLONEDX_15_2024-11-20_023324\sbom_2024-11-20_023324.cdx.json', 'r+') as input_json:
        cdx_bom = Bom.from_json(data=json.loads(input_json.read()))

    my_json_outputter = JsonV1Dot5(cdx_bom)
    serialized_json = my_json_outputter.output_as_string(indent=4)

    my_json_validator = JsonStrictValidator(SchemaVersion.V1_5)
    try:
        validation_errors = my_json_validator.validate_str(serialized_json)
        if validation_errors:
            print('CycloneDX schema invalid', 'ValidationError:', repr(
                validation_errors), sep='\n', file=sys.stderr)
            sys.exit(2)
        print('CycloneDX schema valid')
        my_json_outputter.output_to_file(
            filename='test.json', allow_overwrite=True, indent=4)
    except MissingOptionalDependencyException as error:
        print('CycloneDX schema JSON-validation was skipped due to', error)
