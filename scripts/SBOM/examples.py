from cyclonedx.model.bom import Bom
from cyclonedx.model.component import Component, ComponentType
from cyclonedx.output.json import JsonV1Dot5

bom = Bom()
bom.metadata.component = root_component = Component(
    name='myApp',
    type=ComponentType.APPLICATION,
    bom_ref="myApp"
)

component1 = Component(
    type=ComponentType.LIBRARY,
    name='some-component',
    bom_ref="some-component"
)
bom.components.add(component1)
bom.register_dependency(root_component, [component1])

component2 = Component(
    type=ComponentType.LIBRARY,
    name='some-library',
    bom_ref="some-library1"
)
bom.components.add(component2)
bom.register_dependency(component1, [component2])

component3 = Component(
    type=ComponentType.LIBRARY,
    name='some-library',
    bom_ref="some-library2"
)
bom.components.add(component3)
bom.register_dependency(component1, [component3])

print(JsonV1Dot5(bom).output_as_string(indent=2))