#!/bin/bash
while true
do
    echo "$(date) - Testing proxy connection..."
    
    # First test: Connection to proxy
    echo "Testing connection to proxy..."
    curl -m 5 \
         -x proxy.bmwbrill.cn:8080 \
         -U username:password \
         -v \
         http://example.com \
         -o /dev/null \
         2>&1 | grep "Connected to proxy"
    
    if [ $? -ne 0 ]; then
        echo "Failed to connect to proxy server"
    else
        # Second test: Connection through proxy to KB server
        echo "Testing connection to KB server through proxy..."
        curl -m 5 \
             -x proxy.bmwbrill.cn:8080 \
             -U username:password \
             -H 'Accept-Language: en' \
             -H 'Accept: application/vnd.blackducksoftware.kb-authenticate-1+json' \
             -H 'Authorization: bdsLicenseKey test_license_key' \
             -H 'Cache-Control: no-cache, no-store, must-revalidate' \
             -H 'Pragma: no-cache' \
             -H 'Expires: 0' \
             -H 'Connection: Keep-Alive' \
             -H 'User-Agent: Hub/2024.1.1 (Linux 4.4.0-1128-aws amd64)' \
             -X POST \
             -H 'Content-Length: 0' \
             -v \
             https://kb.blackducksoftware.com/api/authenticate \
             -o /dev/null \
             2>&1
    fi
    
    sleep 0.3
done