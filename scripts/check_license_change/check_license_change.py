import json
import time

from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)


if __name__ == '__main__':
    bd = Client(
        token='OGM1MjQ1ZDYtMTBmZC00MTk2LWJmMGEtMzAyMzQwZDUxZDBlOjgyM2RjNTYzLTI3MzItNDRhMy1hY2ExLTVjMjFkODQxODQyMg==',
        base_url='https://test-yuan.app.blackduck.com',
        verify=False  # TLS certificate verification
    )

    params = {
        'limit': 99999,
    }

    verified_licenses = ['SSLeay License', 'OpenSSL License']

    api = '/api/projects/546073f7-bd2f-4112-b60a-7261d68b62f0/versions/60efc753-4d62-440a-9cf3-168233aeb55b/components'

    while True:
        r = bd.session.get(api, params=params)
        data = json.loads(r.text)
        current_licenses = [lic['licenseDisplay'] for lic in data['items'][0]['licenses'][0]['licenses']]
        if current_licenses != verified_licenses:
            logging.info(f'Current licenses are {current_licenses}')
            break
        time.sleep(5)
