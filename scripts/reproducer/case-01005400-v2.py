"""

"""

import json
from concurrent import futures
from blackduck import Client


def add_component(result):
    post_url = url.format(result[0], result[1])
    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }
    r = bd.session.post(post_url, headers=headers, data=json.dumps(payload))
    if r.status_code == 200:
        print(f'The component version was added successfully.')
    else:
        print(f'Failed to add the component version. The status code is {r.status_code}. The returned text is "{r.text}".')
    return r


if __name__ == '__main__':
    base_url = 'https://test-yuan.app.blackduck.com/'
    token = 'OGM1MjQ1ZDYtMTBmZC00MTk2LWJmMGEtMzAyMzQwZDUxZDBlOjgyM2RjNTYzLTI3MzItNDRhMy1hY2ExLTVjMjFkODQxODQyMg=='

    # Create a project version then update the project_id and version_id accordingly
    project_id = '45c7186b-136a-4923-96ed-c3634656b832'

    # component: micro-ecc version:
    component_id = '64487289-ca5d-4aa6-bab3-a51ab8b357bf'
    component_version_id = 'c8b945f1-f5a8-480c-ba14-91a41746f6f5'

    payload = {
        'component': f'{base_url.rstrip("/")}/api/components/{component_id}/versions/{component_version_id}'
    }

    bd = Client(
        token=token,
        base_url=base_url,
        verify=False  # TLS certificate verification
    )

    # url = f'{base_url.rstrip("/")}/api/projects/{project_id}/versions/{project_version_id}/components/' \
    #       f'{component_id}/versions/{component_version_id}/origins/{origin_id}/vulnerabilities/{{}}/remediation'

    url = f'{base_url.rstrip("/")}/api/projects/{{}}/versions/{{}}/components'
    project_id = ['45c7186b-136a-4923-96ed-c3634656b832', '12f8a37a-abb8-463e-864b-7c6fd1489257']
    project_version_id_list_1 = ['e72bb249-66e3-42a7-8f4c-fe134d66c308', '9f6a7da7-763a-43e2-a474-89f0d0e602a5']
    project_version_id_list_2 = ['6cc4b081-90e1-455f-9343-4542e0cf6842', 'adae60e5-d1a3-4732-9393-aa69a6482170']
    result = [(p_id, p_ver) for p_id, p_ver_list in
              zip(project_id, [project_version_id_list_1, project_version_id_list_2]) for p_ver in p_ver_list]
    print(result)

    with futures.ThreadPoolExecutor() as executor:
        res = executor.map(add_component, result)
