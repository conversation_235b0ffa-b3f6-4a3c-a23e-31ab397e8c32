"""
A reproducer script for case 01005400 Wrong Alert notification upon incorrect version
Requirement for running this script:
1. Python 3.x and install blackduck module by running `pip install blackduck`;
2. Create a project named "QTS" and create two versions, namely "5.0.1" and "5.1.0";
3. Update the server's `base_url`, `token`, `project_id` and `project_version_id_list` accordingly;
4. Run this script
"""

import json
import logging
import requests
from concurrent import futures
from blackduck import Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def add_component(bd, base_url, project_id, project_version_id, component_id, component_version_id):
    url = f'{base_url.rstrip("/")}/api/projects/{project_id}/versions/{project_version_id}/components'
    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }
    payload = {
        'component': f'{base_url.rstrip("/")}/api/components/{component_id}/versions/{component_version_id}'
    }
    try:
        r = bd.session.post(url, headers=headers, data=json.dumps(payload))
        r.raise_for_status()
        logger.info('The component version was added successfully.')
    except requests.exceptions.HTTPError as errh:
        logger.error(f"HTTP Error: {errh}")
    except requests.exceptions.ConnectionError as errc:
        logger.error(f"Error Connecting: {errc}")
    except requests.exceptions.Timeout as errt:
        logger.error(f"Timeout Error: {errt}")
    except requests.exceptions.RequestException as err:
        logger.error(f"Oops: Something Else {err}")


if __name__ == '__main__':
    base_url = '<replace_it_with_HUB_url>'
    token = '<replace_it_with_HUB_token>'
    project_id = '<replace_it_with_project_id_for_QTS>'
    project_version_id_list = ['<replace_it_with_project_version_id_for_5.0.1>', '<replace_it_with_project_version_id_for_5.1.0>']
    component_id = '64487289-ca5d-4aa6-bab3-a51ab8b357bf'
    component_version_id = 'c8b945f1-f5a8-480c-ba14-91a41746f6f5'

    bd = Client(
        token=token,
        base_url=base_url,
        verify=False  # TLS certificate verification
    )

    with futures.ThreadPoolExecutor() as executor:
        for project_version_id in project_version_id_list:
            executor.submit(add_component, bd, base_url, project_id, project_version_id, component_id, component_version_id)
