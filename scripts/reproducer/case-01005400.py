"""
A reproducer script for case 01005400 Wrong Alert notification upon incorrect version
Requirement for running this script:
1. Python 3.x and install blackduck module by running `pip install blackduck`;
2. Create a project named "QTS" and create two versions, namely "5.0.1" and "5.1.0";
3. Update the server's `base_url`, `token`, `project_id` and `project_version_id_list` accordingly;
4. Run this script
"""

import json
from concurrent import futures
from blackduck import Client


def add_component(project_version_id):
    post_url = url.format(project_version_id)
    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }
    r = bd.session.post(post_url, headers=headers, data=json.dumps(payload))
    if r.status_code == 200:
        print(f'The component version was added successfully.')
    else:
        print(f'Failed to add the component version. The status code is {r.status_code}. The returned text is "{r.text}".')
    return r


if __name__ == '__main__':
    base_url = '<replace_it_with_HUB_url>'
    token = '<replace_it_with_HUB_token>'

    # Create a project version then update the project_id and version_id accordingly
    project_id = '<replace_it_with_project_id_for_QTS>'
    project_version_id_list = ['<replace_it_with_project_version_id_for_5.0.1>', '<replace_it_with_project_version_id_for_5.1.0>']

    # component: micro-ecc version: v1.0
    component_id = '64487289-ca5d-4aa6-bab3-a51ab8b357bf'
    component_version_id = 'c8b945f1-f5a8-480c-ba14-91a41746f6f5'

    payload = {
        'component': f'{base_url.rstrip("/")}/api/components/{component_id}/versions/{component_version_id}'
    }

    bd = Client(
        token=token,
        base_url=base_url,
        verify=False  # TLS certificate verification
    )

    url = f'{base_url.rstrip("/")}/api/projects/{project_id}/versions/{{}}/components'

    with futures.ThreadPoolExecutor() as executor:
        res = executor.map(add_component, project_version_id_list)
