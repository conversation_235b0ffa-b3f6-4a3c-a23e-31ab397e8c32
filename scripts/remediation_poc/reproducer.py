"""
A reproducer script for HUB-36158 & HUB-36387 Remediation status not updating riskPriorityDistribution
Requirement for running this script:
1. Python 3.x and install blackduck module by running `pip install blackduck`;
2. Use an existing project version or create a new one and update the `project_id` and `project_version_id` accordingly;
3. Update the server's `base_url` and `token`;
4. Manually add the following component to the BOM:
component: vertx-web
version: 3.3.2
origin: maven: io.vertx:vertx-web:3.3.2
5. Run this script
"""

import json
from concurrent import futures
from blackduck import Client


def ignore_vul(vul_id):
    put_url = url.format(vul_id)
    headers = {
        'content-type': 'application/vnd.blackducksoftware.internal-1+json'
    }
    payload = {
        'id': vul_id,
        'comment': 'updated comment',
        'remediationStatus': 'IGNORED'
    }
    r = bd.session.put(put_url, headers=headers, data=json.dumps(payload))
    if r.status_code == 200:
        print(f'{vul_id} was ignored successfully.')
    else:
        print(f'Failed to ignore {vul_id}. The status code is {r.status_code}. The returned text is "{r.text}".')
    return r


if __name__ == '__main__':
    base_url = 'https://test-yuan.app.blackduck.com/'
    token = 'OGM1MjQ1ZDYtMTBmZC00MTk2LWJmMGEtMzAyMzQwZDUxZDBlOjgyM2RjNTYzLTI3MzItNDRhMy1hY2ExLTVjMjFkODQxODQyMg=='

    # Create a project version then update the project_id and version_id accordingly
    project_id = 'bacabf58-7eac-4da2-ab87-273b73e1629b'
    project_version_id = 'db7540b3-54ba-4553-9575-65f31e8f9f14'

    # vertx-web 3.3.2 maven: io.vertx:vertx-web:3.3.2
    component_id = '9f2be1b6-9704-402f-a25f-54401c69ac84'
    component_version_id = 'a5d99214-7c75-41fc-be78-5fe5dd41b209'
    origin_id = 'f4e564b6-b50f-425f-a9bd-c342f2a5caf2'
    vul_id_list = ['CVE-2018-12542', 'CVE-2018-12540']

    bd = Client(
        token=token,
        base_url=base_url,
        verify=False  # TLS certificate verification
    )

    url = f'{base_url.rstrip("/")}/api/projects/{project_id}/versions/{project_version_id}/components/' \
          f'{component_id}/versions/{component_version_id}/origins/{origin_id}/vulnerabilities/{{}}/remediation'

    with futures.ThreadPoolExecutor() as executor:
        res = executor.map(ignore_vul, vul_id_list)
