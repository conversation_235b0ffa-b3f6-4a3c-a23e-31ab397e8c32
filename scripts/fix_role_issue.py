import json
from blackduck.HubRestApi import HubInstance

# Note: change below settings in accord with your server
username = 'sysadmin'
password = 'blackduck'
urlbase = 'https://sup-cn-hub10.dc1.lan/'

hub = HubInstance(urlbase, username, password, insecure=True)
url_base = hub.get_urlbase()

user_api_url = url_base + '/api/users?limit=999999&filter=userStatus:true'
roles_api_url = url_base + '/api/users/{}/roles?filter=scope:server'
assign_roles_api_url = url_base + '/api/users/{}/roles'
all_roles_api_url = url_base + '/api/roles'


def get_roles_mapping():
    r = hub.execute_get(all_roles_api_url)
    data = json.loads(r.text)
    roles_dict = {}
    for it in data['items']:
        roles_dict[it['name']] = it['_meta']['href']
    return roles_dict


def run():
    # Get all user information
    r = hub.execute_get(user_api_url)
    data = json.loads(r.text)
    roles_dict = get_roles_mapping()
    print(f'{data["totalCount"]} users to check.')
    for it in data['items']:
        user_roles = [role['name'] for role in it['roles']]
        user_id = it['_meta']['href'].split('/')[-1]
        user_name = it['userName']
        r = hub.execute_get(roles_api_url.format(user_id))
        # Use role API to get verified role assignment
        verified_data = json.loads(r.text)
        verified_user_roles = [role['name'] for role in verified_data['items']]
        # Get roles displayed on the user management page ui/admin/users but
        # not on the drill down page ui/admin/users/{user_id}
        missing_roles = set(user_roles) - set(verified_user_roles)
        if missing_roles:
            print(f'User {user_name} misses below roles: '
                  f'{", ".join(missing_roles)}')
            for role in missing_roles:
                pay_load = {
                    'name': role,
                    'role': roles_dict[role]
                }
                r = hub.execute_post(assign_roles_api_url.format(user_id),
                                     data=pay_load)
                if r.status_code == 201:
                    print(f'{"  -"} Assigned role "{role}" to user {user_name}')
                else:
                    print(f'{"  -"} Failed to assign role "{role}" to user '
                          f'{user_name} with status code {r.status_code}')


if __name__ == '__main__':
    run()
