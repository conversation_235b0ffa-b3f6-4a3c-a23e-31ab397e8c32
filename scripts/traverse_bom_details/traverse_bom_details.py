from blackduck import Client
import logging
import sys

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='NTU5NTBmNTUtNjdkYi00ZDE4LTliMTAtNzBkMjBjZWQ5MjRjOmM3NmE1YWM0LTFhZDktNDBlZC1hYjE5LWIxNGE4MDU3Mzc0NA==',
    base_url='https://lobsterapj.app.blackduck.com/',
    verify=False  # TLS certificate verification
)

for project in bd.get_resource('projects'):
    for project_version in bd.get_resource('versions', parent=project):
        # print(project_version)
        url = project_version['_meta']['href'] + '/components'
        data = bd.get_json(url)
        for component in data['items']:
            for origin in component['origins']:
                external_namespace = origin['externalNamespace']
                print(f'当前externalNamespace: {external_namespace}')
                if external_namespace.upper() == 'GOLANG':
                    print(f'找到externalNamespace为GOLANG的组件: {component["origin"]}')
                    sys.exit(0)

        # if data['status'] != 'UP_TO_DATE':
        #     print(f'{url} : bom status {data["status"]}')