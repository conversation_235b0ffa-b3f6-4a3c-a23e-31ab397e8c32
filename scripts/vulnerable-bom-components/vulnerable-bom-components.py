from blackduck import Client
import logging
import json

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='OGM1MjQ1ZDYtMTBmZC00MTk2LWJmMGEtMzAyMzQwZDUxZDBlOjgyM2RjNTYzLTI3MzItNDRhMy1hY2ExLTVjMjFkODQxODQyMg==',
    base_url='https://test-yuan.app.blackduck.com/',
    verify=False  # TLS certificate verification
)

url = 'https://test-yuan.app.blackduck.com/api/projects/c80f09a0-adcc-40c6-bffc-a28cc25dd3a1/versions/8ecbc70e-9864-4621-b7f1-aea0e4319902/vulnerable-bom-components?limit=1000&filter=ignored:false'
bom_url = 'https://test-yuan.app.blackduck.com/api/projects/c80f09a0-adcc-40c6-bffc-a28cc25dd3a1/versions/8ecbc70e-9864-4621-b7f1-aea0e4319902/vulnerability-bom?limit=100'


data = bd.get_json(url)

origins_set = set()

for it in data['items']:
    origins_set.add(it['_meta']['links'][1]['href'])

# origins_set = set(i.rstrip('/vulnerabilities') for i in origins_set)
origins_set = set(i[:-16] for i in origins_set)

print(len(origins_set))

headers = {
    'content-type': 'application/vnd.blackducksoftware.internal-1+json'
}

data_2 = bd.get_json(bom_url, headers=headers)

origins_set_2 = set(it['componentVersionOrigin'].replace('origins', 'origin') for it in data_2['items'])

# print(origins_set_2)

# print(len(origins_set_2))

for i in origins_set:
    if i not in origins_set_2:
        print(i)


# print(len(origins_set))


