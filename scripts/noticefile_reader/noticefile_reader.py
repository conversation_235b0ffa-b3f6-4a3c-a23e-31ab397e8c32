"""
Get origins from a notice file
"""
import json
import re
from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    # token='OGM1MjQ1ZDYtMTBmZC00MTk2LWJmMGEtMzAyMzQwZDUxZDBlOjgyM2RjNTYzLTI3MzItNDRhMy1hY2ExLTVjMjFkODQxODQyMg==',
    # base_url='https://test-yuan.app.blackduck.com/',
    token='OGM1MjQ1ZDYtMTBmZC00MTk2LWJmMGEtMzAyMzQwZDUxZDBlOjgyM2RjNTYzLTI3MzItNDRhMy1hY2ExLTVjMjFkODQxODQyMg==',
    base_url='https://test-yuan.app.blackduck.com/',
    verify=False  # TLS certificate verification
)


def get_component_from_notice_file(fp):
    copyright_start_line = 'Copyright Text: '
    copyright_end_line = 'Licenses: '
    if_copyright = False
    component_list = []
    with open(fp, encoding='utf-8') as f:
        for line in f:
            if line.startswith(copyright_start_line):
                if_copyright = True
                continue
            if line.startswith(copyright_end_line):
                if_copyright = False
                break
            if if_copyright and not re.match(r'\s', line):
                parsed_line = line.strip()[::-1].split(' ')
                origin_name = parsed_line[0][::-1]
                version_name = parsed_line[1][::-1]
                component_name = ' '.join(parsed_line[2:])[::-1]
                # print(component_name, version_name, origin_name)
                component_list.append((component_name, version_name, origin_name))
    return component_list


def get_component_url_from_origin_name(origin_name):
    api = f'/api/components?q={origin_name}'
    r = bd.session.get(api)
    print(r.text)
    return json.loads(r.text)['items'][0]['variant']


def add_component_to_bom(project_id, project_version_id, component_url):
    payload = {
        'component': component_url
    }

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }

    add_component_api = f'/api/projects/{project_id}/versions/' \
                        f'{project_version_id}/components'

    r = bd.session.post(add_component_api, data=json.dumps(payload),
                        headers=headers)
    if r.status_code == 200:
        logging.info('The Component was added successfully.')
    else:
        logging.error(f'Failed to add the component. The status code is '
                      f'{r.status_code}. The returned text is "{r.text}".')


if __name__ == '__main__':
    # get_origins_from_notice_file(r'C:\Projects\Blackduck\Support Cases\2021\Jan\Guangdong OPPO Mobile Telecommunications Corp Ltd - 黑鸭扫描误匹配等case合集  notice文本末端未对齐，有乱码\attachment\4-notice\clienthome-master_2021-12-17_063008\version-license_2021-12-17_063008.txt')
    # print(get_bom_link_from_origin_name('npmjs:@hap-toolkit/runtime/0.7.7-beta.0'))
    s = ['android:platform-frameworks-base:android-cts-8.0_r1',
         'maven:com.facebook.fresco:fbcore:1.11.0',
         'debian:firefox/55.0~b2-1']
    project_id = '4dd4b8f0-2c09-4bcc-a34b-34633e090eb6'
    project_version_id = '4193b4d8-1bc2-4997-86bd-7d492f8761c8'
    for origin_name in s:
        component_url = get_component_url_from_origin_name(origin_name)
        add_component_to_bom(project_id, project_version_id, component_url)
