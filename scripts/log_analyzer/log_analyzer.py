import json
import os
import re

import pandas as pd
from pandas import DataFrame



class LogAnalyzer:
    def __init__(self, log_path):
        self.log_path = log_path
        self.hub_version_search_str = 'blackduck.product.version='
        self.scan_search_str = 'is now associated with scan "{}"'
        self.scan_status_search_str = 'ScanId {} updated to status'
        self.matchengine_scan_search_str = 'name={}'
        self.uuid_search_pattern = re.compile('[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')
        self.datetime_search_pattern = re.compile('\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}.+?\[')

    def get_hub_version(self) -> str:
        """
        Get hub version information from debug folder
        :return: hub version
        """
        debug_log_path = os.path.join(self.log_path, 'debug')
        try:
            sysinfo_fn = [fn for fn in os.listdir(debug_log_path)
                          if fn.startswith('sysinfo')][0]
        except IndexError:
            raise FileNotFoundError(f'Cannot find sysinfo file under debug '
                                    f'folder {debug_log_path}')
        sysinfo_path = os.path.join(debug_log_path, sysinfo_fn)
        with open(sysinfo_path, encoding='utf-8') as f:
            for line in f:
                if line.startswith(self.hub_version_search_str):
                    return line.split('=')[-1]
            else:
                print(f'Unable to get the hub version from sysinfo file '
                      f'{sysinfo_path}')

    def scan_id_2_scan_name(self, scan_id):
        """
        Get scan name for a given scan id in hub-scan container logs
        ====================================
        [bff4c1a8e8da] 2022-04-12 03:21:07,852Z[GMT] [scan-upload-2] INFO  com.blackducksoftware.scan.siggen.impl.BlackDuckInputOutputService - Document [106df5e3-0a55-4c41-b8aa-47214a84a1e7] is now associated with scan "board-x86-latest scan" [106df5e3-0a55-4c41-b8aa-47214a84a1e7] in code location [bbfdd4fd-47c4-4d3e-be4a-72e91155f248]
        ===================================
        :param scan_id: scan id
        :return: scan name
        """
        scan_log_path = os.path.join(self.log_path, 'hub-scan', 'app-log')
        log_list = os.listdir(scan_log_path)
        pattern = f'scan "(.+)" \[{scan_id}\]'
        for log in log_list:
            with open(os.path.join(scan_log_path, log)) as f:
                for line in f:
                    if scan_id in line:
                        scan_name = re.findall(pattern, line)
                        if scan_name:
                            date_time = re.findall(self.datetime_search_pattern, line)
                            return date_time[0].strip('['), scan_name[0]

    def get_scan_info(self, scan_name):
        """
        Get scan id and code location id in hub-scan container logs
        ====================================
        [bff4c1a8e8da] 2022-04-12 03:21:07,852Z[GMT] [scan-upload-2] INFO  com.blackducksoftware.scan.siggen.impl.BlackDuckInputOutputService - Document [106df5e3-0a55-4c41-b8aa-47214a84a1e7] is now associated with scan "board-x86-latest scan" [106df5e3-0a55-4c41-b8aa-47214a84a1e7] in code location [bbfdd4fd-47c4-4d3e-be4a-72e91155f248]
        ===================================
        :param scan_name: scan name
        :return:
        """
        scan_log_path = os.path.join(self.log_path, 'hub-scan', 'app-log')
        log_list = os.listdir(scan_log_path)
        scan_info_list = []
        columns = ['scan_name', 'scan_id', 'codelocation_id', 'date_time',
                   'scan_type']
        for log in log_list:
            with open(os.path.join(scan_log_path, log), encoding='utf-8') as f:
                for line in f:
                    if self.scan_search_str.format(scan_name) in line:
                        date_time = re.findall(self.datetime_search_pattern, line)
                        uuid_list = re.findall(self.uuid_search_pattern, line)
                        scan_type = None  # TODO: fix this
                        scan_info_list.append(
                            (scan_name, uuid_list[-2], uuid_list[-1],
                             date_time[0].strip('['), scan_type))
        hub_version = self.get_hub_version()
        if int(hub_version.split('.')[0]) >= 2022:
            # From 2022 version on, the signature scan can only be found in the
            # match engine logs
            matchengine_log_path = os.path.join(
                self.log_path, 'blackduck-matchengine', 'app-log')
            matchengine_log_list = os.listdir(matchengine_log_path)
            for log in matchengine_log_list:
                with open(os.path.join(matchengine_log_path, log), encoding='utf-8') as f:
                    for line in f:
                        if self.matchengine_scan_search_str.format(scan_name) in line:
                            scan_info = re.findall('Scan{(.+)}', line)[0]
                            # Convert the str to dict
                            scan_info = {i.split('=')[0]: i.split('=')[1] for
                                         i in scan_info.split(', ')}
                            date_time = scan_info['createdOn']
                            scan_id = scan_info['id']
                            codelocation_id = scan_info['codeLocationId']
                            scan_type = scan_info['scanType']
                            scan_info_list.append(
                                (scan_name, scan_id, codelocation_id,
                                 date_time, scan_type))

        df = DataFrame.from_records(scan_info_list, columns=columns)
        df['date_time'] = pd.to_datetime(df['date_time'])
        df = df.sort_values(by=['date_time'])
        return df

    def get_scan_status(self, scan_id):
        """
        Get scan status from jobrunner and bomengine
        # TODO: might need to add matchengine for 2022.2.x as "Approximately
        #       50% of the previous Jobrunner resources will be moved over to
        #       match-engine
        :param scan_id: scan id
        :return:
        """
        jobrunner_log_path = os.path.join(self.log_path, 'hub-jobrunner', 'app-log')
        bomengine_log_path = os.path.join(self.log_path, 'blackduck-bomengine', 'app-log')
        scan_status_list = []
        columns = ['scan_id', 'status', 'date_time']
        jobrunner_log_list = os.listdir(jobrunner_log_path)
        bomengine_log_list = os.listdir(bomengine_log_path)
        for log in jobrunner_log_list:
            with open(os.path.join(jobrunner_log_path, log), encoding='utf-8') as f:
                for line in f:
                    if self.scan_status_search_str.format(scan_id) in line:
                        date_time = re.findall(self.datetime_search_pattern, line)
                        scan_status = re.findall('updated to status (\w+) ', line)
                        scan_status_list.append(
                            (scan_id, scan_status[0], date_time[0].strip('[')))
        for log in bomengine_log_list:
            with open(os.path.join(bomengine_log_path, log), encoding='utf-8') as f:
                for line in f:
                    if self.scan_status_search_str.format(scan_id) in line:
                        date_time = re.findall(self.datetime_search_pattern, line)
                        scan_status = re.findall('updated to status (\w+) ', line)
                        scan_status_list.append(
                            (scan_id, scan_status[0], date_time[0].strip('[')))
        df = DataFrame.from_records(scan_status_list, columns=columns)
        df['date_time'] = pd.to_datetime(df['date_time'])
        df = df.sort_values(by=['date_time'])
        return df

    def analyze_codelocation_summary_json(self, scan_name):
        json_fp = os.path.join(self.log_path, 'scansummary', 'codelocation-summary.json')
        data = json.load(open(json_fp, encoding='utf-8'))
        for it in data:
            if it['codeLocation']['name'] == scan_name:
                project_name = it['releaseSummary']['project']['name']
                project_version_name = it['releaseSummary']['version']['version']
                for status in it['status']:
                    if status['codeLocationStatusId']['operationName'] == 'SnippetScanning':
                        scan_status = status['status']
                        update_dt = status['updatedAt']
                return project_name, project_version_name, scan_status, update_dt
        return None, None, None, None

if __name__ == '__main__':
    pd.set_option('display.max_columns', 500)
    pd.set_option('display.max_rows', 500)
    pd.set_option('mode.chained_assignment', None)
    log_path = r"C:\Users\<USER>\Downloads\192_bds_logs-20250512T065443"
    log_analyzer = LogAnalyzer(log_path)
    print(log_analyzer.get_hub_version())
    print(log_analyzer.get_scan_info('PIU_CM:Src/PIU/PIU_CM:renault_piu/BL4.0_CM/04.00.04.13250411_02 signature'))
    print(log_analyzer.get_scan_status('998d7f4f-58aa-42fd-bc83-e194c04ca429'))
    # print(log_analyzer.get_scan_info('DAIP_HUB_Volsker_V20.23.40.04_gBR0nYro signature'))
    # print(log_analyzer.get_scan_status('efd93fed-db8e-4636-9b79-0b8d6136dce3'))
    # print(log_analyzer.get_scan_status('c83818c3-cdb4-4503-989b-fafa12bd004b'))
    # print(log_analyzer.get_scan_status('6968c5a0-3690-40fd-bcde-7b8ee3397600'))
    # log_path = r"C:\Projects\Blackduck\Support Cases\2022\Guangdong OPPO Mobile Telecommunications Corp Ltd - 黑鸭片段扫描耗时过长\test\us1a-sup-cn-hub04_bds_logs-20221012T112630"
    # log_analyzer = LogAnalyzer(log_path)
    # print(log_analyzer.get_hub_version())
    # print(log_analyzer.get_scan_info('flask/test_snippet_duration_flask/1.0 scan'))
    # print(log_analyzer.get_scan_status('d44c65d1-57f4-45d2-a29f-cc53198f097a'))
    # scan_id_list = ['8d0d8ad8-29d6-4bcb-8d7a-c289a6f83b3e', '4c638c51-6c3e-4173-8d35-af93a8283e4e', 'a736615f-c9e7-4915-a9d3-69623d970fa0', '8e792c37-4e42-4670-9acd-51cca26116c0', '7cb74cdc-2c27-466e-9d5e-4bc5fc9aab42', 'd41943e8-5f06-4f8a-8971-a29902b6186b', 'b76825f9-2f25-411a-8353-ae71cbaa9f1e', '711dc7ef-c95b-4998-869a-8a064224bb8d', 'b6bf0245-4ac7-4e11-a601-92f2508d18d2', 'c7a72212-9ed2-4e13-9bd3-989706b8d86c', '32aba70b-ed5e-446f-9f14-f9e3782755ca', '452414ce-703b-4dce-b0ef-275a5680485b', '770a08a0-19e9-49c9-a9e8-7a8d1843c50b', 'a2fd9b6b-cc7e-41aa-9c4e-106a3892dd2e', '049cfbe2-83ea-4fd8-8459-9c58fe46129a', '1095a700-ec18-41c0-9936-fa780d081209', '97b1c2df-5e0c-4a57-a803-1df99dbfcce1', '4ee6432a-de4a-4161-8e33-a13e91fba506', '5c22f2e8-9230-4f0c-b863-c6fe7ff534d2', '5d51c227-1b4b-4ca2-9bca-18f6139b776c', 'a5e2e724-235b-4060-8ecd-51c71a3ec088', '5de0818c-9c1d-4491-bf77-ebd6b3a27c3e', '782ce427-4b5a-47f5-a878-3f4e920f36ae', 'e9a81346-ee3e-4805-a533-be19fb795037', '17aea32f-91be-4043-b119-5129cdacb4a6', '18dd7425-56df-4bcb-9e3d-e40f2bece4b8', 'e8413291-a6d8-4342-b6ed-ec989bfedaa4', 'fea41b40-aec2-472d-9fef-2abaf4def904', 'ebc80862-30ed-425d-8c14-e9bee968dcb5', '3402f9c9-f6d5-4cd4-a2ae-8ad3d24702a5', '95de69e5-0e39-4af8-bac7-ddee50686c7d', '42fa742d-3f1f-48b8-8fb2-8ebb82ce3d64', 'c0818e26-4de9-4e00-965b-3ae0d1d7f9ca', 'faaf198c-8b33-41d6-9ad3-74244b0e2ed2', '98fa69ce-d89e-4810-9a69-f6b78a747f00', '70b525b9-b6d9-4459-a403-9622567ee3b1', 'd431cae2-b94a-4609-a3e5-1946ceaeaeb8', '6bad97d3-f8d1-49ec-b1b0-4e5e257219bd', '1f4ad1e8-a408-423e-9fd2-a2b7569015fc', '42a3f29d-d904-4c2a-bca3-843a5d57dd69', '328f46fc-47a4-4e73-8137-94c9c64abee9', '9f7f2eaa-8728-47b9-b524-a15c13ec7733', '421cff5e-f7fe-408c-9ad0-4ab9bc477471', '587e1323-5475-4fee-a803-a83a0c79a157', 'c18db6db-9726-4bbb-94a4-d04d158341e1', 'c968f406-783f-41b2-8af6-db1ce84cddea', '44c5397e-c0aa-48c1-a886-ccb0a270e56d', '3071034d-eae4-4bae-8b4a-c2bf33c88313', '59265cb7-db65-4c24-b17c-bc6787332dcc', '0573b78a-6f86-401c-a3a0-5e9438817db1', 'fef3b40c-dc59-45cb-b5f6-b6442a84b2d0', '55e515f2-92e0-4ce8-b8ee-330b9928335a',]
    # for scan_id in scan_id_list:
    #     dt, scan_name = log_analyzer.scan_id_2_scan_name(scan_id)
    #     print(dt, scan_id, scan_name, *log_analyzer.analyze_codelocation_summary_json(scan_name))

