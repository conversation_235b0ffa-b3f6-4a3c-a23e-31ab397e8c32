______     _            _
|  _  \   | |          | |
| | | |___| |_ ___  ___| |_
| | | / _ \ __/ _ \/ __| __|
| |/ /  __/ ||  __/ (__| |_
|___/ \___|\__\___|\___|\__|

2021-06-28 05:19:43 EDT DEBUG [main] --- Initializing detect.
2021-06-28 05:19:43 EDT DEBUG [main] --- You seem to be running in a LINUX operating system.
2021-06-28 05:19:43 EDT DEBUG [main] --- Detect boot begin.

Detect Version: 6.9.1

2021-06-28 05:19:43 EDT DEBUG [main] --- Configuration processed completely.
2021-06-28 05:19:43 EDT INFO  [main] --- 
2021-06-28 05:19:43 EDT INFO  [main] --- Current property values:
2021-06-28 05:19:43 EDT INFO  [main] --- --property = value [notes]
2021-06-28 05:19:43 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:19:43 EDT INFO  [main] --- blackduck.password = **************** [cmd] 	 *** DEPRECATED ***
2021-06-28 05:19:43 EDT INFO  [main] --- blackduck.trust.cert = true [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- blackduck.url = https://test-yuan.app.blackduck.com/ [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- blackduck.username = sysadmin [cmd] 	 *** DEPRECATED ***
2021-06-28 05:19:43 EDT INFO  [main] --- detect.blackduck.signature.scanner.snippet.matching = SNIPPET_MATCHING [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- detect.blackduck.signature.scanner.upload.source.mode = true [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- detect.code.location.name = api_100 [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- detect.project.name = project_100 [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- detect.project.version.name = project_100_version [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- detect.timeout = 600 [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- detect.wait.for.results = true [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- logging.level.com.synopsys.integration = DEBUG [cmd] 
2021-06-28 05:19:43 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:19:43 EDT INFO  [main] --- 
2021-06-28 05:19:43 EDT DEBUG [main] --- Initializing Detect.
2021-06-28 05:19:44 EDT INFO  [main] --- Tilde's will be automatically resolved to USER HOME.
2021-06-28 05:19:44 EDT INFO  [main] --- Source directory: /home/<USER>/scripts/code
2021-06-28 05:19:44 EDT INFO  [main] --- Output directory: /home/<USER>/blackduck
2021-06-28 05:19:44 EDT INFO  [main] --- Run directory: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848
2021-06-28 05:19:44 EDT DEBUG [main] --- Main boot completed. Deciding what Detect should do.
2021-06-28 05:19:44 EDT INFO  [main] --- 
2021-06-28 05:19:44 EDT DEBUG [main] --- Black Duck will run: A Black Duck url was found.
2021-06-28 05:19:44 EDT DEBUG [main] --- Polaris will NOT run: The Polaris url must be provided.
2021-06-28 05:19:44 EDT DEBUG [main] --- Decided what products will be run. Starting product boot.
2021-06-28 05:19:44 EDT DEBUG [main] --- Detect product boot start.
2021-06-28 05:19:44 EDT DEBUG [main] --- Will boot Black Duck product.
2021-06-28 05:19:44 EDT DEBUG [main] --- Detect will check communication with the Black Duck server.
2021-06-28 05:19:44 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:19:45 EDT INFO  [main] --- A successful connection was made.
2021-06-28 05:19:45 EDT INFO  [main] --- Connection to the Black Duck server was successful.
2021-06-28 05:19:45 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:19:45 EDT INFO  [main] --- Successfully connected to Black Duck (version 2021.2.1)!
2021-06-28 05:19:45 EDT DEBUG [main] --- Connected as: sysadmin
2021-06-28 05:19:49 EDT DEBUG [main] --- Roles: System Administrator, Global Code Scanner, Policy Manager, License Manager, Project Manager, Project Viewer, Project Creator, Super User, Global Project Viewer, Copyright Editor, Component Manager, Global Security Manager
2021-06-28 05:19:49 EDT DEBUG [main] --- Group: 
2021-06-28 05:19:49 EDT DEBUG [main] --- Detect product boot completed.
2021-06-28 05:19:49 EDT DEBUG [main] --- Guessed Detect jar location as /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:19:49 EDT DEBUG [main] --- Checking for jar file: /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:19:49 EDT DEBUG [main] --- Found detect jar file.
2021-06-28 05:19:49 EDT DEBUG [main] --- Detect boot completed.
2021-06-28 05:19:49 EDT DEBUG [main] --- Detect will attempt to run.
2021-06-28 05:19:49 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:19:49 EDT INFO  [main] --- Polaris tools will not be run.
2021-06-28 05:19:49 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:19:49 EDT INFO  [main] --- Will include the Docker tool.
2021-06-28 05:19:49 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:19:49 EDT INFO  [main] --- Docker actions finished.
2021-06-28 05:19:49 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:19:49 EDT INFO  [main] --- Will include the Bazel tool.
2021-06-28 05:19:49 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:19:49 EDT INFO  [main] --- Bazel actions finished.
2021-06-28 05:19:49 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:19:49 EDT INFO  [main] --- Will include the detector tool.
2021-06-28 05:19:49 EDT DEBUG [main] --- Initializing detector system.
2021-06-28 05:19:49 EDT DEBUG [main] --- Starting detector file system traversal.
2021-06-28 05:19:49 EDT DEBUG [main] --- Traversing directory: /home/<USER>/scripts/code
2021-06-28 05:19:49 EDT INFO  [main] --- Searching for detectors. This may take a while.
2021-06-28 05:19:50 EDT DEBUG [main] --- 
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- Search results
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- No detectors found.
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- 
2021-06-28 05:19:50 EDT INFO  [main] --- 
2021-06-28 05:19:50 EDT DEBUG [main] --- Starting detector preparation.
2021-06-28 05:19:50 EDT DEBUG [main] --- Preparing detectors for discovery and extraction.
2021-06-28 05:19:50 EDT DEBUG [main] --- Counting detectors that will be evaluated.
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:19:50 EDT DEBUG [main] --- Total number of detectors: 0
2021-06-28 05:19:50 EDT DEBUG [main] --- Starting detector project discovery.
2021-06-28 05:19:50 EDT DEBUG [main] --- 
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- Discovery results:
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- 
2021-06-28 05:19:50 EDT DEBUG [main] --- Starting detector extraction.
2021-06-28 05:19:50 EDT DEBUG [main] --- Finished detectors.
2021-06-28 05:19:50 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT INFO  [main] --- Detector Report
2021-06-28 05:19:50 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT INFO  [main] --- No detectors found.
2021-06-28 05:19:50 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:19:50 EDT DEBUG [main] --- Publishing file events.
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:19:50 EDT DEBUG [main] --- Finished running detectors.
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:19:50 EDT INFO  [main] --- Detector actions finished.
2021-06-28 05:19:50 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:19:50 EDT DEBUG [main] --- Completed code location tools.
2021-06-28 05:19:50 EDT DEBUG [main] --- Determining project info.
2021-06-28 05:19:50 EDT INFO  [main] --- Project name: project_100
2021-06-28 05:19:50 EDT INFO  [main] --- Project version: project_100_version
2021-06-28 05:19:50 EDT DEBUG [main] --- Black Duck tools will run.
2021-06-28 05:19:50 EDT DEBUG [main] --- Creating BDIO code locations.
2021-06-28 05:19:50 EDT DEBUG [main] --- Creating BDIO files from code locations.
2021-06-28 05:19:50 EDT DEBUG [main] --- 
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- Extraction results:
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:19:50 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:19:50 EDT DEBUG [main] --- 
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:19:50 EDT DEBUG [main] --- Getting or creating project.
2021-06-28 05:19:50 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:19:50 EDT DEBUG [main] --- No clone project or version name supplied. Will not clone.
2021-06-28 05:19:51 EDT DEBUG [main] --- No 'Application ID' to set.
2021-06-28 05:19:51 EDT DEBUG [main] --- No custom fields to set.
2021-06-28 05:19:51 EDT DEBUG [main] --- Will not unmap code locations: Project view was not present, or should not unmap code locations.
2021-06-28 05:19:51 EDT DEBUG [main] --- Completed project and version actions.
2021-06-28 05:19:51 EDT DEBUG [main] --- Processing Detect Code Locations.
2021-06-28 05:19:51 EDT DEBUG [main] --- Did not create any BDIO files.
2021-06-28 05:19:51 EDT DEBUG [main] --- Completed Detect Code Location processing.
2021-06-28 05:19:51 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:19:51 EDT INFO  [main] --- Will include the signature scanner tool.
2021-06-28 05:19:51 EDT DEBUG [main] --- Determining signature scanner install directory...
2021-06-28 05:19:51 EDT DEBUG [main] --- Using default scanner path.
2021-06-28 05:19:51 EDT DEBUG [main] --- Determined install directory: /home/<USER>/blackduck/tools
2021-06-28 05:19:51 EDT DEBUG [main] --- Signature scanner will use the Black Duck server to download/update the scanner - this is the most likely situation.
2021-06-28 05:19:51 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:19:51 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:19:51 EDT DEBUG [main] --- Signature scan is online.
2021-06-28 05:19:51 EDT INFO  [main] --- No scan targets provided - registering the source path /home/<USER>/scripts/code to scan
2021-06-28 05:19:51 EDT DEBUG [main] --- The directory structure was likely created by the installer
2021-06-28 05:19:51 EDT DEBUG [main] --- last time downloaded: 1617070363000
2021-06-28 05:19:51 EDT DEBUG [main] --- Last modified on server: 1617070363000
2021-06-28 05:19:51 EDT DEBUG [main] --- The request has not been modified since it was last checked - skipping.
2021-06-28 05:19:51 EDT DEBUG [main] --- The Black Duck Signature Scanner has not been modified since it was last downloaded - skipping download.
2021-06-28 05:19:51 EDT INFO  [main] --- The Black Duck Signature Scanner downloaded/found successfully: /home/<USER>/blackduck/tools
2021-06-28 05:19:51 EDT INFO  [main] --- Starting the Black Duck Signature Scan commands.
2021-06-28 05:19:51 EDT DEBUG [pool-3-thread-1] --- The directory structure was likely created by the installer
2021-06-28 05:19:51 EDT DEBUG [pool-3-thread-1] --- Using this java installation : /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java
2021-06-28 05:19:51 EDT DEBUG [pool-3-thread-1] --- Using the Black Duck hostname : 'test-yuan.app.blackduck.com'
2021-06-28 05:19:51 EDT INFO  [pool-3-thread-1] --- Black Duck CLI command: /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java -Done-jar.silent=true -Done-jar.jar.path=/home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/cache/scan.cli.impl-standalone.jar -Xmx4096m -jar /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/scan.cli-2021.2.1-standalone.jar --no-prompt --scheme https --host test-yuan.app.blackduck.com --username sysadmin --port 443 --insecure --snippet-matching --upload-source -v --logDir /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1 --statusWriteDir /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1 --project project_100 --release project_100_version --name api_100 scan /home/<USER>/scripts/code
2021-06-28 05:19:53 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start wrapper: ScanCliWrapperSettings [commandLine=org.apache.commons.cli.CommandLine@a1f72f5, fileUriSet=null, scheme=https, host=test-yuan.app.blackduck.com, port=443]...
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start scan loop: ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, --project, project_100, --release, project_100_version, --name, api_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[api_100 scan], project=Optional[project_100], release=Optional[project_100_version], username=sysadmin, password=<NOT SHOWN>]...
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Individual File Matching Detected: none...
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized sha1 white list: []...
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized clean sha1 white list: []...
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Initialize client for test-yuan.app.blackduck.com:443
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/scan-summaries
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scanstatus
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans/signatureVersion
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/scan-candidates?scanCandidatesParam={scanCandidatesParam}&limit={limit}&offset={offset}
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/fp-import/scans/{scanId}
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/projects?q=name:{projname}&limit={limit}&offset={offset}
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/scan-sources/{sha1}
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/bulk-sources
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/uploaded-file-data
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search-matches
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-vsls
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-family-vsls
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/global-settings/{settingName}
2021-06-28 05:19:54 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Execute scan file://sup-cn-hub05/home/<USER>/scripts/code....
2021-06-28 05:19:55 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to false
2021-06-28 05:19:56 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 0 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:19:56 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Scan...
2021-06-28 05:19:56 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: ScanExecResult: ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, --project, project_100, --release, project_100_version, --name, api_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[api_100 scan], project=Optional[project_100], release=Optional[project_100_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=ScanContainerView{scanId=70a74aa5-5675-48b2-a38e-1ece02f72de6, createdOn=2021-06-28T09:19:54.519Z, timeToScan=236, scannerVersion=2021.2.1, signatureVersion=7.0.0, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#sup-cn-hub05-code, baseDir=/home/<USER>/scripts/code, name=Optional[api_100 scan], project=Optional[project_100], release=Optional[project_100_version], scanProblem=null, scanProblemList.size()=0, scanNodeList.size()=5}, fpScanContainer=null, stringSearchScanContainer=null, scanResult=ScanView{id=70a74aa5-5675-48b2-a38e-1ece02f72de6, scannerVersion=2021.2.1, signatureVersion=7.0.0, name=api_100 scan, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#70a74aa5-5675-48b2-a38e-1ece02f72de6, baseDir=/, createdOn=2021-06-28T09:19:54.519Z, lastModifiedOn=2021-06-28T09:19:55.769Z, timeToScan=0, createdByUserId=********-0000-0000-0001-********0001, matchCount=0, numDirs=0, numNonDirFiles=0, status=SCANNING, deepSystemSize=Optional.empty, scanSourceType=SN, scanSourceId=70a74aa5-5675-48b2-a38e-1ece02f72de6, timeLastModified=1624871995769, timeToPersistMs=0, scanTime=1624871994519, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=SCANNING, createdAt=2021-06-28T09:19:54.519Z, updatedAt=2021-06-28T09:19:55.769Z, createdByUserName=Optional[sysadmin], scanSize=Optional.empty, serverVersion=2021.2.1, matchCount=0, directoryCount=Optional[0], fileCount=Optional[0], baseDirectory=/, hostName=sup-cn-hub05, scanType=FS, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/70a74aa5-5675-48b2-a38e-1ece02f72de6, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/9fe20bf7-d118-4437-b1ec-b38bd40343dd}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/70a74aa5-5675-48b2-a38e-1ece02f72de6/bom-entries}]}}, scanDate=Mon Jun 28 05:19:54 EDT 2021, scanEndDate=Mon Jun 28 05:19:56 EDT 2021, scanCreated=true]
2021-06-28 05:19:56 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Checking FS scan status before running snippet scan: 70a74aa5-5675-48b2-a38e-1ece02f72de6
2021-06-28 05:20:27 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FS scan 70a74aa5-5675-48b2-a38e-1ece02f72de6 was found, scan status is COMPLETE
2021-06-28 05:20:27 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Starting calculation of fingerprints for snippet scan files...
2021-06-28 05:20:27 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to true
2021-06-28 05:20:28 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:20:28 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:20:28 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 0 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:20:28 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Fingerprint Scan...
2021-06-28 05:20:31 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FingerprintScanExecResult : ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, --project, project_100, --release, project_100_version, --name, api_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[api_100 scan], project=Optional[project_100], release=Optional[project_100_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=null, fpScanContainer=FingerprintScanContainerView{snippetMatchingAllSource=false, fullSnippetScan=false, scanProblem=null, scanProblemList.size()=0, fingerprintScanNodeList.size()=2}, stringSearchScanContainer=null, scanResult=ScanView{id=fb7a0fdb-c8d3-4023-bba7-e45598537686, matchCount=0, numDirs=0, numNonDirFiles=0, status=UNSTARTED, deepSystemSize=Optional.empty, timeLastModified=0, timeToPersistMs=0, scanTime=0, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=MATCHING, createdAt=2021-06-28T09:20:27.288Z, updatedAt=2021-06-28T09:20:30.822Z, createdByUserName=Optional[sysadmin], scanSize=Optional[82958], serverVersion=2021.2.1, matchCount=0, directoryCount=Optional[0], fileCount=Optional[2], baseDirectory=/home/<USER>/scripts/code, hostName=sup-cn-hub05, scanType=SNIPPET, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/fb7a0fdb-c8d3-4023-bba7-e45598537686, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/9fe20bf7-d118-4437-b1ec-b38bd40343dd}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/fb7a0fdb-c8d3-4023-bba7-e45598537686/bom-entries}]}}, scanDate=Mon Jun 28 05:20:27 EDT 2021, scanEndDate=Mon Jun 28 05:20:31 EDT 2021, scanCreated=true]
2021-06-28 05:20:31 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1/log/sup-cn-hub05-code-2021-06-28T091954.519Z.log
2021-06-28 05:20:31 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Logging to file: 
Logging to file: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1/log/sup-cn-hub05-code-2021-06-28T091954.519Z.log
2021-06-28 05:20:31 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1/status/sup-cn-hub05-code-2021-06-28T091954.519Z.json
2021-06-28 05:20:31 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Persist ScanSummary to file...
2021-06-28 05:20:31 EDT DEBUG [pool-3-thread-1] --- INFO: Persisted status: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1/status/sup-cn-hub05-code-2021-06-28T091954.519Z.json
2021-06-28 05:20:31 EDT INFO  [pool-3-thread-1] --- 
2021-06-28 05:20:31 EDT INFO  [pool-3-thread-1] --- Black Duck Signature Scanner return code: 0
2021-06-28 05:20:31 EDT INFO  [pool-3-thread-1] --- You can view the logs at: '/home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan/BlackDuckScanOutput/2021-06-28_09-19-51-883_1'
2021-06-28 05:20:31 EDT INFO  [main] --- Completed the Black Duck Signature Scan commands.
2021-06-28 05:20:31 EDT INFO  [main] --- Signature scanner actions finished.
2021-06-28 05:20:31 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:20:31 EDT INFO  [main] --- Will include the binary scanner tool.
2021-06-28 05:20:31 EDT INFO  [main] --- Binary scanner actions finished.
2021-06-28 05:20:31 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:20:31 EDT INFO  [main] --- Vulnerability Impact Analysis tool will not be run.
2021-06-28 05:20:31 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:20:31 EDT INFO  [main] --- Will perform Black Duck post actions.
2021-06-28 05:20:31 EDT INFO  [main] --- Detect must wait for bom tool calculations to finish.
2021-06-28 05:20:31 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:20:31 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:20:31 EDT INFO  [main] --- Found api_100 scan code location (1 of 2).
2021-06-28 05:20:31 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:20:32 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:20:32 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:20:32 EDT INFO  [main] --- Found api_100 scan code location (1 of 2).
2021-06-28 05:20:32 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:20:32 EDT INFO  [main] --- Try #1 (elapsed: 00:00:00.000)...not done yet, waiting 60 seconds and trying again...
2021-06-28 05:21:33 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:21:33 EDT DEBUG [main] --- There were 2 notifications found.
2021-06-28 05:21:33 EDT INFO  [main] --- Found api_100 scan code location (1 of 2).
2021-06-28 05:21:33 EDT INFO  [main] --- Found api_100 scan code location (2 of 2).
2021-06-28 05:21:33 EDT INFO  [main] --- Try #2 (elapsed: 00:01:01.295)...complete!
2021-06-28 05:21:33 EDT INFO  [main] --- All code locations have been added to the BOM.
2021-06-28 05:21:33 EDT INFO  [main] --- Black Duck actions have finished.
2021-06-28 05:21:33 EDT INFO  [main] --- All tools have finished.
2021-06-28 05:21:33 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- Creating status file: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/status/status.json
2021-06-28 05:21:33 EDT DEBUG [main] --- Detect shutdown begin.
2021-06-28 05:21:33 EDT DEBUG [main] --- Ending phone home.
2021-06-28 05:21:33 EDT DEBUG [main] --- Detect will cleanup.
2021-06-28 05:21:33 EDT DEBUG [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848
2021-06-28 05:21:33 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/status
2021-06-28 05:21:33 EDT INFO  [main] --- Status file has been deleted.  To preserve status file, turn off cleanup actions.
2021-06-28 05:21:33 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/bdio
2021-06-28 05:21:33 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848/scan
2021-06-28 05:21:33 EDT INFO  [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-19-43-848
2021-06-28 05:21:33 EDT DEBUG [main] --- Detect shutdown completed.
2021-06-28 05:21:33 EDT DEBUG [main] --- All Detect actions completed.
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- ======== Detect Issues ========
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- DEPRECATIONS:
2021-06-28 05:21:33 EDT INFO  [main] --- 	blackduck.username
2021-06-28 05:21:33 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:21:33 EDT INFO  [main] --- 	blackduck.password
2021-06-28 05:21:33 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- ======== Detect Result ========
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- Black Duck Project BOM: https://test-yuan.app.blackduck.com/api/projects/ad7841d1-1716-4055-b1d4-424e53705ae7/versions/c595bb9d-691d-4f82-965d-be5a13b455c6/components
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- ======== Detect Status ========
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- Signature scan / Snippet scan on /home/<USER>/scripts/code: SUCCESS
2021-06-28 05:21:33 EDT INFO  [main] --- Overall Status: SUCCESS - Detect exited successfully.
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- ===============================
2021-06-28 05:21:33 EDT INFO  [main] --- 
2021-06-28 05:21:33 EDT INFO  [main] --- Detect duration: 00h 01m 49s 538ms

______     _            _
|  _  \   | |          | |
| | | |___| |_ ___  ___| |_
| | | / _ \ __/ _ \/ __| __|
| |/ /  __/ ||  __/ (__| |_
|___/ \___|\__\___|\___|\__|

2021-06-28 05:21:34 EDT DEBUG [main] --- Initializing detect.
2021-06-28 05:21:35 EDT DEBUG [main] --- You seem to be running in a LINUX operating system.
2021-06-28 05:21:35 EDT DEBUG [main] --- Detect boot begin.

Detect Version: 6.9.1

2021-06-28 05:21:35 EDT DEBUG [main] --- Configuration processed completely.
2021-06-28 05:21:35 EDT INFO  [main] --- 
2021-06-28 05:21:35 EDT INFO  [main] --- Current property values:
2021-06-28 05:21:35 EDT INFO  [main] --- --property = value [notes]
2021-06-28 05:21:35 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:21:35 EDT INFO  [main] --- blackduck.password = **************** [cmd] 	 *** DEPRECATED ***
2021-06-28 05:21:35 EDT INFO  [main] --- blackduck.trust.cert = true [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- blackduck.url = https://test-yuan.app.blackduck.com/ [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- blackduck.username = sysadmin [cmd] 	 *** DEPRECATED ***
2021-06-28 05:21:35 EDT INFO  [main] --- detect.blackduck.signature.scanner.snippet.matching = SNIPPET_MATCHING [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- detect.blackduck.signature.scanner.upload.source.mode = true [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- detect.code.location.name = account_100 [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- detect.project.name = project_100 [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- detect.project.version.name = project_100_version [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- detect.timeout = 600 [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- detect.wait.for.results = true [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- logging.level.com.synopsys.integration = DEBUG [cmd] 
2021-06-28 05:21:35 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:21:35 EDT INFO  [main] --- 
2021-06-28 05:21:35 EDT DEBUG [main] --- Initializing Detect.
2021-06-28 05:21:35 EDT INFO  [main] --- Tilde's will be automatically resolved to USER HOME.
2021-06-28 05:21:35 EDT INFO  [main] --- Source directory: /home/<USER>/scripts/code
2021-06-28 05:21:35 EDT INFO  [main] --- Output directory: /home/<USER>/blackduck
2021-06-28 05:21:35 EDT INFO  [main] --- Run directory: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001
2021-06-28 05:21:35 EDT DEBUG [main] --- Main boot completed. Deciding what Detect should do.
2021-06-28 05:21:35 EDT INFO  [main] --- 
2021-06-28 05:21:35 EDT DEBUG [main] --- Black Duck will run: A Black Duck url was found.
2021-06-28 05:21:35 EDT DEBUG [main] --- Polaris will NOT run: The Polaris url must be provided.
2021-06-28 05:21:35 EDT DEBUG [main] --- Decided what products will be run. Starting product boot.
2021-06-28 05:21:35 EDT DEBUG [main] --- Detect product boot start.
2021-06-28 05:21:35 EDT DEBUG [main] --- Will boot Black Duck product.
2021-06-28 05:21:35 EDT DEBUG [main] --- Detect will check communication with the Black Duck server.
2021-06-28 05:21:35 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:21:36 EDT INFO  [main] --- A successful connection was made.
2021-06-28 05:21:36 EDT INFO  [main] --- Connection to the Black Duck server was successful.
2021-06-28 05:21:36 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:21:36 EDT INFO  [main] --- Successfully connected to Black Duck (version 2021.2.1)!
2021-06-28 05:21:36 EDT DEBUG [main] --- Connected as: sysadmin
2021-06-28 05:21:40 EDT DEBUG [main] --- Roles: System Administrator, Global Code Scanner, Policy Manager, License Manager, Project Manager, Project Viewer, Project Creator, Super User, Global Project Viewer, Copyright Editor, Component Manager, Global Security Manager
2021-06-28 05:21:40 EDT DEBUG [main] --- Group: 
2021-06-28 05:21:40 EDT DEBUG [main] --- Detect product boot completed.
2021-06-28 05:21:40 EDT DEBUG [main] --- Guessed Detect jar location as /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:21:40 EDT DEBUG [main] --- Checking for jar file: /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:21:40 EDT DEBUG [main] --- Found detect jar file.
2021-06-28 05:21:40 EDT DEBUG [main] --- Detect boot completed.
2021-06-28 05:21:40 EDT DEBUG [main] --- Detect will attempt to run.
2021-06-28 05:21:40 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:40 EDT INFO  [main] --- Polaris tools will not be run.
2021-06-28 05:21:40 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:40 EDT INFO  [main] --- Will include the Docker tool.
2021-06-28 05:21:40 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:21:40 EDT INFO  [main] --- Docker actions finished.
2021-06-28 05:21:40 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:40 EDT INFO  [main] --- Will include the Bazel tool.
2021-06-28 05:21:40 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:21:40 EDT INFO  [main] --- Bazel actions finished.
2021-06-28 05:21:40 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:40 EDT INFO  [main] --- Will include the detector tool.
2021-06-28 05:21:40 EDT DEBUG [main] --- Initializing detector system.
2021-06-28 05:21:40 EDT DEBUG [main] --- Starting detector file system traversal.
2021-06-28 05:21:40 EDT DEBUG [main] --- Traversing directory: /home/<USER>/scripts/code
2021-06-28 05:21:40 EDT INFO  [main] --- Searching for detectors. This may take a while.
2021-06-28 05:21:41 EDT DEBUG [main] --- 
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- Search results
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- No detectors found.
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- 
2021-06-28 05:21:41 EDT INFO  [main] --- 
2021-06-28 05:21:41 EDT DEBUG [main] --- Starting detector preparation.
2021-06-28 05:21:41 EDT DEBUG [main] --- Preparing detectors for discovery and extraction.
2021-06-28 05:21:41 EDT DEBUG [main] --- Counting detectors that will be evaluated.
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:21:41 EDT DEBUG [main] --- Total number of detectors: 0
2021-06-28 05:21:41 EDT DEBUG [main] --- Starting detector project discovery.
2021-06-28 05:21:41 EDT DEBUG [main] --- 
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- Discovery results:
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- 
2021-06-28 05:21:41 EDT DEBUG [main] --- Starting detector extraction.
2021-06-28 05:21:41 EDT DEBUG [main] --- Finished detectors.
2021-06-28 05:21:41 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT INFO  [main] --- Detector Report
2021-06-28 05:21:41 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT INFO  [main] --- No detectors found.
2021-06-28 05:21:41 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:41 EDT DEBUG [main] --- Publishing file events.
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:21:41 EDT DEBUG [main] --- Finished running detectors.
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:21:41 EDT INFO  [main] --- Detector actions finished.
2021-06-28 05:21:41 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:41 EDT DEBUG [main] --- Completed code location tools.
2021-06-28 05:21:41 EDT DEBUG [main] --- Determining project info.
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:21:41 EDT INFO  [main] --- Project name: project_100
2021-06-28 05:21:41 EDT INFO  [main] --- Project version: project_100_version
2021-06-28 05:21:41 EDT DEBUG [main] --- Black Duck tools will run.
2021-06-28 05:21:41 EDT DEBUG [main] --- Creating BDIO code locations.
2021-06-28 05:21:41 EDT DEBUG [main] --- Creating BDIO files from code locations.
2021-06-28 05:21:41 EDT DEBUG [main] --- 
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- Extraction results:
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:21:41 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:21:41 EDT DEBUG [main] --- 
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:21:41 EDT DEBUG [main] --- Getting or creating project.
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:21:41 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:21:41 EDT DEBUG [main] --- No clone project or version name supplied. Will not clone.
2021-06-28 05:21:42 EDT DEBUG [main] --- No 'Application ID' to set.
2021-06-28 05:21:42 EDT DEBUG [main] --- No custom fields to set.
2021-06-28 05:21:42 EDT DEBUG [main] --- Will not unmap code locations: Project view was not present, or should not unmap code locations.
2021-06-28 05:21:42 EDT DEBUG [main] --- Completed project and version actions.
2021-06-28 05:21:42 EDT DEBUG [main] --- Processing Detect Code Locations.
2021-06-28 05:21:42 EDT DEBUG [main] --- Did not create any BDIO files.
2021-06-28 05:21:42 EDT DEBUG [main] --- Completed Detect Code Location processing.
2021-06-28 05:21:42 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:21:42 EDT INFO  [main] --- Will include the signature scanner tool.
2021-06-28 05:21:42 EDT DEBUG [main] --- Determining signature scanner install directory...
2021-06-28 05:21:42 EDT DEBUG [main] --- Using default scanner path.
2021-06-28 05:21:42 EDT DEBUG [main] --- Determined install directory: /home/<USER>/blackduck/tools
2021-06-28 05:21:42 EDT DEBUG [main] --- Signature scanner will use the Black Duck server to download/update the scanner - this is the most likely situation.
2021-06-28 05:21:42 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:21:42 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:21:42 EDT DEBUG [main] --- Signature scan is online.
2021-06-28 05:21:42 EDT INFO  [main] --- No scan targets provided - registering the source path /home/<USER>/scripts/code to scan
2021-06-28 05:21:42 EDT DEBUG [main] --- The directory structure was likely created by the installer
2021-06-28 05:21:42 EDT DEBUG [main] --- last time downloaded: 1617070363000
2021-06-28 05:21:42 EDT DEBUG [main] --- Last modified on server: 1617070363000
2021-06-28 05:21:42 EDT DEBUG [main] --- The request has not been modified since it was last checked - skipping.
2021-06-28 05:21:42 EDT DEBUG [main] --- The Black Duck Signature Scanner has not been modified since it was last downloaded - skipping download.
2021-06-28 05:21:42 EDT INFO  [main] --- The Black Duck Signature Scanner downloaded/found successfully: /home/<USER>/blackduck/tools
2021-06-28 05:21:42 EDT INFO  [main] --- Starting the Black Duck Signature Scan commands.
2021-06-28 05:21:42 EDT DEBUG [pool-3-thread-1] --- The directory structure was likely created by the installer
2021-06-28 05:21:42 EDT DEBUG [pool-3-thread-1] --- Using this java installation : /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java
2021-06-28 05:21:42 EDT DEBUG [pool-3-thread-1] --- Using the Black Duck hostname : 'test-yuan.app.blackduck.com'
2021-06-28 05:21:42 EDT INFO  [pool-3-thread-1] --- Black Duck CLI command: /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java -Done-jar.silent=true -Done-jar.jar.path=/home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/cache/scan.cli.impl-standalone.jar -Xmx4096m -jar /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/scan.cli-2021.2.1-standalone.jar --no-prompt --scheme https --host test-yuan.app.blackduck.com --username sysadmin --port 443 --insecure --snippet-matching --upload-source -v --logDir /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1 --statusWriteDir /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1 --project project_100 --release project_100_version --name account_100 scan /home/<USER>/scripts/code
2021-06-28 05:21:44 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start wrapper: ScanCliWrapperSettings [commandLine=org.apache.commons.cli.CommandLine@a1f72f5, fileUriSet=null, scheme=https, host=test-yuan.app.blackduck.com, port=443]...
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start scan loop: ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, --project, project_100, --release, project_100_version, --name, account_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[account_100 scan], project=Optional[project_100], release=Optional[project_100_version], username=sysadmin, password=<NOT SHOWN>]...
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Individual File Matching Detected: none...
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized sha1 white list: []...
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized clean sha1 white list: []...
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Initialize client for test-yuan.app.blackduck.com:443
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/scan-summaries
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scanstatus
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans/signatureVersion
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/scan-candidates?scanCandidatesParam={scanCandidatesParam}&limit={limit}&offset={offset}
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/fp-import/scans/{scanId}
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/projects?q=name:{projname}&limit={limit}&offset={offset}
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/scan-sources/{sha1}
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/bulk-sources
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/uploaded-file-data
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search-matches
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-vsls
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-family-vsls
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/global-settings/{settingName}
2021-06-28 05:21:45 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Execute scan file://sup-cn-hub05/home/<USER>/scripts/code....
2021-06-28 05:21:46 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to false
2021-06-28 05:21:46 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 0 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:21:46 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Scan...
2021-06-28 05:21:46 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: ScanExecResult: ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, --project, project_100, --release, project_100_version, --name, account_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[account_100 scan], project=Optional[project_100], release=Optional[project_100_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=ScanContainerView{scanId=dc5f5eaa-dc2a-427c-a5b0-9928af987229, createdOn=2021-06-28T09:21:45.407Z, timeToScan=232, scannerVersion=2021.2.1, signatureVersion=7.0.0, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#sup-cn-hub05-code, baseDir=/home/<USER>/scripts/code, name=Optional[account_100 scan], project=Optional[project_100], release=Optional[project_100_version], scanProblem=null, scanProblemList.size()=0, scanNodeList.size()=5}, fpScanContainer=null, stringSearchScanContainer=null, scanResult=ScanView{id=dc5f5eaa-dc2a-427c-a5b0-9928af987229, scannerVersion=2021.2.1, signatureVersion=7.0.0, name=account_100 scan, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#dc5f5eaa-dc2a-427c-a5b0-9928af987229, baseDir=/, createdOn=2021-06-28T09:21:45.407Z, lastModifiedOn=2021-06-28T09:21:46.111Z, timeToScan=0, createdByUserId=********-0000-0000-0001-********0001, matchCount=0, numDirs=0, numNonDirFiles=0, status=SCANNING, deepSystemSize=Optional.empty, scanSourceType=SN, scanSourceId=dc5f5eaa-dc2a-427c-a5b0-9928af987229, timeLastModified=*************, timeToPersistMs=0, scanTime=*************, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=SCANNING, createdAt=2021-06-28T09:21:45.407Z, updatedAt=2021-06-28T09:21:46.111Z, createdByUserName=Optional[sysadmin], scanSize=Optional.empty, serverVersion=2021.2.1, matchCount=0, directoryCount=Optional[0], fileCount=Optional[0], baseDirectory=/, hostName=sup-cn-hub05, scanType=FS, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/dc5f5eaa-dc2a-427c-a5b0-9928af987229, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/48677481-0600-4770-9e08-9bd9f770494c}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/dc5f5eaa-dc2a-427c-a5b0-9928af987229/bom-entries}]}}, scanDate=Mon Jun 28 05:21:45 EDT 2021, scanEndDate=Mon Jun 28 05:21:46 EDT 2021, scanCreated=true]
2021-06-28 05:21:47 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Checking FS scan status before running snippet scan: dc5f5eaa-dc2a-427c-a5b0-9928af987229
2021-06-28 05:22:17 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FS scan dc5f5eaa-dc2a-427c-a5b0-9928af987229 was found, scan status is COMPLETE
2021-06-28 05:22:17 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Starting calculation of fingerprints for snippet scan files...
2021-06-28 05:22:17 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to true
2021-06-28 05:22:21 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:22:21 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:22:21 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 4 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:22:21 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Fingerprint Scan...
2021-06-28 05:22:22 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FingerprintScanExecResult : ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, --project, project_100, --release, project_100_version, --name, account_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[account_100 scan], project=Optional[project_100], release=Optional[project_100_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=null, fpScanContainer=FingerprintScanContainerView{snippetMatchingAllSource=false, fullSnippetScan=false, scanProblem=null, scanProblemList.size()=0, fingerprintScanNodeList.size()=2}, stringSearchScanContainer=null, scanResult=ScanView{id=0a20d6f1-d94e-45e1-8844-c2eae97aeb92, matchCount=0, numDirs=0, numNonDirFiles=0, status=UNSTARTED, deepSystemSize=Optional.empty, timeLastModified=0, timeToPersistMs=0, scanTime=0, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=MATCHING, createdAt=2021-06-28T09:22:17.288Z, updatedAt=2021-06-28T09:22:22.242Z, createdByUserName=Optional[sysadmin], scanSize=Optional[82958], serverVersion=2021.2.1, matchCount=0, directoryCount=Optional[0], fileCount=Optional[2], baseDirectory=/home/<USER>/scripts/code, hostName=sup-cn-hub05, scanType=SNIPPET, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/0a20d6f1-d94e-45e1-8844-c2eae97aeb92, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/48677481-0600-4770-9e08-9bd9f770494c}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/0a20d6f1-d94e-45e1-8844-c2eae97aeb92/bom-entries}]}}, scanDate=Mon Jun 28 05:22:17 EDT 2021, scanEndDate=Mon Jun 28 05:22:22 EDT 2021, scanCreated=true]
2021-06-28 05:22:22 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1/log/sup-cn-hub05-code-2021-06-28T092145.407Z.log
2021-06-28 05:22:22 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Logging to file: 
Logging to file: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1/log/sup-cn-hub05-code-2021-06-28T092145.407Z.log
2021-06-28 05:22:22 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1/status/sup-cn-hub05-code-2021-06-28T092145.407Z.json
2021-06-28 05:22:22 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Persist ScanSummary to file...
2021-06-28 05:22:22 EDT DEBUG [pool-3-thread-1] --- INFO: Persisted status: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1/status/sup-cn-hub05-code-2021-06-28T092145.407Z.json
2021-06-28 05:22:22 EDT INFO  [pool-3-thread-1] --- 
2021-06-28 05:22:22 EDT INFO  [pool-3-thread-1] --- Black Duck Signature Scanner return code: 0
2021-06-28 05:22:22 EDT INFO  [pool-3-thread-1] --- You can view the logs at: '/home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan/BlackDuckScanOutput/2021-06-28_09-21-42-886_1'
2021-06-28 05:22:22 EDT INFO  [main] --- Completed the Black Duck Signature Scan commands.
2021-06-28 05:22:23 EDT INFO  [main] --- Signature scanner actions finished.
2021-06-28 05:22:23 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:22:23 EDT INFO  [main] --- Will include the binary scanner tool.
2021-06-28 05:22:23 EDT INFO  [main] --- Binary scanner actions finished.
2021-06-28 05:22:23 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:22:23 EDT INFO  [main] --- Vulnerability Impact Analysis tool will not be run.
2021-06-28 05:22:23 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:22:23 EDT INFO  [main] --- Will perform Black Duck post actions.
2021-06-28 05:22:23 EDT INFO  [main] --- Detect must wait for bom tool calculations to finish.
2021-06-28 05:22:23 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:22:23 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:22:23 EDT INFO  [main] --- Found account_100 scan code location (1 of 2).
2021-06-28 05:22:23 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:22:24 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:22:24 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:22:24 EDT INFO  [main] --- Found account_100 scan code location (1 of 2).
2021-06-28 05:22:24 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:22:24 EDT INFO  [main] --- Try #1 (elapsed: 00:00:00.000)...not done yet, waiting 60 seconds and trying again...
2021-06-28 05:23:24 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:23:25 EDT DEBUG [main] --- There were 2 notifications found.
2021-06-28 05:23:25 EDT INFO  [main] --- Found account_100 scan code location (1 of 2).
2021-06-28 05:23:25 EDT INFO  [main] --- Found account_100 scan code location (2 of 2).
2021-06-28 05:23:25 EDT INFO  [main] --- Try #2 (elapsed: 00:01:01.376)...complete!
2021-06-28 05:23:25 EDT INFO  [main] --- All code locations have been added to the BOM.
2021-06-28 05:23:25 EDT INFO  [main] --- Black Duck actions have finished.
2021-06-28 05:23:25 EDT INFO  [main] --- All tools have finished.
2021-06-28 05:23:25 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- Creating status file: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/status/status.json
2021-06-28 05:23:25 EDT DEBUG [main] --- Detect shutdown begin.
2021-06-28 05:23:25 EDT DEBUG [main] --- Ending phone home.
2021-06-28 05:23:25 EDT DEBUG [main] --- Detect will cleanup.
2021-06-28 05:23:25 EDT DEBUG [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001
2021-06-28 05:23:25 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/status
2021-06-28 05:23:25 EDT INFO  [main] --- Status file has been deleted.  To preserve status file, turn off cleanup actions.
2021-06-28 05:23:25 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/bdio
2021-06-28 05:23:25 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001/scan
2021-06-28 05:23:25 EDT INFO  [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-21-35-001
2021-06-28 05:23:25 EDT DEBUG [main] --- Detect shutdown completed.
2021-06-28 05:23:25 EDT DEBUG [main] --- All Detect actions completed.
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- ======== Detect Issues ========
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- DEPRECATIONS:
2021-06-28 05:23:25 EDT INFO  [main] --- 	blackduck.username
2021-06-28 05:23:25 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:23:25 EDT INFO  [main] --- 	blackduck.password
2021-06-28 05:23:25 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- ======== Detect Result ========
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- Black Duck Project BOM: https://test-yuan.app.blackduck.com/api/projects/ad7841d1-1716-4055-b1d4-424e53705ae7/versions/c595bb9d-691d-4f82-965d-be5a13b455c6/components
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- ======== Detect Status ========
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- Signature scan / Snippet scan on /home/<USER>/scripts/code: SUCCESS
2021-06-28 05:23:25 EDT INFO  [main] --- Overall Status: SUCCESS - Detect exited successfully.
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- ===============================
2021-06-28 05:23:25 EDT INFO  [main] --- 
2021-06-28 05:23:25 EDT INFO  [main] --- Detect duration: 00h 01m 50s 125ms

______     _            _
|  _  \   | |          | |
| | | |___| |_ ___  ___| |_
| | | / _ \ __/ _ \/ __| __|
| |/ /  __/ ||  __/ (__| |_
|___/ \___|\__\___|\___|\__|

2021-06-28 05:23:26 EDT DEBUG [main] --- Initializing detect.
2021-06-28 05:23:26 EDT DEBUG [main] --- You seem to be running in a LINUX operating system.
2021-06-28 05:23:26 EDT DEBUG [main] --- Detect boot begin.

Detect Version: 6.9.1

2021-06-28 05:23:26 EDT DEBUG [main] --- Configuration processed completely.
2021-06-28 05:23:26 EDT INFO  [main] --- 
2021-06-28 05:23:26 EDT INFO  [main] --- Current property values:
2021-06-28 05:23:26 EDT INFO  [main] --- --property = value [notes]
2021-06-28 05:23:26 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:23:26 EDT INFO  [main] --- blackduck.password = **************** [cmd] 	 *** DEPRECATED ***
2021-06-28 05:23:26 EDT INFO  [main] --- blackduck.trust.cert = true [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- blackduck.url = https://test-yuan.app.blackduck.com/ [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- blackduck.username = sysadmin [cmd] 	 *** DEPRECATED ***
2021-06-28 05:23:26 EDT INFO  [main] --- detect.blackduck.signature.scanner.snippet.matching = SNIPPET_MATCHING [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- detect.blackduck.signature.scanner.upload.source.mode = true [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- detect.code.location.name = account_100 [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- detect.project.name = project_101 [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- detect.project.version.name = project_101_version [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- detect.timeout = 600 [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- detect.wait.for.results = true [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- logging.level.com.synopsys.integration = DEBUG [cmd] 
2021-06-28 05:23:26 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:23:26 EDT INFO  [main] --- 
2021-06-28 05:23:26 EDT DEBUG [main] --- Initializing Detect.
2021-06-28 05:23:26 EDT INFO  [main] --- Tilde's will be automatically resolved to USER HOME.
2021-06-28 05:23:26 EDT INFO  [main] --- Source directory: /home/<USER>/scripts/code
2021-06-28 05:23:26 EDT INFO  [main] --- Output directory: /home/<USER>/blackduck
2021-06-28 05:23:26 EDT INFO  [main] --- Run directory: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754
2021-06-28 05:23:26 EDT DEBUG [main] --- Main boot completed. Deciding what Detect should do.
2021-06-28 05:23:26 EDT INFO  [main] --- 
2021-06-28 05:23:27 EDT DEBUG [main] --- Black Duck will run: A Black Duck url was found.
2021-06-28 05:23:27 EDT DEBUG [main] --- Polaris will NOT run: The Polaris url must be provided.
2021-06-28 05:23:27 EDT DEBUG [main] --- Decided what products will be run. Starting product boot.
2021-06-28 05:23:27 EDT DEBUG [main] --- Detect product boot start.
2021-06-28 05:23:27 EDT DEBUG [main] --- Will boot Black Duck product.
2021-06-28 05:23:27 EDT DEBUG [main] --- Detect will check communication with the Black Duck server.
2021-06-28 05:23:27 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:23:28 EDT INFO  [main] --- A successful connection was made.
2021-06-28 05:23:28 EDT INFO  [main] --- Connection to the Black Duck server was successful.
2021-06-28 05:23:28 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:23:28 EDT INFO  [main] --- Successfully connected to Black Duck (version 2021.2.1)!
2021-06-28 05:23:28 EDT DEBUG [main] --- Connected as: sysadmin
2021-06-28 05:23:31 EDT DEBUG [main] --- Roles: System Administrator, Global Code Scanner, Policy Manager, License Manager, Project Manager, Project Viewer, Project Creator, Super User, Global Project Viewer, Copyright Editor, Component Manager, Global Security Manager
2021-06-28 05:23:31 EDT DEBUG [main] --- Group: 
2021-06-28 05:23:31 EDT DEBUG [main] --- Detect product boot completed.
2021-06-28 05:23:32 EDT DEBUG [main] --- Guessed Detect jar location as /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:23:32 EDT DEBUG [main] --- Checking for jar file: /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:23:32 EDT DEBUG [main] --- Found detect jar file.
2021-06-28 05:23:32 EDT DEBUG [main] --- Detect boot completed.
2021-06-28 05:23:32 EDT DEBUG [main] --- Detect will attempt to run.
2021-06-28 05:23:32 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:32 EDT INFO  [main] --- Polaris tools will not be run.
2021-06-28 05:23:32 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:32 EDT INFO  [main] --- Will include the Docker tool.
2021-06-28 05:23:32 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:23:32 EDT INFO  [main] --- Docker actions finished.
2021-06-28 05:23:32 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:32 EDT INFO  [main] --- Will include the Bazel tool.
2021-06-28 05:23:32 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:23:32 EDT INFO  [main] --- Bazel actions finished.
2021-06-28 05:23:32 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:32 EDT INFO  [main] --- Will include the detector tool.
2021-06-28 05:23:32 EDT DEBUG [main] --- Initializing detector system.
2021-06-28 05:23:32 EDT DEBUG [main] --- Starting detector file system traversal.
2021-06-28 05:23:32 EDT DEBUG [main] --- Traversing directory: /home/<USER>/scripts/code
2021-06-28 05:23:32 EDT INFO  [main] --- Searching for detectors. This may take a while.
2021-06-28 05:23:32 EDT DEBUG [main] --- 
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- Search results
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- No detectors found.
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- 
2021-06-28 05:23:32 EDT INFO  [main] --- 
2021-06-28 05:23:32 EDT DEBUG [main] --- Starting detector preparation.
2021-06-28 05:23:32 EDT DEBUG [main] --- Preparing detectors for discovery and extraction.
2021-06-28 05:23:32 EDT DEBUG [main] --- Counting detectors that will be evaluated.
2021-06-28 05:23:32 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:23:32 EDT DEBUG [main] --- Total number of detectors: 0
2021-06-28 05:23:32 EDT DEBUG [main] --- Starting detector project discovery.
2021-06-28 05:23:32 EDT DEBUG [main] --- 
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- Discovery results:
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- 
2021-06-28 05:23:32 EDT DEBUG [main] --- Starting detector extraction.
2021-06-28 05:23:32 EDT DEBUG [main] --- Finished detectors.
2021-06-28 05:23:32 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT INFO  [main] --- Detector Report
2021-06-28 05:23:32 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT INFO  [main] --- No detectors found.
2021-06-28 05:23:32 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:32 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:23:32 EDT DEBUG [main] --- Publishing file events.
2021-06-28 05:23:32 EDT DEBUG [main] --- Finished running detectors.
2021-06-28 05:23:32 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:23:32 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:23:32 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:23:32 EDT INFO  [main] --- Detector actions finished.
2021-06-28 05:23:32 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:32 EDT DEBUG [main] --- Completed code location tools.
2021-06-28 05:23:32 EDT DEBUG [main] --- Determining project info.
2021-06-28 05:23:32 EDT INFO  [main] --- Project name: project_101
2021-06-28 05:23:32 EDT INFO  [main] --- Project version: project_101_version
2021-06-28 05:23:32 EDT DEBUG [main] --- Black Duck tools will run.
2021-06-28 05:23:32 EDT DEBUG [main] --- Creating BDIO code locations.
2021-06-28 05:23:32 EDT DEBUG [main] --- Creating BDIO files from code locations.
2021-06-28 05:23:32 EDT DEBUG [main] --- 
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- Extraction results:
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:23:32 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:23:32 EDT DEBUG [main] --- 
2021-06-28 05:23:32 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:23:33 EDT DEBUG [main] --- Getting or creating project.
2021-06-28 05:23:33 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:23:33 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:23:33 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:23:33 EDT DEBUG [main] --- No clone project or version name supplied. Will not clone.
2021-06-28 05:23:33 EDT DEBUG [main] --- No 'Application ID' to set.
2021-06-28 05:23:33 EDT DEBUG [main] --- No custom fields to set.
2021-06-28 05:23:33 EDT DEBUG [main] --- Will not unmap code locations: Project view was not present, or should not unmap code locations.
2021-06-28 05:23:33 EDT DEBUG [main] --- Completed project and version actions.
2021-06-28 05:23:33 EDT DEBUG [main] --- Processing Detect Code Locations.
2021-06-28 05:23:33 EDT DEBUG [main] --- Did not create any BDIO files.
2021-06-28 05:23:33 EDT DEBUG [main] --- Completed Detect Code Location processing.
2021-06-28 05:23:33 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:23:33 EDT INFO  [main] --- Will include the signature scanner tool.
2021-06-28 05:23:33 EDT DEBUG [main] --- Determining signature scanner install directory...
2021-06-28 05:23:33 EDT DEBUG [main] --- Using default scanner path.
2021-06-28 05:23:33 EDT DEBUG [main] --- Determined install directory: /home/<USER>/blackduck/tools
2021-06-28 05:23:33 EDT DEBUG [main] --- Signature scanner will use the Black Duck server to download/update the scanner - this is the most likely situation.
2021-06-28 05:23:33 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:23:33 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:23:33 EDT DEBUG [main] --- Signature scan is online.
2021-06-28 05:23:34 EDT INFO  [main] --- No scan targets provided - registering the source path /home/<USER>/scripts/code to scan
2021-06-28 05:23:34 EDT DEBUG [main] --- The directory structure was likely created by the installer
2021-06-28 05:23:34 EDT DEBUG [main] --- last time downloaded: 1617070363000
2021-06-28 05:23:34 EDT DEBUG [main] --- Last modified on server: 1617070363000
2021-06-28 05:23:34 EDT DEBUG [main] --- The request has not been modified since it was last checked - skipping.
2021-06-28 05:23:34 EDT DEBUG [main] --- The Black Duck Signature Scanner has not been modified since it was last downloaded - skipping download.
2021-06-28 05:23:34 EDT INFO  [main] --- The Black Duck Signature Scanner downloaded/found successfully: /home/<USER>/blackduck/tools
2021-06-28 05:23:34 EDT INFO  [main] --- Starting the Black Duck Signature Scan commands.
2021-06-28 05:23:34 EDT DEBUG [pool-3-thread-1] --- The directory structure was likely created by the installer
2021-06-28 05:23:34 EDT DEBUG [pool-3-thread-1] --- Using this java installation : /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java
2021-06-28 05:23:34 EDT DEBUG [pool-3-thread-1] --- Using the Black Duck hostname : 'test-yuan.app.blackduck.com'
2021-06-28 05:23:34 EDT INFO  [pool-3-thread-1] --- Black Duck CLI command: /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java -Done-jar.silent=true -Done-jar.jar.path=/home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/cache/scan.cli.impl-standalone.jar -Xmx4096m -jar /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/scan.cli-2021.2.1-standalone.jar --no-prompt --scheme https --host test-yuan.app.blackduck.com --username sysadmin --port 443 --insecure --snippet-matching --upload-source -v --logDir /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1 --statusWriteDir /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1 --project project_101 --release project_101_version --name account_100 scan /home/<USER>/scripts/code
2021-06-28 05:23:35 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start wrapper: ScanCliWrapperSettings [commandLine=org.apache.commons.cli.CommandLine@a1f72f5, fileUriSet=null, scheme=https, host=test-yuan.app.blackduck.com, port=443]...
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start scan loop: ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, --project, project_101, --release, project_101_version, --name, account_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[account_100 scan], project=Optional[project_101], release=Optional[project_101_version], username=sysadmin, password=<NOT SHOWN>]...
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Individual File Matching Detected: none...
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized sha1 white list: []...
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized clean sha1 white list: []...
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Initialize client for test-yuan.app.blackduck.com:443
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/scan-summaries
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scanstatus
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans/signatureVersion
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/scan-candidates?scanCandidatesParam={scanCandidatesParam}&limit={limit}&offset={offset}
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/fp-import/scans/{scanId}
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/projects?q=name:{projname}&limit={limit}&offset={offset}
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/scan-sources/{sha1}
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/bulk-sources
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/uploaded-file-data
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search-matches
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-vsls
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-family-vsls
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/global-settings/{settingName}
2021-06-28 05:23:36 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Execute scan file://sup-cn-hub05/home/<USER>/scripts/code....
2021-06-28 05:23:37 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to false
2021-06-28 05:23:37 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 0 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:23:37 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Scan...
2021-06-28 05:23:38 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: ScanExecResult: ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, --project, project_101, --release, project_101_version, --name, account_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[account_100 scan], project=Optional[project_101], release=Optional[project_101_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=ScanContainerView{scanId=a7cf6a90-02ab-482c-a564-316b6bee3ab5, createdOn=2021-06-28T09:23:36.600Z, timeToScan=256, scannerVersion=2021.2.1, signatureVersion=7.0.0, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#sup-cn-hub05-code, baseDir=/home/<USER>/scripts/code, name=Optional[account_100 scan], project=Optional[project_101], release=Optional[project_101_version], scanProblem=null, scanProblemList.size()=0, scanNodeList.size()=5}, fpScanContainer=null, stringSearchScanContainer=null, scanResult=ScanView{id=a7cf6a90-02ab-482c-a564-316b6bee3ab5, scannerVersion=2021.2.1, signatureVersion=7.0.0, name=account_100 scan, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#a7cf6a90-02ab-482c-a564-316b6bee3ab5, baseDir=/, createdOn=2021-06-28T09:23:36.600Z, lastModifiedOn=2021-06-28T09:23:37.276Z, timeToScan=0, createdByUserId=********-0000-0000-0001-********0001, matchCount=0, numDirs=0, numNonDirFiles=0, status=SCANNING, deepSystemSize=Optional.empty, scanSourceType=SN, scanSourceId=a7cf6a90-02ab-482c-a564-316b6bee3ab5, timeLastModified=*************, timeToPersistMs=0, scanTime=*************, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=SCANNING, createdAt=2021-06-28T09:23:36.600Z, updatedAt=2021-06-28T09:23:37.276Z, createdByUserName=Optional[sysadmin], scanSize=Optional.empty, serverVersion=2021.2.1, matchCount=0, directoryCount=Optional[0], fileCount=Optional[0], baseDirectory=/, hostName=sup-cn-hub05, scanType=FS, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/a7cf6a90-02ab-482c-a564-316b6bee3ab5, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/48677481-0600-4770-9e08-9bd9f770494c}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/a7cf6a90-02ab-482c-a564-316b6bee3ab5/bom-entries}]}}, scanDate=Mon Jun 28 05:23:36 EDT 2021, scanEndDate=Mon Jun 28 05:23:37 EDT 2021, scanCreated=true]
2021-06-28 05:23:38 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Checking FS scan status before running snippet scan: a7cf6a90-02ab-482c-a564-316b6bee3ab5
2021-06-28 05:24:08 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FS scan a7cf6a90-02ab-482c-a564-316b6bee3ab5 was found, scan status is COMPLETE
2021-06-28 05:24:08 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Starting calculation of fingerprints for snippet scan files...
2021-06-28 05:24:08 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to true
2021-06-28 05:24:08 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:24:09 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:24:09 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 0 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:24:09 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Fingerprint Scan...
2021-06-28 05:24:10 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FingerprintScanExecResult : ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, --project, project_101, --release, project_101_version, --name, account_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[account_100 scan], project=Optional[project_101], release=Optional[project_101_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=null, fpScanContainer=FingerprintScanContainerView{snippetMatchingAllSource=false, fullSnippetScan=false, scanProblem=null, scanProblemList.size()=0, fingerprintScanNodeList.size()=2}, stringSearchScanContainer=null, scanResult=ScanView{id=9257afac-5015-4b37-8efe-b81864b1f97f, matchCount=0, numDirs=0, numNonDirFiles=0, status=UNSTARTED, deepSystemSize=Optional.empty, timeLastModified=0, timeToPersistMs=0, scanTime=0, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=SCANNING, createdAt=2021-06-28T09:24:08.266Z, updatedAt=2021-06-28T09:24:09.757Z, createdByUserName=Optional[sysadmin], scanSize=Optional[82958], serverVersion=2021.2.1, matchCount=0, directoryCount=Optional[0], fileCount=Optional[2], baseDirectory=/home/<USER>/scripts/code, hostName=sup-cn-hub05, scanType=SNIPPET, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/9257afac-5015-4b37-8efe-b81864b1f97f, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/48677481-0600-4770-9e08-9bd9f770494c}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/9257afac-5015-4b37-8efe-b81864b1f97f/bom-entries}]}}, scanDate=Mon Jun 28 05:24:08 EDT 2021, scanEndDate=Mon Jun 28 05:24:10 EDT 2021, scanCreated=true]
2021-06-28 05:24:10 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1/log/sup-cn-hub05-code-2021-06-28T092336.600Z.log
2021-06-28 05:24:10 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Logging to file: 
Logging to file: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1/log/sup-cn-hub05-code-2021-06-28T092336.600Z.log
2021-06-28 05:24:10 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1/status/sup-cn-hub05-code-2021-06-28T092336.600Z.json
2021-06-28 05:24:10 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Persist ScanSummary to file...
2021-06-28 05:24:10 EDT DEBUG [pool-3-thread-1] --- INFO: Persisted status: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1/status/sup-cn-hub05-code-2021-06-28T092336.600Z.json
2021-06-28 05:24:10 EDT INFO  [pool-3-thread-1] --- 
2021-06-28 05:24:10 EDT INFO  [pool-3-thread-1] --- Black Duck Signature Scanner return code: 0
2021-06-28 05:24:10 EDT INFO  [pool-3-thread-1] --- You can view the logs at: '/home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan/BlackDuckScanOutput/2021-06-28_09-23-34-326_1'
2021-06-28 05:24:10 EDT INFO  [main] --- Completed the Black Duck Signature Scan commands.
2021-06-28 05:24:10 EDT INFO  [main] --- Signature scanner actions finished.
2021-06-28 05:24:10 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:24:10 EDT INFO  [main] --- Will include the binary scanner tool.
2021-06-28 05:24:10 EDT INFO  [main] --- Binary scanner actions finished.
2021-06-28 05:24:10 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:24:10 EDT INFO  [main] --- Vulnerability Impact Analysis tool will not be run.
2021-06-28 05:24:10 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:24:10 EDT INFO  [main] --- Will perform Black Duck post actions.
2021-06-28 05:24:10 EDT INFO  [main] --- Detect must wait for bom tool calculations to finish.
2021-06-28 05:24:11 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:24:11 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:24:11 EDT INFO  [main] --- Found account_100 scan code location (1 of 2).
2021-06-28 05:24:11 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:24:11 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:24:11 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:24:11 EDT INFO  [main] --- Found account_100 scan code location (1 of 2).
2021-06-28 05:24:11 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:24:11 EDT INFO  [main] --- Try #1 (elapsed: 00:00:00.000)...not done yet, waiting 60 seconds and trying again...
2021-06-28 05:25:12 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:25:12 EDT DEBUG [main] --- There were 2 notifications found.
2021-06-28 05:25:12 EDT INFO  [main] --- Found account_100 scan code location (1 of 2).
2021-06-28 05:25:12 EDT INFO  [main] --- Found account_100 scan code location (2 of 2).
2021-06-28 05:25:12 EDT INFO  [main] --- Try #2 (elapsed: 00:01:01.409)...complete!
2021-06-28 05:25:12 EDT INFO  [main] --- All code locations have been added to the BOM.
2021-06-28 05:25:12 EDT INFO  [main] --- Black Duck actions have finished.
2021-06-28 05:25:12 EDT INFO  [main] --- All tools have finished.
2021-06-28 05:25:12 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- Creating status file: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/status/status.json
2021-06-28 05:25:12 EDT DEBUG [main] --- Detect shutdown begin.
2021-06-28 05:25:12 EDT DEBUG [main] --- Ending phone home.
2021-06-28 05:25:12 EDT DEBUG [main] --- Detect will cleanup.
2021-06-28 05:25:12 EDT DEBUG [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754
2021-06-28 05:25:12 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/status
2021-06-28 05:25:12 EDT INFO  [main] --- Status file has been deleted.  To preserve status file, turn off cleanup actions.
2021-06-28 05:25:12 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/bdio
2021-06-28 05:25:12 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754/scan
2021-06-28 05:25:12 EDT INFO  [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-23-26-754
2021-06-28 05:25:12 EDT DEBUG [main] --- Detect shutdown completed.
2021-06-28 05:25:12 EDT DEBUG [main] --- All Detect actions completed.
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- ======== Detect Issues ========
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- DEPRECATIONS:
2021-06-28 05:25:12 EDT INFO  [main] --- 	blackduck.username
2021-06-28 05:25:12 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:25:12 EDT INFO  [main] --- 	blackduck.password
2021-06-28 05:25:12 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- ======== Detect Result ========
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- Black Duck Project BOM: https://test-yuan.app.blackduck.com/api/projects/b74874bd-65be-4bbd-a479-6c666958b69a/versions/c3cd7b95-929b-47ea-a0a7-b074fd4d4f01/components
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- ======== Detect Status ========
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- Signature scan / Snippet scan on /home/<USER>/scripts/code: SUCCESS
2021-06-28 05:25:12 EDT INFO  [main] --- Overall Status: SUCCESS - Detect exited successfully.
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- ===============================
2021-06-28 05:25:12 EDT INFO  [main] --- 
2021-06-28 05:25:12 EDT INFO  [main] --- Detect duration: 00h 01m 46s 157ms

______     _            _
|  _  \   | |          | |
| | | |___| |_ ___  ___| |_
| | | / _ \ __/ _ \/ __| __|
| |/ /  __/ ||  __/ (__| |_
|___/ \___|\__\___|\___|\__|

2021-06-28 05:25:14 EDT DEBUG [main] --- Initializing detect.
2021-06-28 05:25:14 EDT DEBUG [main] --- You seem to be running in a LINUX operating system.
2021-06-28 05:25:14 EDT DEBUG [main] --- Detect boot begin.

Detect Version: 6.9.1

2021-06-28 05:25:14 EDT DEBUG [main] --- Configuration processed completely.
2021-06-28 05:25:14 EDT INFO  [main] --- 
2021-06-28 05:25:14 EDT INFO  [main] --- Current property values:
2021-06-28 05:25:14 EDT INFO  [main] --- --property = value [notes]
2021-06-28 05:25:14 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:25:14 EDT INFO  [main] --- blackduck.password = **************** [cmd] 	 *** DEPRECATED ***
2021-06-28 05:25:14 EDT INFO  [main] --- blackduck.trust.cert = true [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- blackduck.url = https://test-yuan.app.blackduck.com/ [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- blackduck.username = sysadmin [cmd] 	 *** DEPRECATED ***
2021-06-28 05:25:14 EDT INFO  [main] --- detect.blackduck.signature.scanner.snippet.matching = SNIPPET_MATCHING [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- detect.blackduck.signature.scanner.upload.source.mode = true [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- detect.code.location.name = api_100 [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- detect.project.name = project_101 [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- detect.project.version.name = project_101_version [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- detect.timeout = 600 [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- detect.wait.for.results = true [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- logging.level.com.synopsys.integration = DEBUG [cmd] 
2021-06-28 05:25:14 EDT INFO  [main] --- ------------------------------------------------------------
2021-06-28 05:25:14 EDT INFO  [main] --- 
2021-06-28 05:25:14 EDT DEBUG [main] --- Initializing Detect.
2021-06-28 05:25:14 EDT INFO  [main] --- Tilde's will be automatically resolved to USER HOME.
2021-06-28 05:25:14 EDT INFO  [main] --- Source directory: /home/<USER>/scripts/code
2021-06-28 05:25:14 EDT INFO  [main] --- Output directory: /home/<USER>/blackduck
2021-06-28 05:25:14 EDT INFO  [main] --- Run directory: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589
2021-06-28 05:25:14 EDT DEBUG [main] --- Main boot completed. Deciding what Detect should do.
2021-06-28 05:25:14 EDT INFO  [main] --- 
2021-06-28 05:25:15 EDT DEBUG [main] --- Black Duck will run: A Black Duck url was found.
2021-06-28 05:25:15 EDT DEBUG [main] --- Polaris will NOT run: The Polaris url must be provided.
2021-06-28 05:25:15 EDT DEBUG [main] --- Decided what products will be run. Starting product boot.
2021-06-28 05:25:15 EDT DEBUG [main] --- Detect product boot start.
2021-06-28 05:25:15 EDT DEBUG [main] --- Will boot Black Duck product.
2021-06-28 05:25:15 EDT DEBUG [main] --- Detect will check communication with the Black Duck server.
2021-06-28 05:25:15 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:25:16 EDT INFO  [main] --- A successful connection was made.
2021-06-28 05:25:16 EDT INFO  [main] --- Connection to the Black Duck server was successful.
2021-06-28 05:25:16 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:25:16 EDT INFO  [main] --- Successfully connected to Black Duck (version 2021.2.1)!
2021-06-28 05:25:16 EDT DEBUG [main] --- Connected as: sysadmin
2021-06-28 05:25:20 EDT DEBUG [main] --- Roles: System Administrator, Global Code Scanner, Policy Manager, License Manager, Project Manager, Project Viewer, Project Creator, Super User, Global Project Viewer, Copyright Editor, Component Manager, Global Security Manager
2021-06-28 05:25:20 EDT DEBUG [main] --- Group: 
2021-06-28 05:25:20 EDT DEBUG [main] --- Detect product boot completed.
2021-06-28 05:25:20 EDT DEBUG [main] --- Guessed Detect jar location as /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:25:20 EDT DEBUG [main] --- Checking for jar file: /home/<USER>/detect/synopsys-detect-6.9.1.jar
2021-06-28 05:25:20 EDT DEBUG [main] --- Found detect jar file.
2021-06-28 05:25:20 EDT DEBUG [main] --- Detect boot completed.
2021-06-28 05:25:20 EDT DEBUG [main] --- Detect will attempt to run.
2021-06-28 05:25:20 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:20 EDT INFO  [main] --- Polaris tools will not be run.
2021-06-28 05:25:20 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:20 EDT INFO  [main] --- Will include the Docker tool.
2021-06-28 05:25:20 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:25:20 EDT INFO  [main] --- Docker actions finished.
2021-06-28 05:25:20 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:20 EDT INFO  [main] --- Will include the Bazel tool.
2021-06-28 05:25:20 EDT DEBUG [main] --- Was not applicable.
2021-06-28 05:25:20 EDT INFO  [main] --- Bazel actions finished.
2021-06-28 05:25:20 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:20 EDT INFO  [main] --- Will include the detector tool.
2021-06-28 05:25:20 EDT DEBUG [main] --- Initializing detector system.
2021-06-28 05:25:20 EDT DEBUG [main] --- Starting detector file system traversal.
2021-06-28 05:25:20 EDT DEBUG [main] --- Traversing directory: /home/<USER>/scripts/code
2021-06-28 05:25:20 EDT INFO  [main] --- Searching for detectors. This may take a while.
2021-06-28 05:25:20 EDT DEBUG [main] --- 
2021-06-28 05:25:20 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT DEBUG [main] --- Search results
2021-06-28 05:25:20 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT DEBUG [main] --- No detectors found.
2021-06-28 05:25:20 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT DEBUG [main] --- 
2021-06-28 05:25:20 EDT INFO  [main] --- 
2021-06-28 05:25:20 EDT DEBUG [main] --- Starting detector preparation.
2021-06-28 05:25:20 EDT DEBUG [main] --- Preparing detectors for discovery and extraction.
2021-06-28 05:25:20 EDT DEBUG [main] --- Counting detectors that will be evaluated.
2021-06-28 05:25:20 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:25:20 EDT DEBUG [main] --- Total number of detectors: 0
2021-06-28 05:25:20 EDT DEBUG [main] --- Starting detector project discovery.
2021-06-28 05:25:20 EDT DEBUG [main] --- 
2021-06-28 05:25:20 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT DEBUG [main] --- Discovery results:
2021-06-28 05:25:20 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:25:20 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:25:20 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT DEBUG [main] --- 
2021-06-28 05:25:20 EDT DEBUG [main] --- Starting detector extraction.
2021-06-28 05:25:20 EDT DEBUG [main] --- Finished detectors.
2021-06-28 05:25:20 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT INFO  [main] --- Detector Report
2021-06-28 05:25:20 EDT INFO  [main] --- ======================================================================================================
2021-06-28 05:25:20 EDT INFO  [main] --- No detectors found.
2021-06-28 05:25:20 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:20 EDT DEBUG [main] --- Publishing file events.
2021-06-28 05:25:20 EDT DEBUG [main] --- Finished running detectors.
2021-06-28 05:25:20 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:25:21 EDT INFO  [main] --- Detector actions finished.
2021-06-28 05:25:21 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:21 EDT DEBUG [main] --- Completed code location tools.
2021-06-28 05:25:21 EDT DEBUG [main] --- Determining project info.
2021-06-28 05:25:21 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:25:21 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:25:21 EDT INFO  [main] --- Project name: project_101
2021-06-28 05:25:21 EDT INFO  [main] --- Project version: project_101_version
2021-06-28 05:25:21 EDT DEBUG [main] --- Black Duck tools will run.
2021-06-28 05:25:21 EDT DEBUG [main] --- Creating BDIO code locations.
2021-06-28 05:25:21 EDT DEBUG [main] --- Creating BDIO files from code locations.
2021-06-28 05:25:21 EDT DEBUG [main] --- 
2021-06-28 05:25:21 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:21 EDT DEBUG [main] --- Extraction results:
2021-06-28 05:25:21 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:21 EDT DEBUG [main] --- There were no extractions to be summarized - no code locations were generated or no detectors were evaluated.
2021-06-28 05:25:21 EDT DEBUG [main] --- ======================================================================================================
2021-06-28 05:25:21 EDT DEBUG [main] --- 
2021-06-28 05:25:21 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:25:21 EDT DEBUG [main] --- Getting or creating project.
2021-06-28 05:25:21 EDT DEBUG [pool-2-thread-1] --- starting phone home
2021-06-28 05:25:21 EDT DEBUG [pool-2-thread-1] --- Phoning home to https://www.google-analytics.com/collect
2021-06-28 05:25:21 EDT DEBUG [pool-2-thread-1] --- completed phone home
2021-06-28 05:25:21 EDT DEBUG [main] --- No clone project or version name supplied. Will not clone.
2021-06-28 05:25:22 EDT DEBUG [main] --- No 'Application ID' to set.
2021-06-28 05:25:22 EDT DEBUG [main] --- No custom fields to set.
2021-06-28 05:25:22 EDT DEBUG [main] --- Will not unmap code locations: Project view was not present, or should not unmap code locations.
2021-06-28 05:25:22 EDT DEBUG [main] --- Completed project and version actions.
2021-06-28 05:25:22 EDT DEBUG [main] --- Processing Detect Code Locations.
2021-06-28 05:25:22 EDT DEBUG [main] --- Did not create any BDIO files.
2021-06-28 05:25:22 EDT DEBUG [main] --- Completed Detect Code Location processing.
2021-06-28 05:25:22 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:22 EDT INFO  [main] --- Will include the signature scanner tool.
2021-06-28 05:25:22 EDT DEBUG [main] --- Determining signature scanner install directory...
2021-06-28 05:25:22 EDT DEBUG [main] --- Using default scanner path.
2021-06-28 05:25:22 EDT DEBUG [main] --- Determined install directory: /home/<USER>/blackduck/tools
2021-06-28 05:25:22 EDT DEBUG [main] --- Signature scanner will use the Black Duck server to download/update the scanner - this is the most likely situation.
2021-06-28 05:25:22 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:25:22 EDT ERROR [main] --- Automatically trusting server certificates - not recommended for production use.
2021-06-28 05:25:22 EDT DEBUG [main] --- Signature scan is online.
2021-06-28 05:25:22 EDT INFO  [main] --- No scan targets provided - registering the source path /home/<USER>/scripts/code to scan
2021-06-28 05:25:22 EDT DEBUG [main] --- The directory structure was likely created by the installer
2021-06-28 05:25:22 EDT DEBUG [main] --- last time downloaded: 1617070363000
2021-06-28 05:25:22 EDT DEBUG [main] --- Last modified on server: 1617070363000
2021-06-28 05:25:22 EDT DEBUG [main] --- The request has not been modified since it was last checked - skipping.
2021-06-28 05:25:22 EDT DEBUG [main] --- The Black Duck Signature Scanner has not been modified since it was last downloaded - skipping download.
2021-06-28 05:25:22 EDT INFO  [main] --- The Black Duck Signature Scanner downloaded/found successfully: /home/<USER>/blackduck/tools
2021-06-28 05:25:22 EDT INFO  [main] --- Starting the Black Duck Signature Scan commands.
2021-06-28 05:25:22 EDT DEBUG [pool-3-thread-1] --- The directory structure was likely created by the installer
2021-06-28 05:25:22 EDT DEBUG [pool-3-thread-1] --- Using this java installation : /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java
2021-06-28 05:25:22 EDT DEBUG [pool-3-thread-1] --- Using the Black Duck hostname : 'test-yuan.app.blackduck.com'
2021-06-28 05:25:22 EDT INFO  [pool-3-thread-1] --- Black Duck CLI command: /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/jre/bin/java -Done-jar.silent=true -Done-jar.jar.path=/home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/cache/scan.cli.impl-standalone.jar -Xmx4096m -jar /home/<USER>/blackduck/tools/Black_Duck_Scan_Installation/scan.cli-2021.2.1/lib/scan.cli-2021.2.1-standalone.jar --no-prompt --scheme https --host test-yuan.app.blackduck.com --username sysadmin --port 443 --insecure --snippet-matching --upload-source -v --logDir /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1 --statusWriteDir /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1 --project project_101 --release project_101_version --name api_100 scan /home/<USER>/scripts/code
2021-06-28 05:25:24 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start wrapper: ScanCliWrapperSettings [commandLine=org.apache.commons.cli.CommandLine@a1f72f5, fileUriSet=null, scheme=https, host=test-yuan.app.blackduck.com, port=443]...
2021-06-28 05:25:24 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Start scan loop: ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, --project, project_101, --release, project_101_version, --name, api_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[api_100 scan], project=Optional[project_101], release=Optional[project_101_version], username=sysadmin, password=<NOT SHOWN>]...
2021-06-28 05:25:24 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Individual File Matching Detected: none...
2021-06-28 05:25:24 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized sha1 white list: []...
2021-06-28 05:25:24 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Finalized clean sha1 white list: []...
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Initialize client for test-yuan.app.blackduck.com:443
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/scan-summaries
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = {scanId}, URI = https://test-yuan.app.blackduck.com:443/api/v1/scanstatus
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/scans/signatureVersion
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/scan-candidates?scanCandidatesParam={scanCandidatesParam}&limit={limit}&offset={offset}
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/fp-import/scans/{scanId}
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/projects?q=name:{projname}&limit={limit}&offset={offset}
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/scan-sources/{sha1}
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/bulk-sources
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/uploaded-file-data
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search-matches
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/scans/{scanId}/string-search
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-vsls
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/internal/license-family-vsls
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: RestResourceClient created. SingleRelativePath = null, URI = https://test-yuan.app.blackduck.com:443/api/v1/global-settings/{settingName}
2021-06-28 05:25:25 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Execute scan file://sup-cn-hub05/home/<USER>/scripts/code....
2021-06-28 05:25:26 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to false
2021-06-28 05:25:26 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 0 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:25:26 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Scan...
2021-06-28 05:25:26 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: ScanExecResult: ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, --project, project_101, --release, project_101_version, --name, api_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[api_100 scan], project=Optional[project_101], release=Optional[project_101_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=ScanContainerView{scanId=e3229baf-43d8-4297-8203-8986331837ad, createdOn=2021-06-28T09:25:25.227Z, timeToScan=250, scannerVersion=2021.2.1, signatureVersion=7.0.0, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#sup-cn-hub05-code, baseDir=/home/<USER>/scripts/code, name=Optional[api_100 scan], project=Optional[project_101], release=Optional[project_101_version], scanProblem=null, scanProblemList.size()=0, scanNodeList.size()=5}, fpScanContainer=null, stringSearchScanContainer=null, scanResult=ScanView{id=e3229baf-43d8-4297-8203-8986331837ad, scannerVersion=2021.2.1, signatureVersion=7.0.0, name=api_100 scan, hostName=sup-cn-hub05, ownerEntityKeyToken=SN#e3229baf-43d8-4297-8203-8986331837ad, baseDir=/, createdOn=2021-06-28T09:25:25.227Z, lastModifiedOn=2021-06-28T09:25:26.046Z, timeToScan=0, createdByUserId=********-0000-0000-0001-********0001, matchCount=0, numDirs=0, numNonDirFiles=0, status=SCANNING, deepSystemSize=Optional.empty, scanSourceType=SN, scanSourceId=e3229baf-43d8-4297-8203-8986331837ad, timeLastModified=1624872326046, timeToPersistMs=0, scanTime=1624872325227, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=SCANNING, createdAt=2021-06-28T09:25:25.227Z, updatedAt=2021-06-28T09:25:26.046Z, createdByUserName=Optional[sysadmin], scanSize=Optional.empty, serverVersion=2021.2.1, matchCount=0, directoryCount=Optional[0], fileCount=Optional[0], baseDirectory=/, hostName=sup-cn-hub05, scanType=FS, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/e3229baf-43d8-4297-8203-8986331837ad, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/9fe20bf7-d118-4437-b1ec-b38bd40343dd}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/e3229baf-43d8-4297-8203-8986331837ad/bom-entries}]}}, scanDate=Mon Jun 28 05:25:25 EDT 2021, scanEndDate=Mon Jun 28 05:25:26 EDT 2021, scanCreated=true]
2021-06-28 05:25:26 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Checking FS scan status before running snippet scan: e3229baf-43d8-4297-8203-8986331837ad
2021-06-28 05:25:57 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FS scan e3229baf-43d8-4297-8203-8986331837ad was found, scan status is COMPLETE
2021-06-28 05:25:57 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Starting calculation of fingerprints for snippet scan files...
2021-06-28 05:25:57 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Scan engine initialized with niceness set to true
2021-06-28 05:25:58 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:25:58 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: 2 of unmatched scan candidates are found
2021-06-28 05:25:58 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Total time: 0 seconds to scan path: /home/<USER>/scripts/code
2021-06-28 05:25:58 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Post Fingerprint Scan...
2021-06-28 05:25:59 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: FingerprintScanExecResult : ScanExecResult [scanClientSettings=ScanClientSettings [commandLine=[--no-prompt, --scheme, https, --host, test-yuan.app.blackduck.com, --username, sysadmin, --port, 443, --insecure, --snippet-matching, --upload-source, -v, --logDir, /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, --statusWriteDir, /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, --project, project_101, --release, project_101_version, --name, api_100 scan, /home/<USER>/scripts/code], fileUriSet=[file://sup-cn-hub05/home/<USER>/scripts/code], dryRunWriteDir=No dry run file., dryRunReadFile=No dry run file., fsScanReadyWaitingPeriod=30, snippetMatching=true, snippetMatchingOnly=false, snippetMatchingAllSource=false, fullSnippetScan=false, uploadSource=true, individualFileMatching=none, licenseSearch=false, copyrightSearch=false, maxRequestBodySize=********, maxUpdateSize=10000, logDir=/home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1, scheme=https, host=test-yuan.app.blackduck.com, port=443, name=Optional[api_100 scan], project=Optional[project_101], release=Optional[project_101_version], username=sysadmin, password=<NOT SHOWN>], result=0, dataFileName=null, scanContainer=null, fpScanContainer=FingerprintScanContainerView{snippetMatchingAllSource=false, fullSnippetScan=false, scanProblem=null, scanProblemList.size()=0, fingerprintScanNodeList.size()=2}, stringSearchScanContainer=null, scanResult=ScanView{id=f90021a3-d270-424f-ae14-9fb3aa8c9d7a, matchCount=0, numDirs=0, numNonDirFiles=0, status=UNSTARTED, deepSystemSize=Optional.empty, timeLastModified=0, timeToPersistMs=0, scanTime=0, arguments={managedCodebaseSize=***********, managedCodeBaseLimit=9223372036854775807, codeLocationBytesLimit=9223372036854775807, managedCodeBaseSoftLimit=9223372036854775807}}, scanSummary=ScanSummaryView{status=MATCHING, createdAt=2021-06-28T09:25:57.270Z, updatedAt=2021-06-28T09:25:59.296Z, createdByUserName=Optional[sysadmin], scanSize=Optional[82958], serverVersion=2021.2.1, matchCount=2, directoryCount=Optional[0], fileCount=Optional[2], baseDirectory=/home/<USER>/scripts/code, hostName=sup-cn-hub05, scanType=SNIPPET, resourceMetadata=ResourceMetadata{allow=[GET], href=https://test-yuan.app.blackduck.com/api/scan-summaries/f90021a3-d270-424f-ae14-9fb3aa8c9d7a, links=[ResourceLink{rel=codelocation, href=https://test-yuan.app.blackduck.com/api/codelocations/9fe20bf7-d118-4437-b1ec-b38bd40343dd}, ResourceLink{rel=scan-bom-entries, href=https://test-yuan.app.blackduck.com/api/scan/f90021a3-d270-424f-ae14-9fb3aa8c9d7a/bom-entries}]}}, scanDate=Mon Jun 28 05:25:57 EDT 2021, scanEndDate=Mon Jun 28 05:25:59 EDT 2021, scanCreated=true]
2021-06-28 05:25:59 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1/log/sup-cn-hub05-code-2021-06-28T092525.227Z.log
2021-06-28 05:25:59 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Logging to file: 
Logging to file: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1/log/sup-cn-hub05-code-2021-06-28T092525.227Z.log
2021-06-28 05:25:59 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Creating data output file: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1/status/sup-cn-hub05-code-2021-06-28T092525.227Z.json
2021-06-28 05:25:59 EDT DEBUG [pool-3-thread-1-Stream Redirect Thread] --- INFO: Persist ScanSummary to file...
2021-06-28 05:25:59 EDT DEBUG [pool-3-thread-1] --- INFO: Persisted status: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1/status/sup-cn-hub05-code-2021-06-28T092525.227Z.json
2021-06-28 05:25:59 EDT INFO  [pool-3-thread-1] --- 
2021-06-28 05:25:59 EDT INFO  [pool-3-thread-1] --- Black Duck Signature Scanner return code: 0
2021-06-28 05:25:59 EDT INFO  [pool-3-thread-1] --- You can view the logs at: '/home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan/BlackDuckScanOutput/2021-06-28_09-25-22-688_1'
2021-06-28 05:25:59 EDT INFO  [main] --- Completed the Black Duck Signature Scan commands.
2021-06-28 05:25:59 EDT INFO  [main] --- Signature scanner actions finished.
2021-06-28 05:25:59 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:59 EDT INFO  [main] --- Will include the binary scanner tool.
2021-06-28 05:25:59 EDT INFO  [main] --- Binary scanner actions finished.
2021-06-28 05:25:59 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:59 EDT INFO  [main] --- Vulnerability Impact Analysis tool will not be run.
2021-06-28 05:25:59 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:25:59 EDT INFO  [main] --- Will perform Black Duck post actions.
2021-06-28 05:25:59 EDT INFO  [main] --- Detect must wait for bom tool calculations to finish.
2021-06-28 05:26:00 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:26:00 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:26:00 EDT INFO  [main] --- Found api_100 scan code location (1 of 2).
2021-06-28 05:26:00 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:26:01 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:26:01 EDT DEBUG [main] --- There were 1 notifications found.
2021-06-28 05:26:01 EDT INFO  [main] --- Found api_100 scan code location (1 of 2).
2021-06-28 05:26:01 EDT INFO  [main] --- All code locations have not been added to the BOM yet...
2021-06-28 05:26:01 EDT INFO  [main] --- Try #1 (elapsed: 00:00:00.000)...not done yet, waiting 60 seconds and trying again...
2021-06-28 05:27:01 EDT DEBUG [main] --- At least one code location has been found, now looking for notifications.
2021-06-28 05:27:01 EDT DEBUG [main] --- There were 2 notifications found.
2021-06-28 05:27:01 EDT INFO  [main] --- Found api_100 scan code location (1 of 2).
2021-06-28 05:27:01 EDT INFO  [main] --- Found api_100 scan code location (2 of 2).
2021-06-28 05:27:01 EDT INFO  [main] --- Try #2 (elapsed: 00:01:01.418)...complete!
2021-06-28 05:27:01 EDT INFO  [main] --- All code locations have been added to the BOM.
2021-06-28 05:27:01 EDT INFO  [main] --- Black Duck actions have finished.
2021-06-28 05:27:01 EDT INFO  [main] --- All tools have finished.
2021-06-28 05:27:01 EDT INFO  [main] --- ----------------------------------
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- Creating status file: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/status/status.json
2021-06-28 05:27:01 EDT DEBUG [main] --- Detect shutdown begin.
2021-06-28 05:27:01 EDT DEBUG [main] --- Ending phone home.
2021-06-28 05:27:01 EDT DEBUG [main] --- Detect will cleanup.
2021-06-28 05:27:01 EDT DEBUG [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589
2021-06-28 05:27:01 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/status
2021-06-28 05:27:01 EDT INFO  [main] --- Status file has been deleted.  To preserve status file, turn off cleanup actions.
2021-06-28 05:27:01 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/bdio
2021-06-28 05:27:01 EDT DEBUG [main] --- Cleaning up: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589/scan
2021-06-28 05:27:01 EDT INFO  [main] --- Cleaning up directory: /home/<USER>/blackduck/runs/2021-06-28-09-25-14-589
2021-06-28 05:27:01 EDT DEBUG [main] --- Detect shutdown completed.
2021-06-28 05:27:01 EDT DEBUG [main] --- All Detect actions completed.
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- ======== Detect Issues ========
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- DEPRECATIONS:
2021-06-28 05:27:01 EDT INFO  [main] --- 	blackduck.username
2021-06-28 05:27:01 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:27:01 EDT INFO  [main] --- 	blackduck.password
2021-06-28 05:27:01 EDT INFO  [main] --- 		This property is being removed. Please use blackduck.api.token in the future. It will cause failure in 7.0.0 and be removed in 8.0.0.
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- ======== Detect Result ========
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- Black Duck Project BOM: https://test-yuan.app.blackduck.com/api/projects/b74874bd-65be-4bbd-a479-6c666958b69a/versions/c3cd7b95-929b-47ea-a0a7-b074fd4d4f01/components
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- ======== Detect Status ========
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- Signature scan / Snippet scan on /home/<USER>/scripts/code: SUCCESS
2021-06-28 05:27:01 EDT INFO  [main] --- Overall Status: SUCCESS - Detect exited successfully.
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- ===============================
2021-06-28 05:27:01 EDT INFO  [main] --- 
2021-06-28 05:27:01 EDT INFO  [main] --- Detect duration: 00h 01m 47s 417ms

