import os
import re
import json
import subprocess
from urllib.parse import urljoin

import requests
from blackduck.HubRestApi import <PERSON><PERSON><PERSON><PERSON><PERSON>


def get_bom_info(detect_output):
    """
    Get the bom information (project_id and project_version_id) for the given
    Detect output
    :return: tuple (project_id, project_version_id)
    """
    if type(detect_output) == bytes:
        detect_output = detect_output.decode('utf-8')
    server_url = hub.get_urlbase()
    pattern = urljoin(server_url, 'api/projects/(.+)/versions/(.+)/components')
    results = re.findall(pattern, detect_output)
    if len(results) != 1:
        raise ValueError('Unable to get the bom information for the given '
                         'Detect output.')
    project_id, project_version_id = results[0]
    return project_id, project_version_id


def check_source_upload_status(project_id, project_version_id):
    """
    Check if the source code is uploaded
    :return: True or False
    """
    url_base = hub.get_urlbase()
    url = urljoin(url_base, f'api/internal/projects/{project_id}/versions/'
                            f'{project_version_id}/source-bom-entries')
    headers = hub.get_headers()
    bom_link = urljoin(url_base, f'api/projects/{project_id}/versions/'
                                 f'{project_version_id}/components')

    params = {
        'limit': 99999,
        'filter': 'bomMatchType:snippet'
    }
    r = requests.get(url, headers=headers, params=params, verify=False)
    data = json.loads(r.text)
    if data['totalCount'] == 0:
        raise Exception('No snippets in the scan results. Please double check.')

    for it in data['items']:
        if not it['sourceUploaded']:
            print(f'Source code for "{it["name"]}" was not uploaded. '
                  f'Please check the bom: {bom_link}')
            return False
    else:
        print('All source code has been uploaded.')
        return True


def run_test(detect_jar_path, times=12):
    detect_cmd = """
    cd ./code; java -jar {detect_jar_path} \
    --logging.level.com.synopsys.integration=DEBUG --blackduck.trust.cert=true \
    --blackduck.url="{server_url}" --blackduck.username="{username}" \
    --blackduck.password="{password}"  --detect.project.name="{project_name}" \
    --detect.project.version.name="{project_version_name}" \
    --detect.code.location.name="{scan_name}" \
    --detect.blackduck.signature.scanner.snippet.matching=SNIPPET_MATCHING \
    --detect.blackduck.signature.scanner.upload.source.mode=true \
    --detect.wait.for.results=true --detect.timeout=600
    """
    server_url = hub.get_urlbase()
    detect_output_fp = 'detect_output.txt'

    if os.path.isfile(detect_output_fp):
        # Remove old detect output log if it already exists
        os.remove(detect_output_fp)

    for i in range(times):
        # 4 scans in a loop
        print(f'(Loop {i+1}): Running the 1st scan ...')
        results = subprocess.run(
            detect_cmd.format(detect_jar_path=detect_jar_path, server_url=server_url, username=username, password=password,
                              project_name='project_100', project_version_name='project_100_version', scan_name='api_100'),
            shell=True,
            capture_output=True
        )
        if not results.stdout:
            raise Exception('No Detect output')
        else:
            with open(detect_output_fp, 'a+') as f:
                print(results.stdout.decode('utf-8'), file=f)

        project_id, project_version_id = get_bom_info(results.stdout)
        if not check_source_upload_status(project_id, project_version_id):
            break

        print(f'(Loop {i+1}): Running the 2nd scan ...')
        results = subprocess.run(
            detect_cmd.format(detect_jar_path=detect_jar_path, server_url=server_url, username=username, password=password,
                              project_name='project_100', project_version_name='project_100_version', scan_name='account_100'),
            shell=True,
            capture_output=True
        )
        if not results.stdout:
            raise Exception('No Detect output')
        else:
            with open(detect_output_fp, 'a+') as f:
                print(results.stdout.decode('utf-8'), file=f)
        project_id, project_version_id = get_bom_info(results.stdout)
        if not check_source_upload_status(project_id, project_version_id):
            break

        print(f'(Loop {i+1}): Running the 3rd scan ...')
        results = subprocess.run(
            detect_cmd.format(detect_jar_path=detect_jar_path, server_url=server_url, username=username, password=password,
                              project_name='project_101', project_version_name='project_101_version', scan_name='account_100'),
            shell=True,
            capture_output=True
        )
        if not results.stdout:
            raise Exception('No Detect output')
        else:
            with open(detect_output_fp, 'a+') as f:
                print(results.stdout.decode('utf-8'), file=f)
        project_id, project_version_id = get_bom_info(results.stdout)
        if not check_source_upload_status(project_id, project_version_id):
            break

        print(f'(Loop {i+1}): Running the 4th scan ...')
        results = subprocess.run(
            detect_cmd.format(detect_jar_path=detect_jar_path, server_url=server_url, username=username, password=password,
                              project_name='project_101', project_version_name='project_101_version', scan_name='api_100'),
            shell=True,
            capture_output=True
        )
        if not results.stdout:
            raise Exception('No Detect output')
        else:
            with open(detect_output_fp, 'a+') as f:
                print(results.stdout.decode('utf-8'), file=f)
        project_id, project_version_id = get_bom_info(results.stdout)
        if not check_source_upload_status(project_id, project_version_id):
            break


if __name__ == '__main__':
    # Note: change below settings in accord with your server
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub10.dc1.lan'
    detect_jar_path = '/home/<USER>/detect/synopsys-detect-6.9.1.jar'

    hub = HubInstance(urlbase, username, password, insecure=True)
    run_test(detect_jar_path, times=100)
