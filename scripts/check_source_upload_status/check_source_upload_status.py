import re
import json
import subprocess
from time import sleep
from urllib.parse import urljoin

from blackduck import Client
import logging
import os

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)


def get_config():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'config.json')
    with open(config_path) as f:
        config = json.load(f)
    return config


class SourceUploadTestCase:
    def __init__(self):
        self.config = get_config()
        self.bd = Client(
            token=self.config['token'],
            base_url=self.config['url'],
            verify=False  # TLS certificate verification
        )

    def get_bom_info(self, detect_output):
        """
        Get the bom information (project_id and project_version_id) for the
        given Detect output
        :return: tuple (project_id, project_version_id)
        """
        if type(detect_output) == bytes:
            detect_output = detect_output.decode('utf-8')
        server_url = self.bd.base_url
        pattern = urljoin(server_url, 'api/projects/(.+)/versions/(.+)/components')
        results = re.findall(pattern, detect_output)
        if len(results) != 1:
            # raise ValueError('Unable to get the bom information for the given '
            #                  'Detect output.')
            return None, None
        project_id, project_version_id = results[0]
        return project_id, project_version_id

    def check_source_upload_status(self, project_id, project_version_id):
        """
        Check if the source code is uploaded
        :return: True or False
        """
        if project_id is None and project_version_id is None:
            logging.warning('Both project id and project version id is None. '
                            'Skipped the source upload status check.')
            return True
        url_base = self.bd.base_url
        bom_link = urljoin(url_base, f'api/projects/{project_id}/versions/'
                                     f'{project_version_id}/components')

        params = {
            'limit': 99999,
            'filter': 'bomMatchType:snippet'
        }

        api = f'/api/internal/projects/{project_id}/versions/' \
              f'{project_version_id}/source-bom-entries'

        r = self.bd.session.get(api, params=params)
        data = json.loads(r.text)
        if data['totalCount'] == 0:
            raise Exception('No snippets in the scan results.')

        for it in data['items']:
            if not it['sourceUploaded']:
                logging.info(f'Source code for "{it["name"]}" was not '
                             f'uploaded. Please check the bom: {bom_link}')
                return False
        else:
            logging.info('All source code has been uploaded.')
            return True

    def delete_scan(self, scan_name):
        """
        Delete a given scan if it already exists
        """
        # check if scan exists
        params = {
            'limit': 100,
            'q': f'name:{scan_name}'
        }
        api = '/api/codelocations'
        r = self.bd.session.get(api, params=params)
        data = json.loads(r.text)
        if data['totalCount'] == 0:
            return
        for it in data['items']:
            if it['name'] == scan_name:
                url = it['_meta']['href']
        r = self.bd.session.delete(url)
        if r.status_code == 204:
            logging.info(f'Scan "{scan_name}" was deleted')
        else:
            raise Exception(f'Failed to delete scan "{scan_name}". '
                            f'The status code is {r.status_code} ')

    def run_test(self, times=100):
        detect_jar_path = self.config['detect_jar_path']
        detect_logging_level = self.config['detect_logging_level']
        server_url = self.bd.base_url
        api_token = self.config['token']
        detect_cmd = f"""
        cd ./code; java -jar {detect_jar_path} \
        --logging.level.com.synopsys.integration={detect_logging_level} \
        --blackduck.trust.cert=true \
        --blackduck.url="{server_url}" --blackduck.api.token="{api_token}" \
        --detect.project.name="{{project_name}}" \
        --detect.project.version.name="{{project_version_name}}" \
        --detect.code.location.name="{{scan_name}}" \
        --detect.blackduck.signature.scanner.snippet.matching=SNIPPET_MATCHING \
        --detect.blackduck.signature.scanner.upload.source.mode=true \
        --detect.wait.for.results=true --detect.timeout=300
        """

        detect_output_fp = 'detect_output.txt'

        if os.path.isfile(detect_output_fp):
            # Remove old detect output log if it already exists
            os.remove(detect_output_fp)
        scan_name_1 = self.config['scan_name_1']
        scan_name_2 = self.config['scan_name_2']
        project_name_1 = self.config['project_name_1']
        project_name_2 = self.config['project_name_2']
        project_version_name_1 = self.config['project_version_name_1']
        project_version_name_2 = self.config['project_version_name_2']
        self.delete_scan(scan_name_1 + ' scan')
        self.delete_scan(scan_name_2 + ' scan')
        sleep(30)

        for i in range(times):
            # 4 scans in a loop
            logging.info(f'(Loop {i+1}): Running the 1st scan ...')
            results = subprocess.run(
                detect_cmd.format(project_name=project_name_1,
                                  project_version_name=project_version_name_1,
                                  scan_name=scan_name_1),
                shell=True,
                capture_output=True
            )
            if not results.stdout:
                raise Exception('No Detect output')
            else:
                with open(detect_output_fp, 'a+') as f:
                    print(results.stdout.decode('utf-8'), file=f)

            project_id, project_version_id = self.get_bom_info(results.stdout)
            if not self.check_source_upload_status(project_id, project_version_id):
                break

            logging.info(f'(Loop {i+1}): Running the 2nd scan ...')
            results = subprocess.run(
                detect_cmd.format(project_name=project_name_1,
                                  project_version_name=project_version_name_1,
                                  scan_name=scan_name_2),
                shell=True,
                capture_output=True
            )
            if not results.stdout:
                raise Exception('No Detect output')
            else:
                with open(detect_output_fp, 'a+') as f:
                    print(results.stdout.decode('utf-8'), file=f)
            project_id, project_version_id = self.get_bom_info(results.stdout)
            if not self.check_source_upload_status(project_id, project_version_id):
                break

            logging.info(f'(Loop {i+1}): Running the 3rd scan ...')
            results = subprocess.run(
                detect_cmd.format(project_name=project_name_2,
                                  project_version_name=project_version_name_2,
                                  scan_name=scan_name_2),
                shell=True,
                capture_output=True
            )
            if not results.stdout:
                raise Exception('No Detect output')
            else:
                with open(detect_output_fp, 'a+') as f:
                    print(results.stdout.decode('utf-8'), file=f)
            project_id, project_version_id = self.get_bom_info(results.stdout)
            if not self.check_source_upload_status(project_id, project_version_id):
                break

            logging.info(f'(Loop {i+1}): Running the 4th scan ...')
            results = subprocess.run(
                detect_cmd.format(project_name=project_name_2,
                                  project_version_name=project_version_name_2,
                                  scan_name=scan_name_1),
                shell=True,
                capture_output=True
            )
            if not results.stdout:
                raise Exception('No Detect output')
            else:
                with open(detect_output_fp, 'a+') as f:
                    print(results.stdout.decode('utf-8'), file=f)
            project_id, project_version_id = self.get_bom_info(results.stdout)
            if not self.check_source_upload_status(project_id, project_version_id):
                break


if __name__ == '__main__':
    # Change the url and token in the config.json file
    testcase = SourceUploadTestCase()
    testcase.run_test(times=100)

