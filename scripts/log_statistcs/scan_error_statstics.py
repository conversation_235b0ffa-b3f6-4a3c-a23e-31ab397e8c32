"""
根据jobrunner日志的`msg: Scan processing timed out.`来统计扫描报错
"""

import re
import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import argparse
from pathlib import Path


def parse_log_file(file_path, error_message="msg: Scan processing timed out."):
    """解析日志文件，提取所有包含指定错误信息的记录及其时间戳"""
    error_pattern = re.compile(
        r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*ScanId ([a-f0-9-]+).*' + re.escape(error_message))
    error_records = []

    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            if error_message in line:
                match = error_pattern.search(line)
                if match:
                    time_str, scan_id = match.groups()
                    # 解析时间字符串，移除毫秒部分
                    time_obj = datetime.datetime.strptime(time_str.split(',')[0], '%Y-%m-%d %H:%M:%S')
                    error_records.append((time_obj, scan_id))

    return error_records


def extract_error_details(file_path, error_message="msg: Scan processing timed out."):
    """提取更详细的错误信息，包括状态转换和超时时间"""
    pattern = re.compile(
        r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*ScanId ([a-f0-9-]+) updated to state/transition reason ([A-Z_]+)/([A-Z_]+) (\d+\.\d+) sec after start \(' + re.escape(
            error_message) + r'\)')
    details = []

    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            if error_message in line:
                match = pattern.search(line)
                if match:
                    time_str, scan_id, state, reason, duration = match.groups()
                    time_obj = datetime.datetime.strptime(time_str.split(',')[0], '%Y-%m-%d %H:%M:%S')
                    details.append({
                        'time': time_obj,
                        'scan_id': scan_id,
                        'state': state,
                        'reason': reason,
                        'duration': float(duration)
                    })

    return details


def analyze_error_frequency(error_records, time_window_minutes=5):
    """分析错误频率，统计每个时间窗口内的错误次数"""
    if not error_records:
        return {}

    # 按时间排序
    error_records.sort(key=lambda x: x[0])

    # 统计每个时间窗口内的错误次数
    time_window = datetime.timedelta(minutes=time_window_minutes)
    error_windows = defaultdict(list)

    for time_obj, scan_id in error_records:
        # 将时间舍入到最近的时间窗口
        window_start = time_obj.replace(
            second=0,
            microsecond=0,
            minute=(time_obj.minute // time_window_minutes) * time_window_minutes
        )
        error_windows[window_start].append(scan_id)

    return error_windows


def detect_high_frequency_periods(error_windows, threshold=5):
    """检测高频错误时间段（错误次数超过阈值的时间窗口）"""
    high_freq_periods = {}
    for window_start, scan_ids in error_windows.items():
        if len(scan_ids) >= threshold:
            high_freq_periods[window_start] = len(scan_ids)

    return high_freq_periods


def plot_error_distribution(error_windows, output_file=None):
    """绘制错误分布图"""
    times = list(error_windows.keys())
    counts = [len(ids) for ids in error_windows.values()]

    plt.figure(figsize=(12, 6))
    plt.bar(times, counts, width=0.01, color='red')
    plt.gcf().autofmt_xdate()
    plt.title('Scan Error Frequency Distribution')
    plt.xlabel('Time')
    plt.ylabel('Number of Errors')
    plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    plt.tight_layout()

    if output_file:
        plt.savefig(output_file)
        print(f"Plot saved to {output_file}")
    else:
        plt.show()


def analyze_duration_stats(error_details):
    """分析超时扫描的持续时间统计信息"""
    if not error_details:
        return None

    durations = [record['duration'] for record in error_details]
    return {
        'min': min(durations),
        'max': max(durations),
        'avg': sum(durations) / len(durations),
        'median': sorted(durations)[len(durations) // 2],
        'count': len(durations)
    }


def plot_duration_histogram(error_details, output_file=None):
    """绘制超时扫描持续时间的直方图"""
    if not error_details:
        return

    durations = [record['duration'] / 60 for record in error_details]  # 转换为分钟

    plt.figure(figsize=(10, 6))
    plt.hist(durations, bins=20, color='blue', alpha=0.7)
    plt.title('Scan Duration Before Timeout')
    plt.xlabel('Duration (minutes)')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    if output_file:
        plt.savefig(output_file)
        print(f"Duration histogram saved to {output_file}")
    else:
        plt.show()


def main():
    parser = argparse.ArgumentParser(description='Analyze scan errors from logs')
    parser.add_argument('log_file', type=str, help='Path to the log file')
    parser.add_argument('--window', type=int, default=5, help='Time window in minutes (default: 5)')
    parser.add_argument('--threshold', type=int, default=5, help='Threshold for high frequency detection (default: 5)')
    parser.add_argument('--error-msg', type=str, default="msg: Scan processing timed out.",
                        help='Error message to search for')
    parser.add_argument('--plot', type=str, help='Save frequency plot to file')
    parser.add_argument('--duration-plot', type=str, help='Save duration histogram to file')
    args = parser.parse_args()

    print(f"Analyzing log file: {args.log_file}")

    # 获取基本错误记录
    error_records = parse_log_file(args.log_file, args.error_msg)
    print(f"Found {len(error_records)} error entries matching '{args.error_msg}'")

    if not error_records:
        print("No matching error entries found in the log file.")
        return

    # 获取详细错误信息
    error_details = extract_error_details(args.log_file, args.error_msg)

    # 基本统计信息
    earliest_error = min(error_records, key=lambda x: x[0])
    latest_error = max(error_records, key=lambda x: x[0])
    time_span = latest_error[0] - earliest_error[0]

    print(f"\nError Period: {time_span}")
    print(f"First Error: {earliest_error[0]} (ScanId: {earliest_error[1]})")
    print(f"Last Error: {latest_error[0]} (ScanId: {latest_error[1]})")

    # 分析错误频率
    error_windows = analyze_error_frequency(error_records, args.window)

    # 按错误次数排序，找出错误最频繁的时间窗口
    sorted_windows = sorted(error_windows.items(), key=lambda x: len(x[1]), reverse=True)

    print(f"\nTop 5 highest error {args.window}-minute windows:")
    for i, (window_start, scan_ids) in enumerate(sorted_windows[:5], 1):
        window_end = window_start + datetime.timedelta(minutes=args.window)
        print(f"{i}. {window_start} to {window_end}: {len(scan_ids)} errors")

    # 检测高频错误时间段
    high_freq_periods = detect_high_frequency_periods(error_windows, args.threshold)

    if high_freq_periods:
        print(f"\nDetected {len(high_freq_periods)} high-frequency error periods (>{args.threshold} errors):")
        for window_start, count in sorted(high_freq_periods.items()):
            window_end = window_start + datetime.timedelta(minutes=args.window)
            print(f"{window_start} to {window_end}: {count} errors")
    else:
        print(f"\nNo high-frequency error periods detected (threshold: {args.threshold} errors)")

    # 分析持续时间统计
    if error_details:
        duration_stats = analyze_duration_stats(error_details)
        print("\nScan Duration Statistics (before timeout):")
        print(f"  Minimum: {duration_stats['min']:.2f} seconds ({duration_stats['min'] / 60:.2f} minutes)")
        print(f"  Maximum: {duration_stats['max']:.2f} seconds ({duration_stats['max'] / 60:.2f} minutes)")
        print(f"  Average: {duration_stats['avg']:.2f} seconds ({duration_stats['avg'] / 60:.2f} minutes)")
        print(f"  Median:  {duration_stats['median']:.2f} seconds ({duration_stats['median'] / 60:.2f} minutes)")

        # 绘制持续时间直方图
        if args.duration_plot:
            plot_duration_histogram(error_details, args.duration_plot)

    # 绘制错误分布图
    if args.plot or len(error_windows) > 0:
        plot_error_distribution(error_windows, args.plot)


if __name__ == "__main__":
    main()