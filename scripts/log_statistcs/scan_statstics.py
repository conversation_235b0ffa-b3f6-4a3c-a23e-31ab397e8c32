"""
通过查询scan容器日志中的"<PERSON><PERSON> created"来统计扫描
"""

import re
import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import argparse
from pathlib import Path


def parse_log_file(file_path):
    """解析日志文件，提取所有包含'<PERSON><PERSON> created'的记录及其时间戳"""
    scan_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*<PERSON><PERSON> created \[Scan id: ([a-f0-9-]+)')
    scan_times = []

    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            if '<PERSON><PERSON> created' in line:
                match = scan_pattern.search(line)
                if match:
                    time_str, scan_id = match.groups()
                    # 解析时间字符串，移除毫秒部分
                    time_obj = datetime.datetime.strptime(time_str.split(',')[0], '%Y-%m-%d %H:%M:%S')
                    scan_times.append((time_obj, scan_id))

    return scan_times


def analyze_scan_frequency(scan_times, time_window_minutes=5):
    """分析扫描频率，统计每个时间窗口内的扫描次数"""
    if not scan_times:
        return {}

    # 按时间排序
    scan_times.sort(key=lambda x: x[0])

    # 统计每个时间窗口内的扫描次数
    time_window = datetime.timedelta(minutes=time_window_minutes)
    scan_windows = defaultdict(list)

    for time_obj, scan_id in scan_times:
        # 将时间舍入到最近的时间窗口
        window_start = time_obj.replace(
            second=0,
            microsecond=0,
            minute=(time_obj.minute // time_window_minutes) * time_window_minutes
        )
        scan_windows[window_start].append(scan_id)

    return scan_windows


def detect_high_frequency_periods(scan_windows, threshold=10):
    """检测高频扫描时间段（扫描次数超过阈值的时间窗口）"""
    high_freq_periods = {}
    for window_start, scan_ids in scan_windows.items():
        if len(scan_ids) >= threshold:
            high_freq_periods[window_start] = len(scan_ids)

    return high_freq_periods


def plot_scan_distribution(scan_windows, output_file=None):
    """绘制扫描分布图"""
    times = list(scan_windows.keys())
    counts = [len(ids) for ids in scan_windows.values()]

    plt.figure(figsize=(12, 6))
    plt.bar(times, counts, width=0.01)
    plt.gcf().autofmt_xdate()
    plt.title('Scan Frequency Distribution')
    plt.xlabel('Time')
    plt.ylabel('Number of Scans')
    plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    plt.tight_layout()

    if output_file:
        plt.savefig(output_file)
        print(f"Plot saved to {output_file}")
    else:
        plt.show()


def main():
    parser = argparse.ArgumentParser(description='Analyze scan frequency from logs')
    parser.add_argument('log_file', type=str, help='Path to the log file')
    parser.add_argument('--window', type=int, default=5, help='Time window in minutes (default: 5)')
    parser.add_argument('--threshold', type=int, default=10,
                        help='Threshold for high frequency detection (default: 10)')
    parser.add_argument('--plot', type=str, help='Save plot to file')
    args = parser.parse_args()

    print(f"Analyzing log file: {args.log_file}")
    scan_times = parse_log_file(args.log_file)
    print(f"Found {len(scan_times)} scan entries")

    if not scan_times:
        print("No scan entries found in the log file.")
        return

    # 基本统计信息
    earliest_scan = min(scan_times, key=lambda x: x[0])
    latest_scan = max(scan_times, key=lambda x: x[0])
    time_span = latest_scan[0] - earliest_scan[0]

    print(f"\nScan Period: {time_span}")
    print(f"First Scan: {earliest_scan[0]} (ID: {earliest_scan[1]})")
    print(f"Last Scan: {latest_scan[0]} (ID: {latest_scan[1]})")

    # 分析扫描频率
    scan_windows = analyze_scan_frequency(scan_times, args.window)

    # 按扫描次数排序，找出扫描最频繁的时间窗口
    sorted_windows = sorted(scan_windows.items(), key=lambda x: len(x[1]), reverse=True)

    print(f"\nTop 5 busiest {args.window}-minute windows:")
    for i, (window_start, scan_ids) in enumerate(sorted_windows[:5], 1):
        window_end = window_start + datetime.timedelta(minutes=args.window)
        print(f"{i}. {window_start} to {window_end}: {len(scan_ids)} scans")

    # 检测高频扫描时间段
    high_freq_periods = detect_high_frequency_periods(scan_windows, args.threshold)

    if high_freq_periods:
        print(f"\nDetected {len(high_freq_periods)} high-frequency periods (>{args.threshold} scans):")
        for window_start, count in sorted(high_freq_periods.items()):
            window_end = window_start + datetime.timedelta(minutes=args.window)
            print(f"{window_start} to {window_end}: {count} scans")
    else:
        print(f"\nNo high-frequency periods detected (threshold: {args.threshold} scans)")

    # 绘制分布图
    if args.plot or len(scan_windows) > 0:
        plot_scan_distribution(scan_windows, args.plot)


if __name__ == "__main__":
    main()