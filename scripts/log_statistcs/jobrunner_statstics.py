"""
分析jobrunner日志
前10个最常出现的类:
1. ScanPurgeWorkflowJob - 4453 次 (0.0516 次/秒)
2. QuartzSnippetScanAutoBomJob - 2403 次 (0.0278 次/秒)
3. IJobScheduler - 2123 次 (0.0246 次/秒)
4. ScanToCodeLocationBomMatchEventHandler - 2000 次 (0.0232 次/秒)
5. ScannerApi - 1606 次 (0.0186 次/秒)
6. BomEventHandler - 1600 次 (0.0185 次/秒)
7. KbUpdateWorkflowJob - 1570 次 (0.0182 次/秒)
8. WatchdogJob - 1510 次 (0.0175 次/秒)
9. JobRegistrationRefreshTriggerListener - 1461 次 (0.0169 次/秒)
10. StorageCapacityEnforcerCheckJob - 1241 次 (0.0144 次/秒)
"""

import re
import time
from collections import defaultdict
from datetime import datetime
import argparse
import json
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd
from tqdm import tqdm


def parse_log_line(line):
    """
    解析日志行，提取时间戳和完整类名
    返回：(时间戳, 完整类名, 简短类名)或None
    """
    try:
        # 解析时间戳
        timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d+)Z\[GMT\]', line)
        if not timestamp_match:
            return None

        timestamp_str = timestamp_match.group(1)
        time_obj = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')

        # 解析类名
        class_match = re.search(r'(INFO|DEBUG|WARN|ERROR)\s+([\w\.]+)', line)
        if not class_match:
            return None

        log_level = class_match.group(1)
        full_class_name = class_match.group(2)

        # 提取最后一个点后面的类名部分
        short_class_name = full_class_name.split('.')[-1]

        return (time_obj, full_class_name, short_class_name, log_level)

    except Exception as e:
        print(f"解析错误: {e}, 行: {line[:100]}...")
        return None


def analyze_log_file(file_path, time_interval=3600, level_filter=None):
    """
    分析日志文件，计算类名出现次数和频率
    time_interval: 统计时间间隔（秒），默认1小时
    level_filter: 日志级别过滤条件，如 'INFO', 'ERROR' 等
    """
    class_stats = defaultdict(int)  # 类名出现次数
    class_timeseries = defaultdict(lambda: defaultdict(int))  # 按时间间隔的类名出现次数
    class_levels = defaultdict(lambda: defaultdict(int))  # 类名日志级别分布

    min_time = None
    max_time = None
    total_lines = 0
    parsed_lines = 0
    filtered_lines = 0

    # 获取文件大小用于进度显示
    file_size = Path(file_path).stat().st_size

    with open(file_path, 'r', encoding='utf-8') as f:  # 明确指定 encoding='utf-8'
        line_iterator = tqdm(f, total=file_size, unit='B', unit_scale=True, desc="分析日志")
        for line in line_iterator:
            total_lines += 1
            line_iterator.update(len(line))

            parsed_data = parse_log_line(line)
            if not parsed_data:
                continue

            parsed_lines += 1
            time_obj, full_class_name, short_class_name, log_level = parsed_data

            # 如果指定了日志级别过滤条件，则跳过不匹配的行
            if level_filter and log_level != level_filter:
                continue

            filtered_lines += 1

            # 更新最早和最晚时间
            if min_time is None or time_obj < min_time:
                min_time = time_obj
            if max_time is None or time_obj > max_time:
                max_time = time_obj

            # 计算时间区间索引（以秒为单位）
            time_bucket = int(time_obj.timestamp() // time_interval * time_interval)

            # 更新统计数据 - 使用full_class_name作为键
            class_stats[full_class_name] += 1
            class_timeseries[full_class_name][time_bucket] += 1
            class_levels[full_class_name][log_level] += 1

    # 计算总持续时间（秒）
    if min_time and max_time:
        duration = (max_time - min_time).total_seconds()
    else:
        duration = 0

    # 计算每个类名的出现频率（每秒）
    class_frequencies = {}
    for class_name, count in class_stats.items():
        frequency = count / duration if duration > 0 else 0
        class_frequencies[class_name] = frequency

    # 构建结果，但使用短类名作为显示名称
    results = {
        "总行数": total_lines,
        "解析成功行数": parsed_lines,
        "符合过滤条件行数": filtered_lines,
        "日志级别过滤": level_filter if level_filter else "无",
        "开始时间": min_time.isoformat() if min_time else None,
        "结束时间": max_time.isoformat() if max_time else None,
        "总持续时间(秒)": duration,
        "类名统计": [
            {
                "完整类名": class_name,
                "短类名": class_name.split('.')[-1],
                "总出现次数": count,
                "频率(每秒)": class_frequencies[class_name],
                "日志级别分布": dict(class_levels[class_name])
            }
            for class_name, count in sorted(class_stats.items(), key=lambda x: x[1], reverse=True)
        ],
        "时间序列数据": {class_name: dict(times) for class_name, times in class_timeseries.items()}
    }

    return results


def generate_plots(results, output_dir, use_short_name=True):
    """
    生成可视化图表
    use_short_name: 是否使用短类名作为标签
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)

    # 1. 出现频率条形图
    plt.figure(figsize=(12, 8))
    class_data = results["类名统计"]

    if not class_data:  # 检查是否有数据
        print("没有数据可供绘图")
        return

    # 根据设置选择使用完整类名还是短类名
    classes = [item["短类名"] if use_short_name else item["完整类名"] for item in class_data]
    frequencies = [item["频率(每秒)"] for item in class_data]

    # 限制显示的类数量，避免图表过于拥挤
    max_classes_to_show = min(20, len(classes))

    plt.barh(classes[:max_classes_to_show], frequencies[:max_classes_to_show])
    title_filter = f"日志级别: {results['日志级别过滤']}" if results['日志级别过滤'] else "所有日志级别"
    plt.title(f"类出现频率 (每秒) - {title_filter}")
    plt.xlabel("频率 (每秒)")
    plt.ylabel("类名")
    plt.tight_layout()
    plt.savefig(output_dir / "class_frequencies.png")
    plt.close()

    # 2. 出现总次数饼图 (仅显示前10个)
    if len(class_data) > 0:  # 确保有数据
        plt.figure(figsize=(10, 10))
        counts = [item["总出现次数"] for item in class_data[:10]]
        labels = [f"{cls} ({count})" for cls, count in zip(classes[:10], counts)]

        plt.pie(counts, labels=labels, autopct='%1.1f%%')
        plt.title(f"前10个类出现次数分布 - {title_filter}")
        plt.tight_layout()
        plt.savefig(output_dir / "class_counts_pie.png")
        plt.close()

    # 3. 时间序列图 (仅显示前5个最常出现的类)
    plt.figure(figsize=(15, 8))
    time_data = results["时间序列数据"]

    has_data = False
    for i in range(min(5, len(class_data))):
        class_name = class_data[i]["完整类名"]
        display_name = class_data[i]["短类名"] if use_short_name else class_name

        if class_name in time_data and time_data[class_name]:
            times = sorted(time_data[class_name].keys())
            if not times:
                continue

            counts = [time_data[class_name][t] for t in times]

            # 将时间戳转换为可读格式
            readable_times = [datetime.fromtimestamp(t).strftime('%Y-%m-%d %H:%M') for t in times]

            plt.plot(readable_times, counts, marker='o', label=display_name)
            has_data = True

    if has_data:
        plt.title(f"前5个类的出现次数随时间变化 - {title_filter}")
        plt.xlabel("时间")
        plt.ylabel("出现次数")
        plt.xticks(rotation=45)
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(output_dir / "class_timeseries.png")
    plt.close()

    # 4. 日志级别分布图 (仅显示前5个最常出现的类)
    for i in range(min(5, len(class_data))):
        item = class_data[i]
        class_name = item["完整类名"]
        display_name = item["短类名"] if use_short_name else class_name
        level_dist = item["日志级别分布"]

        if level_dist:
            plt.figure(figsize=(8, 6))
            levels = list(level_dist.keys())
            counts = list(level_dist.values())

            plt.bar(levels, counts)
            plt.title(f"类 '{display_name}' 日志级别分布")
            plt.xlabel("日志级别")
            plt.ylabel("次数")
            plt.tight_layout()
            plt.savefig(output_dir / f"level_dist_class_{i + 1}.png")
            plt.close()


def main():
    parser = argparse.ArgumentParser(description="分析日志类名统计")
    parser.add_argument("log_file", help="日志文件路径")
    parser.add_argument("--interval", type=int, default=3600,
                        help="时间间隔（秒）用于统计频率，默认3600秒（1小时）")
    parser.add_argument("--output", default="log_class_analysis",
                        help="输出结果的目录，默认为'log_class_analysis'")
    parser.add_argument("--level", choices=["INFO", "DEBUG", "WARN", "ERROR"],
                        help="只统计特定日志级别的行")
    parser.add_argument("--full-name", action="store_true",
                        help="在图表中显示完整类名而非短类名")
    args = parser.parse_args()

    start_time = time.time()
    print(f"开始分析日志文件: {args.log_file}")

    # 确定过滤条件
    level_filter = args.level
    level_desc = "无" if level_filter is None else level_filter
    print(f"应用日志级别过滤: {level_desc}")

    try:
        results = analyze_log_file(args.log_file, args.interval, level_filter)

        # 输出结果到JSON文件
        output_dir = Path(args.output)
        output_dir.mkdir(exist_ok=True, parents=True)

        with open(output_dir / "results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 生成报表 - 使用短类名
        class_stats = results["类名统计"]
        if class_stats:
            # 创建一个新的DataFrame，以短类名作为显示
            df = pd.DataFrame([
                {
                    "类名": item["短类名"],
                    "完整类名": item["完整类名"],
                    "总出现次数": item["总出现次数"],
                    "频率(每秒)": item["频率(每秒)"],
                    "日志级别分布": json.dumps(item["日志级别分布"])
                }
                for item in class_stats
            ])
            df.to_csv(output_dir / "class_stats.csv", index=False)
        else:
            print("没有匹配的数据用于生成CSV报表")

        # 生成可视化图表
        generate_plots(results, output_dir, use_short_name=not args.full_name)

        # 打印摘要
        print("\n分析完成!")
        print(f"总行数: {results['总行数']}")
        print(f"解析成功行数: {results['解析成功行数']}")
        print(f"符合过滤条件行数: {results['符合过滤条件行数']}")
        print(f"日志级别过滤: {results['日志级别过滤']}")
        print(f"开始时间: {results['开始时间']}")
        print(f"结束时间: {results['结束时间']}")
        print(f"总持续时间: {results['总持续时间(秒)']:.2f} 秒")

        if class_stats:
            print("\n前10个最常出现的类:")
            for i, item in enumerate(class_stats[:min(10, len(class_stats))], 1):
                print(f"{i}. {item['短类名']} - {item['总出现次数']} 次 ({item['频率(每秒)']:.4f} 次/秒)")
        else:
            print("\n没有找到符合条件的类")

        print(f"\n详细结果已保存到目录: {output_dir}")

    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

    end_time = time.time()
    print(f"分析耗时: {end_time - start_time:.2f} 秒")


if __name__ == "__main__":
    main()