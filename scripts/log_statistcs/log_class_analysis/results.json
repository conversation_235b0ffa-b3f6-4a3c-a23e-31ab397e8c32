{"总行数": 696522, "解析成功行数": 696522, "符合过滤条件行数": 696522, "日志级别过滤": "无", "开始时间": "2025-08-11T05:23:35.339000", "结束时间": "2025-08-12T13:06:10.879000", "总持续时间(秒)": 114155.54, "类名统计": [{"完整类名": "com.blackducksoftware.bom.aggregate.domain.impl.job.QuartzVersionBomNotificationJob", "短类名": "QuartzVersionBomNotificationJob", "总出现次数": 113119, "频率(每秒)": 0.9909199325762027, "日志级别分布": {"INFO": 113119}}, {"完整类名": "com.blackducksoftware.job.quartz.impl.JobRegistrationRefreshTriggerListener", "短类名": "JobRegistrationRefreshTriggerListener", "总出现次数": 69622, "频率(每秒)": 0.609887176741488, "日志级别分布": {"INFO": 69622}}, {"完整类名": "com.blackducksoftware.scan.bom.handler.ScanToCodeLocationBomMatchEventHandler", "短类名": "ScanToCodeLocationBomMatchEventHandler", "总出现次数": 61940, "频率(每秒)": 0.5425930270226045, "日志级别分布": {"INFO": 61940}}, {"完整类名": "com.blackducksoftware.job.quartz.api.IJobScheduler", "短类名": "IJobScheduler", "总出现次数": 53564, "频率(每秒)": 0.46921945268709697, "日志级别分布": {"INFO": 53564}}, {"完整类名": "com.blackducksoftware.scan.siggen.impl.ScannerApi", "短类名": "ScannerApi", "总出现次数": 52034, "频率(每秒)": 0.45581668660145624, "日志级别分布": {"INFO": 52034}}, {"完整类名": "com.blackducksoftware.scan.bom.handler.BomEventHandler", "短类名": "BomEventHandler", "总出现次数": 49552, "频率(每秒)": 0.4340744216180836, "日志级别分布": {"INFO": 49552}}, {"完整类名": "com.blackducksoftware.scan.bom.job.QuartzSnippetScanAutoBomJob", "短类名": "QuartzSnippetScanAutoBomJob", "总出现次数": 49502, "频率(每秒)": 0.43363642272639596, "日志级别分布": {"INFO": 49502}}, {"完整类名": "com.blackducksoftware.bom.aggregate.domain.impl.job.VersionBomNotificationCheckJob", "短类名": "VersionBomNotificationCheckJob", "总出现次数": 28834, "频率(每秒)": 0.25258520085840774, "日志级别分布": {"INFO": 28834}}, {"完整类名": "com.blackducksoftware.scan.bom.job.ScanPurgeWorkflowJob", "短类名": "ScanPurgeWorkflowJob", "总出现次数": 27292, "频率(每秒)": 0.23907731503876203, "日志级别分布": {"INFO": 27292}}, {"完整类名": "com.blackducksoftware.job.quartz.jobs.WatchdogJob", "短类名": "WatchdogJob", "总出现次数": 23036, "频率(每秒)": 0.20179484937831316, "日志级别分布": {"INFO": 23036}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbUpdateWorkflowJob", "短类名": "KbUpdateWorkflowJob", "总出现次数": 19371, "频率(每秒)": 0.16968953061761174, "日志级别分布": {"INFO": 19371}}, {"完整类名": "com.blackducksoftware.search.repository.jdbc.impl.SearchDashboardComponentRepository", "短类名": "SearchDashboardComponentRepository", "总出现次数": 19336, "频率(每秒)": 0.1693829313934304, "日志级别分布": {"INFO": 19336}}, {"完整类名": "com.blackducksoftware.search.repository.jdbc.impl.SearchDashboardProjectVersionRepository", "短类名": "SearchDashboardProjectVersionRepository", "总出现次数": 19293, "频率(每秒)": 0.16900625234657907, "日志级别分布": {"INFO": 19293}}, {"完整类名": "com.blackducksoftware.kb.domain.impl.KbComponentUpdateRegistry", "短类名": "KbComponentUpdateRegistry", "总出现次数": 18348, "频率(每秒)": 0.16072807329368335, "日志级别分布": {"INFO": 18348}}, {"完整类名": "com.blackducksoftware.bom.aggregate.domain.impl.computation.BomComputationManager", "短类名": "BomComputationManager", "总出现次数": 12388, "频率(每秒)": 0.1085186054045209, "日志级别分布": {"INFO": 12388}}, {"完整类名": "com.blackducksoftware.scan.siggen.impl.BlackDuckInputOutputService", "短类名": "BlackDuckInputOutputService", "总出现次数": 11279, "频率(每秒)": 0.09880378998688982, "日志级别分布": {"INFO": 11279}}, {"完整类名": "com.blackducksoftware.kb.kbdetail.impl.KbVersionSecurityUpdateRegistry", "短类名": "KbVersionSecurityUpdateRegistry", "总出现次数": 10855, "频率(每秒)": 0.09508955938537894, "日志级别分布": {"INFO": 10855}}, {"完整类名": "com.blackducksoftware.scan.siggen.impl.ScanFailureReporter", "短类名": "ScanFailureReporter", "总出现次数": 10473, "频率(每秒)": 0.09174324785288564, "日志级别分布": {"ERROR": 10473}}, {"完整类名": "com.blackducksoftware.search.repository.jdbc.impl.SearchDashboardVulnerabilityRepository", "短类名": "SearchDashboardVulnerabilityRepository", "总出现次数": 9668, "频率(每秒)": 0.0846914656967152, "日志级别分布": {"INFO": 9668}}, {"完整类名": "com.blackducksoftware.scan.bom.impl.ScanCompositeLeafApi", "短类名": "ScanCompositeLeafApi", "总出现次数": 7059, "频率(每秒)": 0.061836683528456005, "日志级别分布": {"INFO": 7059}}, {"完整类名": "com.blackducksoftware.search.integration.domain.impl.SearchDashboardRefreshCheckJob", "短类名": "SearchDashboardRefreshCheckJob", "总出现次数": 4779, "频率(每秒)": 0.04186393406750124, "日志级别分布": {"INFO": 4779}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbComponentUpdateJobTask", "短类名": "KbComponentUpdateJobTask", "总出现次数": 3124, "频率(每秒)": 0.027366170752641528, "日志级别分布": {"INFO": 3124}}, {"完整类名": "com.blackducksoftware.codelocation.domain.api.PolicyBasedPurgeThrottle", "短类名": "PolicyBasedPurgeThrottle", "总出现次数": 2228, "频率(每秒)": 0.019517230613599657, "日志级别分布": {"INFO": 2228}}, {"完整类名": "com.blackducksoftware.storage.domain.impl.StoragePrunerWorkflowJob", "短类名": "StoragePrunerWorkflowJob", "总出现次数": 1857, "频率(每秒)": 0.016267278837277632, "日志级别分布": {"INFO": 1857}}, {"完整类名": "com.blackducksoftware.report.db.domain.impl.ReportingViewApi", "短类名": "ReportingViewApi", "总出现次数": 1716, "频率(每秒)": 0.015032121962718586, "日志级别分布": {"INFO": 1716}}, {"完整类名": "com.blackducksoftware.kb.kbvuln.impl.KbBdsaVulnerabilityUpdateRegistry", "短类名": "KbBdsaVulnerabilityUpdateRegistry", "总出现次数": 1710, "频率(每秒)": 0.014979562095716074, "日志级别分布": {"INFO": 1710}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbBdsaVulnerabilityUpdateJobTask", "短类名": "KbBdsaVulnerabilityUpdateJobTask", "总出现次数": 1710, "频率(每秒)": 0.014979562095716074, "日志级别分布": {"INFO": 1710}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbComponentVersionSecurityUpdateJobTask", "短类名": "KbComponentVersionSecurityUpdateJobTask", "总出现次数": 1670, "频率(每秒)": 0.01462916298236599, "日志级别分布": {"INFO": 1670}}, {"完整类名": "com.blackducksoftware.kb.kbvuln.impl.KbNvdVulnerabilityUpdateRegistry", "短类名": "KbNvdVulnerabilityUpdateRegistry", "总出现次数": 1586, "频率(每秒)": 0.013893324844330814, "日志级别分布": {"INFO": 1586}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbNvdVulnerabilityUpdateJobTask", "短类名": "KbNvdVulnerabilityUpdateJobTask", "总出现次数": 1586, "频率(每秒)": 0.013893324844330814, "日志级别分布": {"INFO": 1586}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbComponentActivityUpdateProcessor", "短类名": "KbComponentActivityUpdateProcessor", "总出现次数": 1562, "频率(每秒)": 0.013683085376320764, "日志级别分布": {"INFO": 1562}}, {"完整类名": "com.blackducksoftware.bom.aggregate.impl.VersionBomKbComponentUpdateApi", "短类名": "VersionBomKbComponentUpdateApi", "总出现次数": 1562, "频率(每秒)": 0.013683085376320764, "日志级别分布": {"INFO": 1562}}, {"完整类名": "com.blackducksoftware.scan.bom.job.ScanPurgeCheckJob", "短类名": "ScanPurgeCheckJob", "总出现次数": 1392, "频率(每秒)": 0.01219388914458291, "日志级别分布": {"INFO": 1392}}, {"完整类名": "com.blackducksoftware.bom.aggregate.domain.impl.job.QuartzVersionBomEventCleanupJob", "短类名": "QuartzVersionBomEventCleanupJob", "总出现次数": 786, "频率(每秒)": 0.006885342577329143, "日志级别分布": {"INFO": 786}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbUpdateCheckJob", "短类名": "KbUpdateCheckJob", "总出现次数": 748, "频率(每秒)": 0.006552463419646564, "日志级别分布": {"INFO": 748}}, {"完整类名": "com.blackducksoftware.bom.aggregate.domain.impl.BomAggregatePurgeOrphansApi", "短类名": "BomAggregatePurgeOrphansApi", "总出现次数": 210, "频率(每秒)": 0.001839595345087939, "日志级别分布": {"INFO": 210}}, {"完整类名": "com.blackducksoftware.bom.aggregate.job.CollectScanStatsJob", "短类名": "CollectScanStatsJob", "总出现次数": 208, "频率(每秒)": 0.0018220753894204347, "日志级别分布": {"INFO": 208}}, {"完整类名": "com.blackducksoftware.bom.aggregate.impl.VersionBomVulnerabilityUpdateApi", "短类名": "VersionBomVulnerabilityUpdateApi", "总出现次数": 172, "频率(每秒)": 0.0015067161874053594, "日志级别分布": {"INFO": 172}}, {"完整类名": "com.blackducksoftware.bom.aggregate.impl.VersionBomKbVersionSecurityUpdateApi", "短类名": "VersionBomKbVersionSecurityUpdateApi", "总出现次数": 154, "频率(每秒)": 0.0013490365863978219, "日志级别分布": {"INFO": 154}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbBdsaVulnerabilityActivityUpdateProcessor", "短类名": "KbBdsaVulnerabilityActivityUpdateProcessor", "总出现次数": 148, "频率(每秒)": 0.0012964767193953093, "日志级别分布": {"INFO": 148}}, {"完整类名": "com.blackducksoftware.kb.kbdetail.impl.KbLicenseUpdateRegistry", "短类名": "KbLicenseUpdateRegistry", "总出现次数": 120, "频率(每秒)": 0.0010511973400502508, "日志级别分布": {"INFO": 120}}, {"完整类名": "com.blackducksoftware.storage.domain.impl.StoragePrunerCheckJob", "短类名": "StoragePrunerCheckJob", "总出现次数": 112, "频率(每秒)": 0.000981117517380234, "日志级别分布": {"INFO": 112}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbVersionSecurityActivityUpdateProcessor", "短类名": "KbVersionSecurityActivityUpdateProcessor", "总出现次数": 108, "频率(每秒)": 0.0009460776060452258, "日志级别分布": {"INFO": 108}}, {"完整类名": "com.blackducksoftware.bom.domain.impl.VersionBomRiskWarningApi", "短类名": "VersionBomRiskWarningApi", "总出现次数": 100, "频率(每秒)": 0.000875997783375209, "日志级别分布": {"INFO": 100}}, {"完整类名": "com.blackducksoftware.job.quartz.jobs.JobHistoryStatsJob", "短类名": "JobHistoryStatsJob", "总出现次数": 78, "频率(每秒)": 0.0006832782710326631, "日志级别分布": {"INFO": 78}}, {"完整类名": "com.blackducksoftware.bom.aggregate.domain.impl.job.BomAggregatePurgeOrphansCheckJob", "短类名": "BomAggregatePurgeOrphansCheckJob", "总出现次数": 70, "频率(每秒)": 0.0006131984483626463, "日志级别分布": {"INFO": 70}}, {"完整类名": "com.blackducksoftware.core.rest.repository.rest.impl.SystemAuthenticationRestClient", "短类名": "SystemAuthenticationRestClient", "总出现次数": 56, "频率(每秒)": 0.000490558758690117, "日志级别分布": {"INFO": 56}}, {"完整类名": "com.blackducksoftware.bom.aggregate.impl.VersionBomArchivedKbVersionSecurityUpdateApi", "短类名": "VersionBomArchivedKbVersionSecurityUpdateApi", "总出现次数": 54, "频率(每秒)": 0.0004730388030226129, "日志级别分布": {"INFO": 54}}, {"完整类名": "com.blackducksoftware.vuln.impl.AutoRemediateUnmappedApi", "短类名": "AutoRemediateUnmappedApi", "总出现次数": 50, "频率(每秒)": 0.0004379988916876045, "日志级别分布": {"INFO": 50}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbComponentVersionLicenseUpdateJobTask", "短类名": "KbComponentVersionLicenseUpdateJobTask", "总出现次数": 48, "频率(每秒)": 0.0004204789360201003, "日志级别分布": {"INFO": 48}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbLicenseUpdateJobTask", "短类名": "KbLicenseUpdateJobTask", "总出现次数": 48, "频率(每秒)": 0.0004204789360201003, "日志级别分布": {"INFO": 48}}, {"完整类名": "com.blackducksoftware.report.integration.domain.impl.QuartzReportPurgeJob", "短类名": "QuartzReportPurgeJob", "总出现次数": 48, "频率(每秒)": 0.0004204789360201003, "日志级别分布": {"INFO": 48}}, {"完整类名": "com.blackducksoftware.report.db.job.ReportingDatabaseTransferCheckJob", "短类名": "ReportingDatabaseTransferCheckJob", "总出现次数": 39, "频率(每秒)": 0.00034163913551633153, "日志级别分布": {"INFO": 39}}, {"完整类名": "com.blackducksoftware.kb.kbdetail.impl.KbVersionLicenseUpdateRegistry", "短类名": "KbVersionLicenseUpdateRegistry", "总出现次数": 24, "频率(每秒)": 0.00021023946801005015, "日志级别分布": {"INFO": 24}}, {"完整类名": "com.blackducksoftware.kb.integration.domain.impl.KbNvdVulnerabilityActivityUpdateProcessor", "短类名": "KbNvdVulnerabilityActivityUpdateProcessor", "总出现次数": 24, "频率(每秒)": 0.00021023946801005015, "日志级别分布": {"INFO": 24}}, {"完整类名": "com.blackducksoftware.bom.aggregate.domain.impl.job.PolicyRuleScheduledChangeCheckJob", "短类名": "PolicyRuleScheduledChangeCheckJob", "总出现次数": 21, "频率(每秒)": 0.0001839595345087939, "日志级别分布": {"INFO": 21}}, {"完整类名": "com.blackducksoftware.scan.bom.job.ScanStatisticPurgeJob", "短类名": "ScanStatisticPurgeJob", "总出现次数": 14, "频率(每秒)": 0.00012263968967252925, "日志级别分布": {"INFO": 14}}, {"完整类名": "org.springframework.scheduling.quartz.LocalDataSourceJobStore", "短类名": "LocalDataSourceJobStore", "总出现次数": 12, "频率(每秒)": 0.00010511973400502508, "日志级别分布": {"INFO": 12}}, {"完整类名": "com.blackducksoftware.usermgmt.domain.impl.job.ApiTokenPurgeCheckJob", "短类名": "ApiTokenPurgeCheckJob", "总出现次数": 8, "频率(每秒)": 7.007982267001672e-05, "日志级别分布": {"INFO": 8}}, {"完整类名": "com.blackducksoftware.usermgmt.domain.impl.job.ApiTokenPurgeJob", "短类名": "ApiTokenPurgeJob", "总出现次数": 8, "频率(每秒)": 7.007982267001672e-05, "日志级别分布": {"INFO": 8}}, {"完整类名": "com.blackducksoftware.job.integration.domain.impl.NotificationPurgeCheckJob", "短类名": "NotificationPurgeCheckJob", "总出现次数": 7, "频率(每秒)": 6.131984483626463e-05, "日志级别分布": {"INFO": 7}}], "时间序列数据": {"com.blackducksoftware.job.quartz.api.IJobScheduler": {"**********": 2375, "1754906400": 1923, "1754910000": 2660, "1754913600": 3522, "**********": 2547, "1754920800": 3098, "1754924400": 2858, "1754859600": 42, "**********": 224, "1754866800": 42, "**********": 512, "**********": 528, "**********": 733, "**********": 841, "**********": 993, "**********": 1047, "**********": 1008, "**********": 600, "**********": 884, "1754902800": 804, "**********": 2189, "1754935200": 3213, "**********": 3175, "1754942400": 2525, "**********": 2740, "**********": 3064, "1754953200": 2567, "**********": 2282, "**********": 1929, "1754964000": 1289, "1754967600": 832, "1754971200": 496, "1754974800": 22}, "com.blackducksoftware.bom.aggregate.domain.impl.job.VersionBomNotificationCheckJob": {"**********": 870, "1754906400": 806, "1754910000": 1207, "1754913600": 1724, "**********": 1424, "1754920800": 1498, "1754924400": 1338, "1754859600": 35, "**********": 154, "1754866800": 21, "**********": 297, "**********": 350, "**********": 424, "**********": 512, "**********": 597, "**********": 663, "**********": 442, "**********": 333, "**********": 333, "1754902800": 130, "**********": 1330, "1754935200": 1917, "**********": 2076, "1754942400": 1418, "**********": 1418, "**********": 1880, "1754953200": 1581, "**********": 1446, "**********": 1115, "1754964000": 747, "1754967600": 456, "1754971200": 283, "1754974800": 9}, "com.blackducksoftware.bom.aggregate.domain.impl.job.QuartzVersionBomNotificationJob": {"**********": 3480, "1754906400": 3133, "1754910000": 4828, "1754913600": 6896, "**********": 5696, "1754920800": 5900, "1754924400": 5108, "1754859600": 140, "**********": 602, "1754866800": 84, "**********": 1188, "**********": 1350, "**********": 1764, "**********": 1978, "**********": 2318, "**********": 2601, "**********": 1696, "**********": 1291, "**********": 1332, "1754902800": 508, "**********": 5388, "1754935200": 7387, "**********": 8081, "1754942400": 5636, "**********": 5479, "**********": 7406, "1754953200": 6229, "**********": 5708, "**********": 4239, "1754964000": 2845, "1754967600": 1709, "1754971200": 1087, "1754974800": 32}, "com.blackducksoftware.job.quartz.impl.JobRegistrationRefreshTriggerListener": {"**********": 3869, "1754906400": 1720, "1754910000": 3750, "1754913600": 4498, "**********": 3930, "1754920800": 4307, "1754924400": 3991, "1754859600": 119, "**********": 385, "1754866800": 196, "**********": 585, "**********": 749, "**********": 987, "**********": 1269, "**********": 1372, "**********": 1434, "**********": 924, "**********": 945, "**********": 807, "1754902800": 807, "**********": 3588, "1754935200": 3835, "**********": 3910, "1754942400": 3180, "**********": 3083, "**********": 3986, "1754953200": 3276, "**********": 3331, "**********": 2052, "1754964000": 1528, "1754967600": 767, "1754971200": 424, "1754974800": 18}, "com.blackducksoftware.scan.bom.job.QuartzSnippetScanAutoBomJob": {"1754906400": 616, "1754913600": 1252, "**********": 1048, "1754920800": 1416, "1754859600": 112, "**********": 560, "1754866800": 476, "**********": 540, "**********": 784, "**********": 820, "**********": 1188, "**********": 1012, "**********": 1068, "**********": 364, "**********": 268, "1754902800": 144, "1754910000": 748, "**********": 1444, "1754924400": 820, "**********": 348, "**********": 1348, "1754935200": 2984, "**********": 3610, "1754942400": 2534, "**********": 1812, "**********": 5112, "1754953200": 4946, "**********": 5228, "**********": 3090, "1754964000": 2086, "1754967600": 1058, "1754971200": 646, "1754974800": 20}, "com.blackducksoftware.scan.siggen.impl.ScannerApi": {"1754906400": 3727, "1754910000": 4924, "1754913600": 3104, "**********": 1457, "1754920800": 1074, "1754859600": 84, "**********": 420, "1754866800": 357, "**********": 419, "**********": 595, "**********": 678, "**********": 993, "**********": 892, "**********": 968, "**********": 870, "**********": 1485, "1754902800": 1540, "**********": 1083, "1754924400": 615, "**********": 525, "**********": 1011, "1754935200": 2224, "**********": 2760, "1754942400": 1921, "**********": 1359, "**********": 3869, "1754953200": 3772, "**********": 3907, "**********": 2350, "1754964000": 1577, "1754967600": 855, "1754971200": 602, "1754974800": 17}, "com.blackducksoftware.scan.bom.handler.BomEventHandler": {"1754906400": 616, "1754913600": 1252, "**********": 1048, "1754920800": 1416, "1754859600": 112, "**********": 560, "1754866800": 476, "**********": 540, "**********": 784, "**********": 820, "**********": 1188, "**********": 1012, "**********": 1068, "**********": 364, "**********": 268, "1754902800": 144, "1754910000": 748, "**********": 1444, "1754924400": 820, "**********": 348, "**********": 1348, "1754935200": 2928, "**********": 3616, "1754942400": 2616, "**********": 1812, "**********": 5112, "1754953200": 4976, "**********": 5172, "**********": 3124, "1754964000": 2104, "1754967600": 1048, "1754971200": 648, "1754974800": 20}, "com.blackducksoftware.scan.bom.handler.ScanToCodeLocationBomMatchEventHandler": {"1754906400": 770, "1754913600": 1565, "**********": 1310, "1754920800": 1770, "1754859600": 140, "**********": 700, "1754866800": 595, "**********": 675, "**********": 980, "**********": 1025, "**********": 1485, "**********": 1265, "**********": 1335, "**********": 455, "**********": 335, "1754902800": 180, "1754910000": 935, "**********": 1805, "1754924400": 1025, "**********": 435, "**********": 1685, "1754935200": 3660, "**********": 4520, "1754942400": 3270, "**********": 2265, "**********": 6390, "1754953200": 6220, "**********": 6465, "**********": 3905, "1754964000": 2630, "1754967600": 1310, "1754971200": 810, "1754974800": 25}, "com.blackducksoftware.bom.aggregate.domain.impl.computation.BomComputationManager": {"1754906400": 154, "1754913600": 313, "**********": 262, "1754920800": 354, "1754859600": 28, "**********": 140, "1754866800": 119, "**********": 135, "**********": 196, "**********": 205, "**********": 297, "**********": 253, "**********": 267, "**********": 91, "**********": 67, "1754902800": 36, "1754910000": 187, "**********": 361, "1754924400": 205, "**********": 87, "**********": 337, "1754935200": 732, "**********": 904, "1754942400": 654, "**********": 453, "**********": 1278, "1754953200": 1244, "**********": 1293, "**********": 781, "1754964000": 526, "1754967600": 262, "1754971200": 162, "1754974800": 5}, "com.blackducksoftware.search.integration.domain.impl.SearchDashboardRefreshCheckJob": {"1754906400": 168, "1754910000": 292, "1754913600": 307, "**********": 306, "1754920800": 311, "1754924400": 396, "**********": 21, "1754866800": 21, "**********": 26, "**********": 59, "**********": 50, "**********": 72, "**********": 79, "**********": 48, "**********": 40, "1754902800": 128, "**********": 321, "**********": 70, "**********": 87, "**********": 204, "1754935200": 180, "**********": 282, "1754942400": 247, "**********": 217, "**********": 199, "1754953200": 180, "**********": 143, "**********": 129, "1754964000": 96, "1754967600": 64, "1754971200": 35, "1754974800": 1}, "com.blackducksoftware.search.repository.jdbc.impl.SearchDashboardProjectVersionRepository": {"1754906400": 780, "1754910000": 1168, "1754913600": 1228, "**********": 1224, "1754920800": 1244, "1754924400": 1584, "1754859600": 21, "**********": 84, "1754866800": 84, "**********": 104, "**********": 236, "**********": 200, "**********": 288, "**********": 316, "**********": 192, "**********": 160, "1754902800": 560, "**********": 1284, "**********": 280, "**********": 348, "**********": 816, "1754935200": 720, "**********": 1128, "1754942400": 988, "**********": 868, "**********": 796, "1754953200": 720, "**********": 572, "**********": 516, "1754964000": 384, "1754967600": 256, "1754971200": 140, "1754974800": 4}, "com.blackducksoftware.search.repository.jdbc.impl.SearchDashboardComponentRepository": {"1754906400": 816, "1754910000": 1168, "1754913600": 1228, "**********": 1224, "1754920800": 1244, "1754924400": 1584, "1754859600": 28, "**********": 84, "1754866800": 84, "**********": 104, "**********": 236, "**********": 200, "**********": 288, "**********": 316, "**********": 192, "**********": 160, "1754902800": 560, "**********": 1284, "**********": 280, "**********": 348, "**********": 816, "1754935200": 720, "**********": 1128, "1754942400": 988, "**********": 868, "**********": 796, "1754953200": 720, "**********": 572, "**********": 516, "1754964000": 384, "1754967600": 256, "1754971200": 140, "1754974800": 4}, "com.blackducksoftware.search.repository.jdbc.impl.SearchDashboardVulnerabilityRepository": {"1754906400": 408, "1754910000": 584, "1754913600": 614, "**********": 612, "1754920800": 622, "1754924400": 792, "1754859600": 14, "**********": 42, "1754866800": 42, "**********": 52, "**********": 118, "**********": 100, "**********": 144, "**********": 158, "**********": 96, "**********": 80, "1754902800": 280, "**********": 642, "**********": 140, "**********": 174, "**********": 408, "1754935200": 360, "**********": 564, "1754942400": 494, "**********": 434, "**********": 398, "1754953200": 360, "**********": 286, "**********": 258, "1754964000": 192, "1754967600": 128, "1754971200": 70, "1754974800": 2}, "com.blackducksoftware.scan.bom.job.ScanPurgeCheckJob": {"1754910000": 94, "1754913600": 99, "1754920800": 103, "**********": 14, "**********": 7, "**********": 14, "**********": 24, "**********": 24, "1754902800": 41, "**********": 54, "1754924400": 67, "**********": 73, "1754906400": 65, "**********": 41, "**********": 12, "**********": 29, "**********": 17, "**********": 31, "1754935200": 91, "**********": 66, "1754942400": 55, "**********": 75, "**********": 69, "1754953200": 58, "**********": 50, "**********": 49, "1754964000": 33, "1754967600": 23, "1754971200": 13, "1754974800": 1}, "com.blackducksoftware.scan.bom.job.ScanPurgeWorkflowJob": {"1754910000": 2068, "1754913600": 3033, "1754920800": 1811, "**********": 252, "**********": 133, "**********": 252, "**********": 456, "**********": 456, "1754902800": 782, "**********": 1393, "1754924400": 1139, "**********": 1229, "1754906400": 1478, "**********": 779, "**********": 264, "**********": 674, "**********": 323, "**********": 539, "1754935200": 1547, "**********": 1224, "1754942400": 935, "**********": 1227, "**********": 1243, "1754953200": 1044, "**********": 850, "**********": 881, "1754964000": 577, "1754967600": 437, "1754971200": 247, "1754974800": 19}, "com.blackducksoftware.codelocation.domain.api.PolicyBasedPurgeThrottle": {"1754910000": 188, "1754913600": 565, "1754920800": 127, "**********": 14, "**********": 7, "**********": 14, "**********": 24, "**********": 24, "1754902800": 58, "**********": 195, "1754924400": 79, "**********": 73, "1754906400": 154, "**********": 65, "**********": 24, "**********": 58, "**********": 17, "**********": 31, "1754935200": 91, "**********": 57, "1754942400": 54, "**********": 19, "**********": 69, "1754953200": 58, "**********": 50, "**********": 49, "1754964000": 33, "1754967600": 17, "1754971200": 13, "1754974800": 1}, "com.blackducksoftware.scan.siggen.impl.BlackDuckInputOutputService": {"1754910000": 342, "1754913600": 174, "1754920800": 442, "**********": 287, "**********": 112, "**********": 182, "**********": 277, "**********": 247, "**********": 157, "1754924400": 182, "**********": 213, "1754906400": 265, "**********": 250, "**********": 48, "**********": 111, "**********": 204, "1754902800": 34, "**********": 91, "1754935200": 481, "**********": 709, "1754942400": 695, "**********": 551, "**********": 621, "1754953200": 1219, "**********": 1231, "**********": 1160, "1754964000": 555, "1754967600": 250, "1754971200": 175, "1754974800": 14}, "com.blackducksoftware.scan.siggen.impl.ScanFailureReporter": {"1754910000": 2919, "1754913600": 1321, "**********": 14, "**********": 7, "**********": 35, "**********": 133, "**********": 167, "1754902800": 1050, "**********": 407, "1754906400": 2205, "**********": 549, "**********": 216, "**********": 1058, "1754920800": 12, "**********": 102, "**********": 51, "**********": 35, "1754953200": 55, "**********": 24, "1754964000": 8, "1754967600": 64, "1754971200": 39, "1754974800": 2}, "com.blackducksoftware.scan.bom.impl.ScanCompositeLeafApi": {"1754910000": 299, "1754913600": 268, "1754920800": 326, "**********": 140, "**********": 70, "**********": 140, "**********": 144, "**********": 185, "1754902800": 65, "**********": 188, "1754924400": 199, "**********": 213, "1754906400": 166, "**********": 135, "**********": 36, "**********": 46, "**********": 170, "**********": 136, "1754935200": 464, "**********": 509, "1754942400": 497, "**********": 338, "**********": 626, "1754953200": 580, "**********": 500, "**********": 384, "1754964000": 126, "1754967600": 62, "1754971200": 44, "1754974800": 3}, "com.blackducksoftware.job.quartz.jobs.WatchdogJob": {"1754910000": 1469, "**********": 1460, "1754920800": 1717, "1754859600": 161, "**********": 147, "1754866800": 168, "**********": 147, "**********": 310, "**********": 504, "**********": 360, "**********": 392, "**********": 530, "**********": 612, "**********": 569, "1754902800": 260, "1754906400": 1032, "1754913600": 1020, "1754924400": 880, "**********": 940, "**********": 132, "**********": 1500, "1754935200": 1518, "**********": 1006, "1754942400": 694, "**********": 1004, "**********": 1048, "1754953200": 859, "**********": 848, "**********": 672, "1754964000": 505, "1754967600": 352, "1754971200": 210, "1754974800": 10}, "com.blackducksoftware.kb.integration.domain.impl.KbUpdateCheckJob": {"1754913600": 73, "1754924400": 70, "**********": 7, "**********": 7, "**********": 7, "**********": 19, "1754902800": 19, "1754920800": 24, "**********": 69, "**********": 12, "**********": 12, "**********": 29, "1754910000": 24, "**********": 24, "**********": 17, "**********": 17, "1754906400": 17, "**********": 41, "1754935200": 17, "**********": 13, "1754942400": 47, "**********": 38, "**********": 35, "1754953200": 31, "**********": 26, "**********": 20, "1754964000": 15, "1754967600": 11, "1754971200": 7}, "com.blackducksoftware.kb.integration.domain.impl.KbUpdateWorkflowJob": {"1754913600": 1752, "1754924400": 1680, "**********": 259, "**********": 189, "**********": 189, "**********": 477, "1754902800": 456, "1754920800": 576, "**********": 1656, "**********": 360, "**********": 324, "**********": 732, "1754910000": 576, "**********": 576, "**********": 459, "**********": 459, "1754906400": 1167, "**********": 984, "1754935200": 408, "**********": 324, "1754942400": 1128, "**********": 912, "**********": 1010, "1754953200": 744, "**********": 669, "**********": 513, "1754964000": 360, "1754967600": 264, "1754971200": 168}, "com.blackducksoftware.kb.domain.impl.KbComponentUpdateRegistry": {"1754913600": 1752, "1754924400": 1680, "**********": 168, "**********": 168, "**********": 168, "**********": 456, "1754902800": 456, "1754920800": 576, "**********": 1656, "**********": 288, "**********": 288, "**********": 696, "1754910000": 576, "**********": 576, "**********": 408, "**********": 408, "1754906400": 804, "**********": 984, "1754935200": 408, "**********": 312, "1754942400": 1128, "**********": 912, "**********": 840, "1754953200": 744, "**********": 624, "**********": 480, "1754964000": 360, "1754967600": 264, "1754971200": 168}, "com.blackducksoftware.kb.integration.domain.impl.KbComponentUpdateJobTask": {"1754913600": 292, "1754924400": 280, "**********": 28, "**********": 28, "**********": 28, "**********": 76, "1754902800": 76, "1754920800": 96, "**********": 276, "**********": 48, "**********": 48, "**********": 116, "1754910000": 96, "**********": 96, "**********": 68, "**********": 68, "1754906400": 200, "**********": 164, "1754935200": 68, "**********": 52, "1754942400": 188, "**********": 152, "**********": 140, "1754953200": 124, "**********": 104, "**********": 80, "1754964000": 60, "1754967600": 44, "1754971200": 28}, "com.blackducksoftware.kb.integration.domain.impl.KbComponentActivityUpdateProcessor": {"1754913600": 146, "1754924400": 140, "**********": 14, "**********": 14, "**********": 14, "**********": 38, "1754902800": 38, "1754920800": 48, "**********": 138, "**********": 24, "**********": 24, "**********": 58, "1754910000": 48, "**********": 48, "**********": 34, "**********": 34, "1754906400": 100, "**********": 82, "1754935200": 34, "**********": 26, "1754942400": 94, "**********": 76, "**********": 70, "1754953200": 62, "**********": 52, "**********": 40, "1754964000": 30, "1754967600": 22, "1754971200": 14}, "com.blackducksoftware.bom.aggregate.impl.VersionBomKbComponentUpdateApi": {"1754913600": 146, "1754924400": 140, "**********": 14, "**********": 14, "**********": 14, "**********": 38, "1754902800": 38, "1754920800": 48, "**********": 138, "**********": 24, "**********": 24, "**********": 58, "1754910000": 48, "**********": 48, "**********": 34, "**********": 34, "1754906400": 100, "**********": 82, "1754935200": 34, "**********": 26, "1754942400": 94, "**********": 76, "**********": 70, "1754953200": 62, "**********": 52, "**********": 40, "1754964000": 30, "1754967600": 22, "1754971200": 14}, "com.blackducksoftware.kb.kbdetail.impl.KbVersionSecurityUpdateRegistry": {"1754913600": 949, "1754924400": 910, "**********": 182, "**********": 182, "**********": 182, "**********": 247, "1754902800": 247, "1754920800": 312, "**********": 897, "**********": 312, "**********": 156, "**********": 377, "1754910000": 312, "**********": 312, "**********": 442, "**********": 221, "1754906400": 650, "**********": 533, "1754935200": 221, "**********": 221, "1754942400": 611, "**********": 494, "**********": 455, "1754953200": 403, "**********": 338, "**********": 260, "1754964000": 195, "1754967600": 143, "1754971200": 91}, "com.blackducksoftware.kb.integration.domain.impl.KbComponentVersionSecurityUpdateJobTask": {"1754913600": 146, "1754924400": 140, "**********": 28, "**********": 28, "**********": 28, "**********": 38, "1754902800": 38, "1754920800": 48, "**********": 138, "**********": 48, "**********": 24, "**********": 58, "1754910000": 48, "**********": 48, "**********": 68, "**********": 34, "1754906400": 100, "**********": 82, "1754935200": 34, "**********": 34, "1754942400": 94, "**********": 76, "**********": 70, "1754953200": 62, "**********": 52, "**********": 40, "1754964000": 30, "1754967600": 22, "1754971200": 14}, "com.blackducksoftware.kb.kbvuln.impl.KbNvdVulnerabilityUpdateRegistry": {"1754913600": 146, "1754924400": 140, "**********": 14, "**********": 14, "**********": 14, "**********": 38, "1754902800": 38, "1754920800": 48, "**********": 138, "**********": 24, "**********": 48, "**********": 58, "1754910000": 48, "**********": 48, "**********": 34, "**********": 34, "1754906400": 100, "**********": 82, "1754935200": 34, "**********": 26, "1754942400": 94, "**********": 76, "**********": 70, "1754953200": 62, "**********": 52, "**********": 40, "1754964000": 30, "1754967600": 22, "1754971200": 14}, "com.blackducksoftware.kb.integration.domain.impl.KbNvdVulnerabilityUpdateJobTask": {"1754913600": 146, "1754924400": 140, "**********": 14, "**********": 14, "**********": 14, "**********": 38, "1754902800": 38, "1754920800": 48, "**********": 138, "**********": 24, "**********": 48, "**********": 58, "1754910000": 48, "**********": 48, "**********": 34, "**********": 34, "1754906400": 100, "**********": 82, "1754935200": 34, "**********": 26, "1754942400": 94, "**********": 76, "**********": 70, "1754953200": 62, "**********": 52, "**********": 40, "1754964000": 30, "1754967600": 22, "1754971200": 14}, "com.blackducksoftware.kb.kbvuln.impl.KbBdsaVulnerabilityUpdateRegistry": {"1754913600": 146, "1754924400": 140, "**********": 14, "**********": 14, "**********": 14, "**********": 52, "1754902800": 38, "1754920800": 48, "**********": 138, "**********": 48, "**********": 24, "**********": 82, "1754910000": 48, "**********": 48, "**********": 34, "**********": 68, "1754906400": 100, "**********": 82, "1754935200": 34, "**********": 26, "1754942400": 94, "**********": 76, "**********": 70, "1754953200": 62, "**********": 82, "**********": 62, "1754964000": 30, "1754967600": 22, "1754971200": 14}, "com.blackducksoftware.kb.integration.domain.impl.KbBdsaVulnerabilityUpdateJobTask": {"1754913600": 146, "1754924400": 140, "**********": 14, "**********": 14, "**********": 14, "**********": 52, "1754902800": 38, "1754920800": 48, "**********": 138, "**********": 48, "**********": 24, "**********": 82, "1754910000": 48, "**********": 48, "**********": 34, "**********": 68, "1754906400": 100, "**********": 82, "1754935200": 34, "**********": 26, "1754942400": 94, "**********": 76, "**********": 70, "1754953200": 62, "**********": 82, "**********": 62, "1754964000": 30, "1754967600": 22, "1754971200": 14}, "com.blackducksoftware.bom.aggregate.domain.impl.job.BomAggregatePurgeOrphansCheckJob": {"1754913600": 37, "**********": 33}, "com.blackducksoftware.bom.aggregate.domain.impl.BomAggregatePurgeOrphansApi": {"1754913600": 111, "**********": 99}, "com.blackducksoftware.bom.aggregate.domain.impl.job.QuartzVersionBomEventCleanupJob": {"1754920800": 74, "**********": 74, "**********": 14, "**********": 14, "1754910000": 14, "1754913600": 72, "**********": 24, "**********": 24, "1754924400": 24, "**********": 34, "1754902800": 34, "1754906400": 34, "**********": 28, "1754935200": 54, "**********": 18, "1754942400": 50, "**********": 46, "**********": 36, "1754953200": 30, "**********": 28, "**********": 18, "1754964000": 16, "1754967600": 16, "1754971200": 8, "1754974800": 2}, "com.blackducksoftware.report.db.job.ReportingDatabaseTransferCheckJob": {"1754859600": 7, "**********": 12, "**********": 20}, "com.blackducksoftware.report.db.domain.impl.ReportingViewApi": {"1754859600": 308, "**********": 528, "**********": 880}, "com.blackducksoftware.kb.integration.domain.impl.KbVersionSecurityActivityUpdateProcessor": {"**********": 14, "**********": 14, "**********": 14, "**********": 24, "**********": 34, "**********": 8}, "com.blackducksoftware.bom.aggregate.impl.VersionBomArchivedKbVersionSecurityUpdateApi": {"**********": 7, "**********": 7, "**********": 7, "**********": 12, "**********": 17, "**********": 4}, "com.blackducksoftware.vuln.impl.AutoRemediateUnmappedApi": {"**********": 7, "**********": 7, "**********": 7, "**********": 12, "**********": 17}, "com.blackducksoftware.bom.domain.impl.VersionBomRiskWarningApi": {"**********": 14, "**********": 14, "**********": 14, "**********": 24, "**********": 34}, "com.blackducksoftware.bom.aggregate.impl.VersionBomKbVersionSecurityUpdateApi": {"**********": 21, "**********": 21, "**********": 21, "**********": 36, "**********": 51, "**********": 4}, "com.blackducksoftware.kb.kbdetail.impl.KbVersionLicenseUpdateRegistry": {"**********": 7, "**********": 17}, "com.blackducksoftware.kb.integration.domain.impl.KbComponentVersionLicenseUpdateJobTask": {"**********": 14, "**********": 34}, "com.blackducksoftware.kb.kbdetail.impl.KbLicenseUpdateRegistry": {"**********": 35, "**********": 85}, "com.blackducksoftware.kb.integration.domain.impl.KbLicenseUpdateJobTask": {"**********": 14, "**********": 34}, "com.blackducksoftware.job.integration.domain.impl.NotificationPurgeCheckJob": {"**********": 7}, "com.blackducksoftware.kb.integration.domain.impl.KbBdsaVulnerabilityActivityUpdateProcessor": {"**********": 14, "**********": 24, "**********": 24, "**********": 34, "**********": 30, "**********": 22}, "com.blackducksoftware.bom.aggregate.impl.VersionBomVulnerabilityUpdateApi": {"**********": 14, "**********": 24, "**********": 24, "**********": 24, "**********": 34, "**********": 30, "**********": 22}, "com.blackducksoftware.storage.domain.impl.StoragePrunerCheckJob": {"**********": 14, "**********": 34, "**********": 2, "**********": 38, "**********": 24}, "com.blackducksoftware.storage.domain.impl.StoragePrunerWorkflowJob": {"**********": 98, "**********": 442, "**********": 640, "**********": 437, "**********": 240}, "com.blackducksoftware.scan.bom.job.ScanStatisticPurgeJob": {"**********": 14}, "com.blackducksoftware.report.integration.domain.impl.QuartzReportPurgeJob": {"**********": 48}, "org.springframework.scheduling.quartz.LocalDataSourceJobStore": {"**********": 12}, "com.blackducksoftware.kb.integration.domain.impl.KbNvdVulnerabilityActivityUpdateProcessor": {"**********": 24}, "com.blackducksoftware.bom.aggregate.domain.impl.job.PolicyRuleScheduledChangeCheckJob": {"1754910000": 12, "**********": 4, "1754967600": 5}, "com.blackducksoftware.job.quartz.jobs.JobHistoryStatsJob": {"**********": 17, "**********": 34, "**********": 16, "**********": 11}, "com.blackducksoftware.usermgmt.domain.impl.job.ApiTokenPurgeCheckJob": {"**********": 8}, "com.blackducksoftware.usermgmt.domain.impl.job.ApiTokenPurgeJob": {"**********": 8}, "com.blackducksoftware.bom.aggregate.job.CollectScanStatsJob": {"**********": 208}, "com.blackducksoftware.core.rest.repository.rest.impl.SystemAuthenticationRestClient": {"**********": 56}}}