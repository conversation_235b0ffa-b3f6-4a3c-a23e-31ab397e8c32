"""
统计webserver access-log中API的访问次数
"""

import re
import sys
import time
from collections import defaultdict
from datetime import datetime
import argparse
import json
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd
from tqdm import tqdm


def normalize_api_path(path):
    """
    将API路径标准化，替换UUID和数字ID为占位符
    """
    # 替换UUID格式 (8-4-4-4-12位十六进制数)
    normalized_path = re.sub(r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', '<uuid>', path)

    # 替换纯数字ID
    normalized_path = re.sub(r'/\d+(?=/|$)', '/<id>', normalized_path)

    return normalized_path


def parse_log_line(line, user_agent_filter=None):
    """
    解析日志行
    user_agent_filter: 用户代理过滤条件，只统计包含该字符串的日志行
    返回：(时间戳, API路径, HTTP方法, 状态码, 用户代理)或None
    """
    try:
        # 如果需要过滤用户代理，先检查是否包含
        if user_agent_filter:
            if user_agent_filter not in line:
                return None

        # 解析用户代理
        user_agent_match = re.search(r'"([^"]+)"$', line) or re.search(r'"-"\s+"([^"]+)"', line)
        if not user_agent_match:
            return None

        user_agent = user_agent_match.group(1)

        # 解析时间戳
        timestamp_match = re.search(r'\[(\d+/\w+/\d+:\d+:\d+:\d+\s+[+\-]\d+)\]', line)
        if not timestamp_match:
            return None

        timestamp_str = timestamp_match.group(1)
        time_obj = datetime.strptime(timestamp_str, '%d/%b/%Y:%H:%M:%S %z')

        # 解析HTTP请求
        request_match = re.search(r'"(\w+)\s+([^"\s]+)\s+HTTP/\d\.\d"', line)
        if not request_match:
            return None

        method = request_match.group(1)
        path = request_match.group(2)

        # 解析状态码
        status_match = re.search(r'"\s+(\d+)\s+', line)
        if not status_match:
            return None

        status_code = int(status_match.group(1))

        # 标准化API路径
        normalized_path = normalize_api_path(path)

        return (time_obj, normalized_path, method, status_code, user_agent)

    except Exception as e:
        print(f"解析错误: {e}, 行: {line[:100]}...")
        return None


def analyze_log_file(file_path, time_interval=3600, user_agent_filter=None):
    """
    分析日志文件，计算API调用次数和频率
    time_interval: 统计时间间隔（秒），默认1小时
    user_agent_filter: 用户代理过滤条件，只统计包含该字符串的日志行
    """
    api_stats = defaultdict(int)  # API总调用次数
    api_timeseries = defaultdict(lambda: defaultdict(int))  # 按时间间隔的API调用次数
    status_codes = defaultdict(lambda: defaultdict(int))  # API状态码统计
    methods = defaultdict(lambda: defaultdict(int))  # API方法统计
    user_agents = defaultdict(lambda: defaultdict(int))  # API用户代理统计

    min_time = None
    max_time = None
    total_lines = 0
    parsed_lines = 0
    filtered_lines = 0

    # 获取文件大小用于进度显示
    file_size = Path(file_path).stat().st_size

    with open(file_path, 'r') as f:
        line_iterator = tqdm(f, total=file_size, unit='B', unit_scale=True, desc="分析日志")
        for line in line_iterator:
            total_lines += 1
            line_iterator.update(len(line))

            parsed_data = parse_log_line(line, user_agent_filter)
            if not parsed_data:
                continue

            parsed_lines += 1
            filtered_lines += 1
            time_obj, api_path, method, status_code, user_agent = parsed_data

            # 更新最早和最晚时间
            if min_time is None or time_obj < min_time:
                min_time = time_obj
            if max_time is None or time_obj > max_time:
                max_time = time_obj

            # 计算时间区间索引（以秒为单位）
            time_bucket = int(time_obj.timestamp() // time_interval * time_interval)

            # 更新统计数据
            api_stats[api_path] += 1
            api_timeseries[api_path][time_bucket] += 1
            status_codes[api_path][status_code] += 1
            methods[api_path][method] += 1
            user_agents[api_path][user_agent] += 1

    # 计算总持续时间（秒）
    if min_time and max_time:
        duration = (max_time - min_time).total_seconds()
    else:
        duration = 0

    # 计算每个API的调用频率（每秒请求数）
    api_frequencies = {}
    for api_path, count in api_stats.items():
        frequency = count / duration if duration > 0 else 0
        api_frequencies[api_path] = frequency

    results = {
        "总行数": total_lines,
        "解析成功行数": parsed_lines,
        "符合过滤条件行数": filtered_lines,
        "过滤条件": user_agent_filter if user_agent_filter else "无",
        "开始时间": min_time.isoformat() if min_time else None,
        "结束时间": max_time.isoformat() if max_time else None,
        "总持续时间(秒)": duration,
        "API调用统计": [
            {
                "api": api_path,
                "总调用次数": count,
                "频率(每秒)": api_frequencies[api_path],
                "状态码分布": dict(status_codes[api_path]),
                "HTTP方法分布": dict(methods[api_path]),
                "用户代理分布": dict(user_agents[api_path])
            }
            for api_path, count in sorted(api_stats.items(), key=lambda x: x[1], reverse=True)
        ],
        "时间序列数据": {api: dict(times) for api, times in api_timeseries.items()}
    }

    return results


def generate_plots(results, output_dir):
    """
    生成可视化图表
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)

    # 1. 调用频率条形图
    plt.figure(figsize=(12, 8))
    api_data = results["API调用统计"]

    if not api_data:  # 检查是否有数据
        print("没有数据可供绘图")
        return

    apis = [item["api"] for item in api_data]
    frequencies = [item["频率(每秒)"] for item in api_data]

    # 对长API路径进行截断
    shortened_apis = [api[-50:] if len(api) > 50 else api for api in apis]

    # 限制显示的API数量，避免图表过于拥挤
    max_apis_to_show = min(20, len(apis))

    plt.barh(shortened_apis[:max_apis_to_show], frequencies[:max_apis_to_show])
    plt.title(f"API调用频率 (请求/秒) - 过滤条件: {results['过滤条件']}")
    plt.xlabel("频率 (请求/秒)")
    plt.ylabel("API路径")
    plt.tight_layout()
    plt.savefig(output_dir / "api_frequencies.png")
    plt.close()

    # 2. 调用总次数饼图 (仅显示前10个)
    if len(api_data) > 0:  # 确保有数据
        plt.figure(figsize=(10, 10))
        counts = [item["总调用次数"] for item in api_data[:10]]
        labels = [f"{api[-30:]}... ({count})" if len(api) > 30 else f"{api} ({count})"
                  for api, count in zip(apis[:10], counts)]

        plt.pie(counts, labels=labels, autopct='%1.1f%%')
        plt.title(f"前10个API调用次数分布 - 过滤条件: {results['过滤条件']}")
        plt.tight_layout()
        plt.savefig(output_dir / "api_counts_pie.png")
        plt.close()

    # 3. 时间序列图 (仅显示前5个最常调用的API)
    plt.figure(figsize=(15, 8))
    time_data = results["时间序列数据"]

    has_data = False
    for i, api in enumerate(apis[:5]):
        if api in time_data and time_data[api]:
            times = sorted(time_data[api].keys())
            if not times:
                continue

            counts = [time_data[api][t] for t in times]

            # 将时间戳转换为可读格式
            readable_times = [datetime.fromtimestamp(t).strftime('%Y-%m-%d %H:%M') for t in times]

            plt.plot(readable_times, counts, marker='o', label=api[-30:] if len(api) > 30 else api)
            has_data = True

    if has_data:
        plt.title(f"前5个API的调用次数随时间变化 - 过滤条件: {results['过滤条件']}")
        plt.xlabel("时间")
        plt.ylabel("调用次数")
        plt.xticks(rotation=45)
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(output_dir / "api_timeseries.png")
    plt.close()

    # 4. 状态码分布图 (仅显示前5个最常调用的API)
    for i, item in enumerate(api_data[:5]):
        api = item["api"]
        status_dist = item["状态码分布"]

        if status_dist:
            plt.figure(figsize=(8, 6))
            status_codes = list(status_dist.keys())
            counts = list(status_dist.values())

            plt.bar(status_codes, counts)
            plt.title(f"API '{api[-30:]}...' 状态码分布 - 过滤条件: {results['过滤条件']}"
                      if len(api) > 30 else f"API '{api}' 状态码分布 - 过滤条件: {results['过滤条件']}")
            plt.xlabel("HTTP状态码")
            plt.ylabel("次数")
            plt.tight_layout()
            plt.savefig(output_dir / f"status_dist_api_{i + 1}.png")
            plt.close()


def main():
    parser = argparse.ArgumentParser(description="分析API访问日志")
    parser.add_argument("log_file", help="日志文件路径")
    parser.add_argument("--interval", type=int, default=3600,
                        help="时间间隔（秒）用于统计频率，默认3600秒（1小时）")
    parser.add_argument("--output", default="log_analysis_results",
                        help="输出结果的目录，默认为'log_analysis_results'")
    parser.add_argument("--filter", default="Go-http-client/1.1",
                        help="用户代理过滤条件，只统计包含该字符串的日志行，默认为'Go-http-client/1.1'")
    parser.add_argument("--no-filter", action="store_true",
                        help="不应用任何过滤器，统计所有日志行")
    args = parser.parse_args()

    start_time = time.time()
    print(f"开始分析日志文件: {args.log_file}")

    # 确定过滤条件
    user_agent_filter = None if args.no_filter else args.filter
    filter_desc = "无" if user_agent_filter is None else user_agent_filter
    print(f"应用过滤条件: {filter_desc}")

    try:
        results = analyze_log_file(args.log_file, args.interval, user_agent_filter)

        # 输出结果到JSON文件
        output_dir = Path(args.output)
        output_dir.mkdir(exist_ok=True, parents=True)

        with open(output_dir / "results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 生成报表
        df = pd.DataFrame(results["API调用统计"])
        if not df.empty:
            df.to_csv(output_dir / "api_stats.csv", index=False)
        else:
            print("没有匹配的数据用于生成CSV报表")

        # 生成可视化图表
        generate_plots(results, output_dir)

        # 打印摘要
        print("\n分析完成!")
        print(f"总行数: {results['总行数']}")
        print(f"解析成功行数: {results['解析成功行数']}")
        print(f"符合过滤条件行数: {results['符合过滤条件行数']}")
        print(f"过滤条件: {results['过滤条件']}")
        print(f"开始时间: {results['开始时间']}")
        print(f"结束时间: {results['结束时间']}")
        print(f"总持续时间: {results['总持续时间(秒)']:.2f} 秒")

        api_stats = results["API调用统计"]
        if api_stats:
            print("\n前10个最常调用的API:")
            for i, item in enumerate(api_stats[:min(10, len(api_stats))], 1):
                print(f"{i}. {item['api']} - {item['总调用次数']} 次 ({item['频率(每秒)']:.4f} 请求/秒)")
        else:
            print("\n没有找到符合过滤条件的API调用")

        print(f"\n详细结果已保存到目录: {output_dir}")

    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

    end_time = time.time()
    print(f"分析耗时: {end_time - start_time:.2f} 秒")


if __name__ == "__main__":
    main()