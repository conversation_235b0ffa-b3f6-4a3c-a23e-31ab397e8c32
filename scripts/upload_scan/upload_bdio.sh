#!/bin/bash

# Set parameters
api_server=$1
file_path=$2
token=$3

# Check parameters
if [[ -z "$api_server" || -z "$file_path" || -z "$token" ]]; then
  echo "Usage: $0 <api_server> <file_path> <token>"
  exit 1
fi

# Get Bearer Token
response=$(curl -H "Authorization: token $token" --insecure -X POST "$api_server/api/tokens/authenticate")
bearerToken=$(echo "$response" | jq -r '.bearerToken')

if [[ -z "$bearerToken" || "$bearerToken" == "null" ]]; then
  echo "Failed to get a valid token"
  exit 1
fi

# Use Bearer Token to upload the file
curl -v -X POST -H "Authorization: Bearer $bearerToken" -H "Accept: application/vnd.blackducksoftware.bdio+json" --insecure "$api_server/api/scan/data" -F "file=@$file_path"