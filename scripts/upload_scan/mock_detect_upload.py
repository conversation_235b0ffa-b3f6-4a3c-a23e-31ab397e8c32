"""
模拟Detect上传
通过如下API：api/intelligent-persistence-scans
"""

import requests
import json
import os
from urllib.parse import urljoin


def generate_bearer_token(base_url, access_token):
    auth_url = urljoin(base_url, 'api/tokens/authenticate')
    r = requests.post(auth_url, headers={
        'Authorization': f'token {access_token}'
    }, verify=False)
    return r.json()['bearerToken']


def upload_scan_file(base_url, bearerToken, file_path):
    """
    Upload a scan file
    """
    upload_url = urljoin(base_url, 'api/intelligent-persistence-scans')

    headers = {
        'User-Agent': 'synopsys_detect/9.0.0 BlackDuckCommon/66.2.7 (Ubuntu 11.0.23 amd64 Linux 5.15.0-1058-aws)',
        'Authorization': f'Bearer {bearerToken}',
        'Content-Type': 'application/vnd.blackducksoftware.intelligent-persistence-scan-1-ld-2+json',
        'Accept': 'application/json'
    }

    with open(file_path, 'r') as f:
        data = json.load(f)

    r = requests.post(upload_url, data=json.dumps(data), headers=headers, verify=False)
    # r = requests.post(upload_url, files={'file': f}, headers=headers, verify=False)

    print(r.status_code)
    print(r.headers)


if __name__ == '__main__':
    base_url = 'https://54.199.136.232'
    access_token = 'ZGVkMmYwNGQtNjUyNy00NWZiLWExNmItMzUyZmJhZThmNjhlOjU5ZmRlOTRjLTI4ZTQtNDk5My04NTc1LTI3NGZhMjYyMTQzZQ=='
    bearerToken = generate_bearer_token(base_url, access_token)
    upload_scan_file(base_url, bearerToken, r'testbdio/testupload1_3_0/bdio-header.jsonld')