{"@id": "urn:uuid:5b969088-1e6a-33fa-988d-48f6c9f52e1f", "@type": "PACKAGE_MANAGER", "@graph": [{"@id": "http:pypi/%2Fhome%2Fubuntu%2Fsourcecode%2Fpython%2Fpython/-pip", "@type": "https://blackducksoftware.github.io/bdio#Project", "https://blackducksoftware.github.io/bdio#hasName": "/home/<USER>/sourcecode/python/python", "https://blackducksoftware.github.io/bdio#hasIdentifier": "/home/<USER>/sourcecode/python/python/-pip", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi", "https://blackducksoftware.github.io/bdio#hasDependency": [{"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/six/1.16.0"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/PyYAML/6.0.1"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/websocket-client/1.8.0"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/requests-oauthlib/2.0.0"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/python-dateutil/2.9.0.post0"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/certifi/2024.6.2"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/google-auth/2.30.0"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/urllib3/2.2.2"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/setuptools/69.5.1"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/requests/2.32.3"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/oauthlib/3.2.2"}}]}, {"@id": "http:pypi/oauthlib/3.2.2", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "<PERSON><PERSON><PERSON><PERSON>", "https://blackducksoftware.github.io/bdio#hasVersion": "3.2.2", "https://blackducksoftware.github.io/bdio#hasIdentifier": "oauthlib/3.2.2", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/requests/2.32.3", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "requests", "https://blackducksoftware.github.io/bdio#hasVersion": "2.32.3", "https://blackducksoftware.github.io/bdio#hasIdentifier": "requests/2.32.3", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi", "https://blackducksoftware.github.io/bdio#hasDependency": [{"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/idna/3.7"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/charset-normalizer/3.3.2"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/certifi/2024.6.2"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/urllib3/2.2.2"}}]}, {"@id": "http:pypi/urllib3/2.2.2", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "urllib3", "https://blackducksoftware.github.io/bdio#hasVersion": "2.2.2", "https://blackducksoftware.github.io/bdio#hasIdentifier": "urllib3/2.2.2", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/certifi/2024.6.2", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "certifi", "https://blackducksoftware.github.io/bdio#hasVersion": "2024.6.2", "https://blackducksoftware.github.io/bdio#hasIdentifier": "certifi/2024.6.2", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/charset-normalizer/3.3.2", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "charset-normalizer", "https://blackducksoftware.github.io/bdio#hasVersion": "3.3.2", "https://blackducksoftware.github.io/bdio#hasIdentifier": "charset-normalizer/3.3.2", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/idna/3.7", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "idna", "https://blackducksoftware.github.io/bdio#hasVersion": "3.7", "https://blackducksoftware.github.io/bdio#hasIdentifier": "idna/3.7", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/setuptools/69.5.1", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "setuptools", "https://blackducksoftware.github.io/bdio#hasVersion": "69.5.1", "https://blackducksoftware.github.io/bdio#hasIdentifier": "setuptools/69.5.1", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/google-auth/2.30.0", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "google-auth", "https://blackducksoftware.github.io/bdio#hasVersion": "2.30.0", "https://blackducksoftware.github.io/bdio#hasIdentifier": "google-auth/2.30.0", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi", "https://blackducksoftware.github.io/bdio#hasDependency": [{"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/cachetools/5.3.3"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/pyasn1-modules/0.4.0"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/rsa/4.9"}}]}, {"@id": "http:pypi/rsa/4.9", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "rsa", "https://blackducksoftware.github.io/bdio#hasVersion": "4.9", "https://blackducksoftware.github.io/bdio#hasIdentifier": "rsa/4.9", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi", "https://blackducksoftware.github.io/bdio#hasDependency": {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/pyasn1/0.6.0"}}}, {"@id": "http:pypi/pyasn1/0.6.0", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "pyasn1", "https://blackducksoftware.github.io/bdio#hasVersion": "0.6.0", "https://blackducksoftware.github.io/bdio#hasIdentifier": "pyasn1/0.6.0", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/pyasn1-modules/0.4.0", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "pyasn1-modules", "https://blackducksoftware.github.io/bdio#hasVersion": "0.4.0", "https://blackducksoftware.github.io/bdio#hasIdentifier": "pyasn1-modules/0.4.0", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi", "https://blackducksoftware.github.io/bdio#hasDependency": {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/pyasn1/0.6.0"}}}, {"@id": "http:pypi/cachetools/5.3.3", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "cachetools", "https://blackducksoftware.github.io/bdio#hasVersion": "5.3.3", "https://blackducksoftware.github.io/bdio#hasIdentifier": "cachetools/5.3.3", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/python-dateutil/2.9.0.post0", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "python-dateutil", "https://blackducksoftware.github.io/bdio#hasVersion": "2.9.0.post0", "https://blackducksoftware.github.io/bdio#hasIdentifier": "python-dateutil/2.9.0.post0", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi", "https://blackducksoftware.github.io/bdio#hasDependency": {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/six/1.16.0"}}}, {"@id": "http:pypi/six/1.16.0", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "six", "https://blackducksoftware.github.io/bdio#hasVersion": "1.16.0", "https://blackducksoftware.github.io/bdio#hasIdentifier": "six/1.16.0", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/requests-oauthlib/2.0.0", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "requests-o<PERSON><PERSON><PERSON>", "https://blackducksoftware.github.io/bdio#hasVersion": "2.0.0", "https://blackducksoftware.github.io/bdio#hasIdentifier": "requests-oauthlib/2.0.0", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi", "https://blackducksoftware.github.io/bdio#hasDependency": [{"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/requests/2.32.3"}}, {"@type": "https://blackducksoftware.github.io/bdio#Dependency", "https://blackducksoftware.github.io/bdio#dependsOn": {"@id": "http:pypi/oauthlib/3.2.2"}}]}, {"@id": "http:pypi/websocket-client/1.8.0", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "websocket-client", "https://blackducksoftware.github.io/bdio#hasVersion": "1.8.0", "https://blackducksoftware.github.io/bdio#hasIdentifier": "websocket-client/1.8.0", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:pypi/PyYAML/6.0.1", "@type": "https://blackducksoftware.github.io/bdio#Component", "https://blackducksoftware.github.io/bdio#hasName": "PyYAML", "https://blackducksoftware.github.io/bdio#hasVersion": "6.0.1", "https://blackducksoftware.github.io/bdio#hasIdentifier": "PyYAML/6.0.1", "https://blackducksoftware.github.io/bdio#hasNamespace": "pypi"}, {"@id": "http:detect/testupload1/3.0", "@type": "https://blackducksoftware.github.io/bdio#Project", "https://blackducksoftware.github.io/bdio#hasIdentifier": "testupload1/3.0", "https://blackducksoftware.github.io/bdio#hasName": "testupload1", "https://blackducksoftware.github.io/bdio#hasVersion": "3.0", "https://blackducksoftware.github.io/bdio#hasNamespace": "root", "https://blackducksoftware.github.io/bdio#hasSubproject": {"@id": "http:pypi/%2Fhome%2Fubuntu%2Fsourcecode%2Fpython%2Fpython/-pip"}}]}