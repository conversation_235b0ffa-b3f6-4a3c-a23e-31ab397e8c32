import os
from urllib.parse import urljoin

import requests
from blackduck.HubRestApi import HubInstance

username = 'sysadmin'
password = 'blackduck'
urlbase = 'https://10.132.63.29'

hub = HubInstance(urlbase, username, password, insecure=True)

# url_base = hub.get_urlbase()
# headers = hub.get_headers()


def upload_scan_file(file_path):
    """
    Upload a scan file
    """
    base_url = hub.get_urlbase()
    upload_url = urljoin(base_url, 'api/scan/data')
    if not os.path.isfile(file_path):
        raise FileNotFoundError('Cannot find file {}'.format(file_path))
    headers = hub.get_headers()
    # headers = {
    #     # 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.96 Safari/537.36',
    #     # 'X-CSRF-TOKEN': self.x_csrf_token,
    #     'Accept': 'application/json',
    #     # 'Accept-Encoding': 'gzip, deflate, br'
    #     # 'Accept-Language: en,zh-CN;q=0.9,zh;q=0.8,en-US;q=0.7',
    #     # 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryFSBapu8HCpvw8ia5',
    #     # 'X-Requested-With': 'XMLHttpRequest'
    # }
    print(headers)
    print(upload_url)
    with open(file_path, 'rb') as f:
        r = requests.post(upload_url, files={'file': f}, headers=headers, verify=False)
    print(r.text)
    print(r.status_code)
    return r


if __name__ == '__main__':
    upload_scan_file(r'C:\Projects\Blackduck\Support Cases\2022\Synopsys Inc - HQ - Encounter the URI must be absolute error when upload the BDIO file\test\us1a-sup-cn-hub05.nprd.sig.synopsys.com-flask-main-2022-09-14T022836.256Z.bdio')

