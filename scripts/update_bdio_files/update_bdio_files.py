"""
A sample for extracting the bdio files (.zip) and update the content and finally
regenerate new bdio files
The script is aimed to fix an issue in https://jira-sig.internal.synopsys.com/browse/HUB-33551.
The steps are:
1. unzip the bdio file;
2. edit bdio-header.jsonld and add the scan name to the file
```
  "https://blackducksoftware.github.io/bdio#hasName" : [ {
    "@value" : "source/TNMS/PreCheck scan"
  } ],
```
3. zip the updated bido file
"""
import zipfile
import json
import os
import shutil


def process_bdio_file(bdio_fp, scan_name, bdio_output_path=None):
    bdio_basename = os.path.basename(bdio_fp)
    bdio_dirname = os.path.dirname(bdio_fp)
    # unzip bdio file
    with zipfile.ZipFile(bdio_fp, 'r') as zip_ref:
        extract_dir = bdio_basename.rstrip('.bdio')
        if bdio_output_path is not None:
            bdio_output_path = os.path.join(bdio_output_path, extract_dir)
        else:
            bdio_output_path = os.path.join(bdio_dirname, extract_dir)
        zip_ref.extractall(bdio_output_path)

    # Update bdio file
    bdio_header_fp = os.path.join(bdio_output_path, 'bdio-header.jsonld')
    bdio_header_data = json.load(open(bdio_header_fp))
    scan_name_key = 'https://blackducksoftware.github.io/bdio#hasName'
    if scan_name_key not in bdio_header_data:
        bdio_header_data[scan_name_key] = [{"@value": scan_name}]
    json.dump(bdio_header_data, open(bdio_header_fp, 'w'), indent=2)

    # zip the updated bdio file
    shutil.make_archive(bdio_output_path, 'zip', bdio_output_path)
    os.rename(bdio_output_path + '.zip', bdio_output_path + '.bdio')


if __name__ == '__main__':
    bdio_output_path = r'C:\Users\<USER>\Desktop\temp\bdio\updated'
    bdio_path = r'C:\Users\<USER>\Desktop\temp\bdio'

    # scan_map_fp = os.path.join(bdio_path, 'scan_map.json')
    # scan_map = json.load(open(scan_map_fp))
    for file in os.listdir(bdio_path):
        # if file not in scan_map or not scan_map[file].endswith(' scan'):
        #     continue
        bdio_fp = os.path.join(bdio_path, file)
        # scan_name = scan_map[file]
        scan_name = 'test_00941859'
        try:
            process_bdio_file(bdio_fp, scan_name, bdio_output_path)
        except zipfile.BadZipfile:
            print(file)
