import requests
import json
import urllib3
import sys
import logging
import argparse
from urllib.parse import urljoin, urlparse, urlunparse

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 禁用不安全的 HTTPS 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def ensure_port(url, default_port=443):
    """確保 URL 包含端口"""
    parsed = urlparse(url)
    if not parsed.port:
        netloc = f"{parsed.hostname}:{default_port}"
        parsed = parsed._replace(netloc=netloc)
    return urlunparse(parsed)


def login(base_url, username, password):
    """登入 Black Duck 伺服器"""
    session = requests.Session()
    login_url = urljoin(base_url, "/j_spring_security_check")
    login_data = {"j_username": username, "j_password": password}
    try:
        response = session.post(login_url, data=login_data, verify=False, allow_redirects=False, timeout=10)
        response.raise_for_status()
        logging.info("登入成功")
        # return session
        return response
    except requests.exceptions.RequestException as e:
        logging.error(f"登入失敗: {str(e)}")
        sys.exit(1)


# def get_headers(session):
def get_headers(response):
    """獲取包含認證信息的標頭"""
    headers = {
        # 'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    bearer_token = next((cookie.value for cookie in response.cookies if cookie.name == 'AUTHORIZATION_BEARER'), None)
    if bearer_token:
        # headers['Authorization'] = f'Bearer {bearer_token}'
        headers['authorization'] = f'Bearer {bearer_token}'
    headers['X-CSRF-TOKEN'] = response.headers.get('X-CSRF-TOKEN')
    print(headers)
    return headers


def get_project_version_ids(session, base_url, headers, project_name, version_name):
    """獲取指定專案和版本的ID"""
    projects_url = urljoin(base_url, "/api/projects")
    session = requests.Session()
    try:
        response = session.get(projects_url, headers=headers, params={'q': f"name:{project_name}"}, verify=False,
                               timeout=10)
        response.raise_for_status()
        projects = response.json().get('items', [])

        if not projects:
            logging.error(f"找不到名為 '{project_name}' 的專案。")
            return None, None

        project = projects[0]
        project_id = project['_meta']['href'].split('/')[-1]

        versions_url = urljoin(base_url, project['_meta']['href'] + "/versions")
        response = session.get(versions_url, headers=headers, params={'q': f"versionName:{version_name}"}, verify=False,
                               timeout=10)
        response.raise_for_status()
        versions = response.json().get('items', [])

        if not versions:
            logging.error(f"在專案 '{project_name}' 中找不到名為 '{version_name}' 的版本。")
            return None, None

        version = versions[0]
        version_id = version['_meta']['href'].split('/')[-1]

        return project_id, version_id
    except requests.exceptions.RequestException as e:
        logging.error(f"獲取專案和版本ID失敗: {str(e)}")
        return None, None


def get_snippet_info(session, base_url, headers, project_id, version_id):
    """獲取指定專案版本的所有snippet資訊"""
    url = urljoin(base_url, f"/api/internal/projects/{project_id}/versions/{version_id}/source-bom-entries")
    params = {
        'filter': 'bomMatchType:snippet',
        'limit': 100,
        'offset': 0
    }

    all_snippets = []

    session = requests.Session()

    try:
        while True:
            logging.info(f"正在請求 URL: {url}")
            logging.info(f"請求參數: {params}")

            response = session.get(url, headers=headers, params=params, verify=False, timeout=10)
            response.raise_for_status()

            data = response.json()
            logging.info(f"API 響應狀態碼: {response.status_code}")

            snippets = data.get('items', [])
            all_snippets.extend(snippets)

            logging.info(f"目前獲取到 {len(all_snippets)} 個 snippets")

            if len(all_snippets) >= data.get('totalCount', 0):
                break

            params['offset'] += params['limit']

        logging.info(f"總共獲取到 {len(all_snippets)} 個 snippets")

        return [format_snippet(snippet, base_url) for snippet in all_snippets]
    except requests.exceptions.RequestException as e:
        logging.error(f"獲取snippet資訊失敗: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logging.error(f"錯誤響應: {e.response.text}")
        return []


def format_snippet(snippet, base_url):
    """格式化snippet資訊以符合目標payload結構"""
    formatted = {
        "uri": snippet.get('uri', ''),
        "scanSummary": next(
            (link['href'] for link in snippet.get('_meta', {}).get('links', []) if link['rel'] == 'scan'), ''),
        "compositePath": snippet.get('compositePath', {}),
        "snippetBomComponentRequests": []
    }

    for component in snippet.get('fileSnippetBomComponents', []):
        formatted_component = {
            "sourceStartLines": component.get('sourceStartLines', []),
            "sourceEndLines": component.get('sourceEndLines', []),
            "hashId": component.get('hashId', ''),
            "component": urljoin(base_url, f"/api/components/{component.get('project', {}).get('id', '')}"),
            "componentVersion": urljoin(base_url,
                                        f"/api/components/{component.get('project', {}).get('id', '')}/versions/{component.get('release', {}).get('id', '')}"),
            "origin": urljoin(base_url,
                              f"/api/components/{component.get('project', {}).get('id', '')}/versions/{component.get('release', {}).get('id', '')}/origins/{component.get('channelRelease', {}).get('id', '')}"),
            "reviewStatus": "REVIEWED",
            "ignored": False
        }
        formatted['snippetBomComponentRequests'].append(formatted_component)

    return formatted


def put_snippet_info(session, base_url, headers, project_id, version_id, payload):
    """將snippet資訊PUT到指定URL"""
    url = urljoin(base_url, f"/api/projects/{project_id}/versions/{version_id}/bulk-snippet-bom-entries")
    session = requests.Session()
    headers['Content-Type'] = 'application/vnd.blackducksoftware.bill-of-materials-6+json'

    try:
        logging.info(f"正在PUT snippet資訊到 URL: {url}")
        logging.info(f"Payload: {json.dumps(payload, indent=2)}")

        # response = session.put(url, headers=headers, json=json.dumps(payload), verify=False, timeout=10)
        response = session.put(url, headers=headers, data=json.dumps(payload), verify=False, timeout=10)
        response.raise_for_status()

        logging.info(f"PUT 請求成功，響應狀態碼: {response.status_code}")
        # return response.json()
        return response
    except requests.exceptions.RequestException as e:
        logging.error(f"PUT snippet資訊失敗: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logging.error(f"錯誤響應: {e.response.text}")
        return None


def main(base_url, username, password, project_name, version_name):
    session = login(base_url, username, password)
    headers = get_headers(session)

    logging.info(f"正在獲取專案 '{project_name}' 版本 '{version_name}' 的資訊...")
    project_id, version_id = get_project_version_ids(session, base_url, headers, project_name, version_name)

    if not project_id or not version_id:
        logging.error(f"找不到指定的專案 '{project_name}' 或版本 '{version_name}'。")
        return

    logging.info(f"正在檢查專案 '{project_name}' 版本 '{version_name}' 的snippet資訊...")
    snippets = get_snippet_info(session, base_url, headers, project_id, version_id)

    if snippets:
        logging.info(f"找到 {len(snippets)} 個snippets，正在準備PUT請求...")
        print(f"準備PUT的Payload:")
        print(json.dumps(snippets[:5], indent=2))  # 只打印前5個snippet作為示例
        if len(snippets) > 5:
            print(f"... 還有 {len(snippets) - 5} 個snippets未顯示")

        # confirm = input("確認要更新這些snippet資訊嗎？(y/n): ").lower().strip()
        # if confirm != 'y':
        #     logging.info("操作已取消。")
        #     return

        # 對每個snippet單獨發送PUT請求
        for i, snippet in enumerate(snippets, 1):
            result = put_snippet_info(session, base_url, headers, project_id, version_id, [snippet])
            # if result:
            if result.status_code == 204:
                logging.info(f"成功更新第 {i}/{len(snippets)} 個snippet")
            else:
                logging.error(f"更新第 {i}/{len(snippets)} 個snippet失敗")
                logging.error("由於更新失敗，停止處理剩餘的snippets。")
                return
    else:
        logging.info(f"專案 '{project_name}' 版本 '{version_name}' 沒有找到任何snippet")

    print("所有snippet處理完成。")


if __name__ == "__main__":
    # parser = argparse.ArgumentParser(description="Black Duck API 腳本 - 更新專案版本的snippet資訊")
    # parser.add_argument("base_url", help="Black Duck 伺服器的基礎網址")
    # parser.add_argument("username", help="Black Duck 使用者名稱")
    # parser.add_argument("password", help="Black Duck 密碼")
    # parser.add_argument("project_name", help="專案名稱")
    # parser.add_argument("version_name", help="版本名稱")
    # args = parser.parse_args()
    #
    # main(args.base_url, args.username, args.password, args.project_name, args.version_name)
    base_url = 'https://lobsterapj.app.blackduck.com'
    username = 'sysadmin'
    password = 'SJsj002@'
    project_name = 'proj_simple_snippet_test'
    version_name = '1.0'

    main(base_url, username, password, project_name, version_name)
