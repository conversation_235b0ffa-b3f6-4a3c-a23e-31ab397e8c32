#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
步骤1：获取snippet hashid

https://blackduck.enflame.cn/api/projects/24149344-d916-4389-aecc-3d52c095b92b/versions/deb2e571-f3af-43bc-8573-9e8e56eebe31/matched-files?filter=bomMatchType:SNIPPET
可以获取snippet hashid

"uri": "file:///home/<USER>/tops/eccl/src/comm/bootstrap.cc"
"snippet": "https://blackduck.enflame.cn/api/projects/24149344-d916-4389-aecc-3d52c095b92b/versions/deb2e571-f3af-43bc-8573-9e8e56eebe31/scans/ca549ab9-ec7c-4bda-aa62-fb40bf9040da/nodes/42/snippets/2d3ce0ae9e86f6234c1f71ad897c2252b42094cd66c3aa7ead55ee9538439913"


步骤2：需要根据snippet 的信息得到 versionid   scanid  nodeid
https://blackduck.enflame.cn/api/projects/24149344-d916-4389-aecc-3d52c095b92b/versions/deb2e571-f3af-43bc-8573-9e8e56eebe31/scans/ca549ab9-ec7c-4bda-aa62-fb40bf9040da/nodes/60/snippets/8f0d11115fe47127b4e0eaf8b7772fbfabc78302b6ead565086786aac6ace56f


https://blackduck.enflame.cn/api/internal/releases/deb2e571-f3af-43bc-8573-9e8e56eebe31/scans/ca549ab9-ec7c-4bda-aa62-fb40bf9040da/nodes/60/file-bom-entry

"""
import requests
import json


# 获取bearer token
def get_bearer_token(hubHost, accessToken):
    url = "https://{}/api/tokens/authenticate".format(hubHost)
    payload = {}
    files = {}
    headers = {
        'Authorization': 'token ' + accessToken,
        'Accept': 'application/vnd.blackducksoftware.user-4+json'
    }
    try:
        response = requests.request("POST", url, headers=headers, data=payload, files=files, verify=False)
        if response.status_code != 200:
            print("申请 bearerToken 失败")
        return response.json()['bearerToken']
    except Exception as e:
        print('Reason:', e)


# 请求/api/projects/{projectId}/versions/{projectVersionId}/bulk-snippet-bom-entries
def requests_method(accessToken):
    payload = [{
        "scanSummary": "https://cn58sigkb01/api/scan-summaries/e1cdd7aa-7cb3-457d-a2c3-76a327bdfee7",
        "uri": "file:///home/<USER>/sourcecode/WebGoat/webgoat-container/src/main/resources/static/js/libs/jquery.min.js",
        "compositePath": {
            # "path": "webgoat-container/src/main/resources/static/js/libs/jquery.min.js",
            "path": "webgoat-lessons/auth-bypass/src/main/java/org/owasp/webgoat/auth_bypass/AccountVerificationHelper.java",
            "archiveContext": "",
            # "compositePathContext": "webgoat-container/src/main/resources/static/js/libs/jquery.min.js#",
            "compositePathContext": "webgoat-lessons/auth-bypass/src/main/java/org/owasp/webgoat/auth_bypass/AccountVerificationHelper.java",
            "fileName": "AccountVerificationHelper.java"
        },
        "snippetBomComponentRequests": [{
            "sourceStartLines": [
                1
            ],
            "sourceEndLines": [
                2
            ],
            "hashId": "e365a578740659bfabb76694c6400bdb11a0a8508b6c2ce62d42d0537874f9e9",
            "component": "https://cn58sigkb01/api/components/7a777763-ac31-4919-8f49-52e373f3af41",
            "componentVersion": "https://cn58sigkb01/api/components/7a777763-ac31-4919-8f49-52e373f3af41/versions/634befbb-a6c9-4ae7-87a3-4636c2f78ce3",
            "origin": "https://cn58sigkb01/api/components/7a777763-ac31-4919-8f49-52e373f3af41/versions/634befbb-a6c9-4ae7-87a3-4636c2f78ce3/origins/3b4ceb05-d110-46a5-b325-4ced0d761858",
            "reviewStatus": "REVIEWED",
            "ignored": "false"
        }]
    }]

    headers = {
        "Content-Type": "application/vnd.blackducksoftware.bill-of-materials-6+json",
        'Accept': 'application/vnd.blackducksoftware.bill-of-materials-6+json',
        "Authorization": "Bearer " + accessToken
    }
    # https://blackduck.enflame.cn/api/projects/24149344-d916-4389-aecc-3d52c095b92b/versions/deb2e571-f3af-43bc-8573-9e8e56eebe31/source-trees?filter=bomMatchType%3Asnippet&filter=bomMatchReviewStatus%3Anot_reviewed&offset=0&limit=100
    url = "https://cn58sigkb01/api/projects/3342245a-c6a5-41f3-9c63-2ded4e96c7e4/versions/99d9723a-5366-4a17-9f31-5f264c987c02/bulk-snippet-bom-entries"
    req = requests.put(url, headers=headers, data=json.dumps(payload), verify=False)
    if req.ok:
        print("ok")
    else:
        print("failure!")


if __name__ == '__main__':
    accessToken = "OTNmNTMxNWItMzk0ZC00NjgzLTg4ZDQtZDgyZDdlNjc3YWVjOmM5NGFlYWQ4LTdjZGMtNDNlYy1iOGRiLWJkNzUyNGQ0NmIzYw=="
    hubHost = "cn58sigkb01"
    bearer = get_bearer_token(hubHost, accessToken)
    requests_method(bearer)