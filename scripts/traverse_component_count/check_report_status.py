import json

from blackduck import Client
import logging
from urllib.parse import urljoin

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='ZjBiYjljZWMtM2NmMS00MmNhLTk0Y2YtMmY0MGYyZjE1M2ZjOmVkYmNhY2E5LWNmNDYtNGFiMS05NGYxLWFhNjI3ZjNiMmUzNg==',
    base_url='https://zhex.app.blackduck.com/',
    verify=False  # TLS certificate verification
)

url_str = """
https://zhex.app.blackduck.com/api/projects/09f258fe-de38-4605-aced-eda30c444491/versions/cdeefcd8-3140-4346-866b-a977c4117815/
https://zhex.app.blackduck.com/api/projects/9b41b4cc-0e5a-4101-9b3b-6d1fa24853de/versions/344e43c4-a2e2-4d69-8e32-58206003fef7/
https://zhex.app.blackduck.com/api/projects/3c49c46f-abf2-4906-992f-8bf9f7ee5b8f/versions/001c71db-2664-4664-aab2-2ab13de44179/
https://zhex.app.blackduck.com/api/projects/ec73b065-6d6f-46c7-a495-a532ffa5c5ed/versions/85786ca0-a94e-4779-90ed-c83446c99bb2/
https://zhex.app.blackduck.com/api/projects/f8e29bfd-003f-4b7a-acbd-7c9fc04887e7/versions/e604b399-e705-49ee-92f5-e1509d64b564/
https://zhex.app.blackduck.com/api/projects/a721bc05-6cbc-486b-aa8f-573ec5e1e933/versions/0db93d1c-1d8e-41b8-a5ae-d31f762e1fb6/
https://zhex.app.blackduck.com/api/projects/a721bc05-6cbc-486b-aa8f-573ec5e1e933/versions/c2e83be4-98f4-4c02-bdd5-243511d4be7f/
https://zhex.app.blackduck.com/api/projects/a721bc05-6cbc-486b-aa8f-573ec5e1e933/versions/8c9c6647-a57a-454d-815f-8719a1ee0e35/
https://zhex.app.blackduck.com/api/projects/b0161f2c-02d5-4486-9b1a-ee2e3c710a50/versions/8bda8f77-4bf4-4b9c-b1d6-190fcff2daa3/
https://zhex.app.blackduck.com/api/projects/4f33aaf3-643e-4232-8f1d-0dfd699490b5/versions/3177634e-5e1c-4e8f-b91d-80b1a5c5bb46/
https://zhex.app.blackduck.com/api/projects/17c31606-7486-4670-a432-2f8244f8fac2/versions/a1d99c27-391d-4136-be9c-74f164c98d90/
https://zhex.app.blackduck.com/api/projects/91bdd0b8-77fa-40cd-8039-6d6abbc32393/versions/9a1d76f0-c401-45f2-b93f-734bbdf6d1d3/
https://zhex.app.blackduck.com/api/projects/10b3f86b-7d4a-4003-b5ca-12b349de0a93/versions/59373fba-335c-4246-adcb-e90b20e10fe0/
https://zhex.app.blackduck.com/api/projects/5c888e71-40a1-484b-bd89-b5b92fb94287/versions/59cc0688-ec96-4b8a-ab57-fab512450477/
https://zhex.app.blackduck.com/api/projects/9db110ce-8460-4f9e-891b-3da99f91f78b/versions/dc6e15c5-327b-4ba6-bebc-81085015cb48/
https://zhex.app.blackduck.com/api/projects/0a527a41-e142-4914-b472-445fc49b876a/versions/99fc0420-3718-4a3a-b130-04efbbbce066/
https://zhex.app.blackduck.com/api/projects/0a527a41-e142-4914-b472-445fc49b876a/versions/a5de9cbe-41b1-40a5-bd5d-09f83c337eeb/
https://zhex.app.blackduck.com/api/projects/527b72ea-2b02-4fd5-9eb3-410195da9304/versions/2920c428-06c9-4905-b7d5-a9d51cefc93f/
"""

url_list = url_str.split()

for url in url_list:
    report_url = urljoin(url, 'reports?limit=100&offset=0')
    r = bd.session.get(report_url)
    data = r.json()
    for it in data['items']:
        print(report_url, it['status'], round(float(it.get('fileSize', 0)) / 1024 / 1024, 2), 'MB')

