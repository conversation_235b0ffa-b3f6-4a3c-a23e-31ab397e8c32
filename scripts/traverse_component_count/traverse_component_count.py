import json

from blackduck import Client
import logging
from urllib.parse import urljoin

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='ZjBiYjljZWMtM2NmMS00MmNhLTk0Y2YtMmY0MGYyZjE1M2ZjOmVkYmNhY2E5LWNmNDYtNGFiMS05NGYxLWFhNjI3ZjNiMmUzNg==',
    base_url='https://zhex.app.blackduck.com/',
    verify=False  # TLS certificate verification
)

for project in bd.get_resource('projects'):
    for project_version in bd.get_resource('versions', parent=project):
        # print(project_version)
        url = project_version['_meta']['href'] + '/components?filter=bomInclusion%3Afalse&filter=bomMatchInclusion%3Afalse&filter=bomMatchReviewStatus%3Areviewed&limit=1&offset=0'
        data = bd.get_json(url)
        component_count = data['totalCount']
        if component_count >= 300:
            project_version_id = project_version['_meta']['href'].split('/')[-1]
            payload = {
                "reportFormat": "JSON", "reportType": "VERSION_LICENSE", "categories": ["COPYRIGHT_TEXT"]
            }
            headers = {
                'Content-Type': 'application/json'
            }
            report_url = urljoin(bd.base_url, f'api/versions/{project_version_id}/license-reports')
            r = bd.session.post(report_url, headers=headers, data=json.dumps(payload))
            print(f'Creating notice file for {url}, status: {r.status_code} text: {r.text}')

        # print(f'{url}: component count {data["totalCount"]}')

        # if data['status'] != 'UP_TO_DATE':
        #     print(f'{url} : bom status {data["status"]}')
    # break

# traverse report status
# for project in bd.get_resource('projects'):
#     for project_version in bd.get_resource('versions', parent=project):
#         # print(project_version)
#         url = project_version['_meta']['href'] + '/reports'
#         headers = {
#             'Accept': 'application/vnd.blackducksoftware.report-4+json'
#         }
#         data = bd.get_json(url, headers=headers)
#         for it in data['items']:
#             href = it['_meta']['href']
#             report_id = href.split('/')[-1]
#             if report_id in ['cb9cf60a-9d5d-4bd9-9fcd-e23b645b7f6a', '330d7aa9-7885-4b12-8241-8fb44181ac41']:
#                 print(report_id, href)
