"""
Bulk Component Adjustments
Apply an adjustment to multiple components.

PATCH /api/projects/{projectId}/versions/{projectVersionId}/bulk-adjustment
Accept: application/vnd.blackducksoftware.bill-of-materials-7+json
Content-Type: application/vnd.blackducksoftware.bill-of-materials-7+json
"""

import json
from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='NTU5NTBmNTUtNjdkYi00ZDE4LTliMTAtNzBkMjBjZWQ5MjRjOmM3NmE1YWM0LTFhZDktNDBlZC1hYjE5LWIxNGE4MDU3Mzc0NA==',
    base_url='https://lobsterapj.app.blackduck.com/',
    verify=False  # TLS certificate verification
)

"""
payload:
{
  "components" : [ "/api/projects/{projectId}/versions}/{projectVersionId}/components/{componentId}", "/api/projects/{projectId}/versions}/{projectVersionId}/components/{componentId}/versions/{componentVersionId}" ],
  "reviewStatus" : "REVIEWED",
  "ignored" : true,
  "usage" : "DYNAMICALLY_LINKED",
  "inAttributionReport" : true
}
"""

if __name__ == '__main__':
    project_id = '6a17aeaa-738d-41b8-9a89-2fa746013ae6'
    project_version_id = '4579d95d-d563-40ff-b6f1-190607ab9575'
    component_id = '3a90566d-9eee-4c31-a0d9-1b815f509bdf'

    # project_id = 'fb9a4828-408b-47d9-bcaf-76ac7f542090'
    # project_version_id = '668b04b9-dc96-4146-9562-3dcc6bd42a19'
    # component_id = 'd5b90213-52f8-4429-b3c7-ed582af929d2'



    payload = {
        "components": [
            f'/api/projects/{project_id}/versions/{project_version_id}/components/{component_id}',
        ],
        "reviewStatus": "REVIEWED",
        "ignored": True,
        "usage": "DYNAMICALLY_LINKED",
        "inAttributionReport": True
    }

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-7+json'
    }

    r = bd.session.patch(
        f'/api/projects/{project_id}/versions/{project_version_id}/bulk-adjustment',
        json=payload,
        headers=headers
    )
    print(r.status_code)
    print(r.text)
