"""
https://regedit.blackducksoftware.com/api
"""
from datetime import datetime

import requests


def get_auth_token(api_key):
    url = 'https://regedit.blackducksoftware.com/api/authenticate'
    headers = {
        'Authorization': 'apiKey ' + api_key,
        'Accept': 'application/json'
    }
    r = requests.post(url, headers=headers)
    if r.status_code == 200:
        return r.json()['jsonWebToken']
    else:
        raise Exception(r.json()['message'])


def get_license_status(auth_token, key_id):
    url = f'https://regedit.blackducksoftware.com/api/key/{key_id}'
    headers = {
        'Authorization': 'Bearer ' + auth_token,
        'Accept': 'application/json'
    }
    r = requests.get(url, headers=headers)
    if r.status_code == 200:
        return r.json()
    else:
        raise Exception(r.json()['message'])


if __name__ == '__main__':
    api_key = 'YzNiYjY1NjctZjkwZi00MGQ0LWE1MjgtZDhjNjdjMWI1NDFlOjMwYzgzYzI2OTMyNTRmN2IzMTEwYTQ4NTk5NzY0NjBi'
    # key_id_list = ['ZTE_hub_1_0018000000iCFXw', 'ZTE_hub_2_0018000000iCFXw', 'ZTE_hub_3_0018000000iCFXw', 'ZTE_hub_4_0018000000iCFXw', 'ZTE_hub_5_0018000000iCFXw', 'ZTE_hub_6_0018000000iCFXw', 'ZTE_hub_7_0018000000iCFXw', 'ZTE_hub_7_0018000000iCFXw', 'ZTE_hub_8_0018000000iCFXw', 'ZTE_hub_9_0018000000iCFXw', 'ZTE_hub_10_0018000000iCFXw', 'ZTE_hub_11_0018000000iCFXw', 'ZTE_hub_12_0018000000iCFXw', 'ZTE_hub_13_0018000000iCFXw', 'ZTE_hub_14_0018000000iCFXw',]
    key_id_list = [f'BeijingByteDance_KBOnprem_Prod_hub_{i+1}_0013400001Xnvos' for i in range(30)]
    auth_token = get_auth_token(api_key)
    for key_id in key_id_list:
        data = get_license_status(auth_token, key_id)
        registration_id = data['registrationId']
        system_id = data['systemId']
        for p in data['properties']:
            if p.get('fieldLabel') == 'Token':
                token = p.get('value')
                break
            else:
                token = None
        for p in data['properties']:
            if p.get('fieldLabel') == 'License Usage Window':
                license_usage_window = p.get('value')
                break
            else:
                activate_window = None
        for p in data['properties']:
            if p.get('fieldLabel') == 'License Start Date':
                license_start_date = datetime.strptime(p.get('value'), '%Y%m%dT%H%M%SZ')
                break
            else:
                license_start_date = None
        for p in data['properties']:
            if p.get('fieldLabel') == 'License Expiration Date':
                license_expiration_date = datetime.strptime(p.get('value'), '%Y%m%dT%H%M%SZ')
                break
            else:
                license_expiration_date = None
        for p in data['properties']:
            if p.get('fieldLabel') == 'Code Location Bytes Limit':
                scan_size_limit = int(p.get('value')) / 1024.0 / 1024 / 1024
                break
            else:
                scan_size_limit = None
        for p in data['properties']:
            if p.get('fieldLabel') == 'User Limit':
                user_limit = p.get('value')
                break
            else:
                user_limit = None
        print(f'registrationId: {data["registrationId"]}, expirationDate: {license_expiration_date}, '
              f'startDate: {license_start_date}, '
              f'scanSizeLimit: {scan_size_limit} GB, '
              f'userLimit: {user_limit}, '
              f'systemId: {data["systemId"]}, token: {token}, '
              f'license_usage_window: {license_usage_window}')
