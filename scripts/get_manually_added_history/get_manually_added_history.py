import json
import re
from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='OWRmOTcwYjYtZGM4Ny00N2JkLTkwYjUtMmM0ZmUzNDk4MTRjOmFiZmZmYmMwLWZhYzYtNGQxZC1hY2ZiLTFlZDI5YmM4YjZhOQ==',
    base_url='https://cn58sigkb01/',
    verify=False  # TLS certificate verification
)

component_version_id_list = [
    '7d8079a2-4379-42b1-b7ed-5db99f378bf6',
    'b46bf724-c574-4851-a9d3-3ad9d43a6c28',
    '4925ddc6-c62c-4954-81e3-18544bc63df7'
]


def get_manually_added_history(project_id, project_version_id, component_version_id_list):
    url = f'{bd.base_url}/api/journal/projects/{project_id}/versions/{project_version_id}?filter=journalAction:component_added&limit=9999&offset=0&sort=timestamp DESC'
    data = bd.get_json(url)
    for it in data['items']:
        pass

if __name__ == '__main__':
    pass


