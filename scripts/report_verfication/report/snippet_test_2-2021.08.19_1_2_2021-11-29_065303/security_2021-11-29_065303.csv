Used by,Component id,Version id,Origin id,Component name,Component version name,Component origin name,Component origin id,Component origin version name,Vulnerability id,Description,Published on,Updated on,Base score,Exploitability,Impact,Vulnerability source,Remediation status,Remediation target date,Remediation actual date,Remediation comment,URL,Security Risk,Project path,Overall score,CWE Ids,Solution available,Workaround available,Exploit available,CVSS Version,Match type,Reachable
,15e2f588-ef74-4461-8854-fd49fa16f792,e1561c74-8a91-416a-98d9-91fb89000d34,611fa842-6aa6-472d-a86a-6889d90bd3e8,Acegi Security System for Spring,1.0.6,maven,org.acegisecurity:acegi-security:1.0.6,1.0.6,CVE-2010-3700,"VMware SpringSource Spring Security 2.x before 2.0.6 and 3.x before 3.0.4, and Acegi Security 1.0.0 through 1.0.7, as used in IBM WebSphere Application Server (WAS) 6.1 and 7.0, allows remote attackers to bypass security constraints via a path parameter.",10/30/10,10/11/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-3700,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,b75f622a-30da-46e4-a9c9-56f4ab75e22e,074abcb0-ecc7-4905-9fe8-6a4c5bd92ad5,a8254896-a7a5-4495-a6eb-a8977d67639d,Apache Commons BeanUtils,1.7.0,maven,commons-beanutils:commons-beanutils:1.7.0,1.7.0,CVE-2014-0114,"Apache Commons BeanUtils, as distributed in lib/commons-beanutils-1.8.0.jar in Apache Struts 1.x through 1.3.10 and in other products requiring commons-beanutils through 1.9.2, does not suppress the class property, which allows remote attackers to ""manipulate"" the ClassLoader and execute arbitrary code via the class parameter, as demonstrated by the passing of this parameter to the getClass method of the ActionForm object in Struts 1.",4/30/14,1/27/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0114,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-20],false,false,false,CVSS 2.0,Exact,false
,b75f622a-30da-46e4-a9c9-56f4ab75e22e,074abcb0-ecc7-4905-9fe8-6a4c5bd92ad5,a8254896-a7a5-4495-a6eb-a8977d67639d,Apache Commons BeanUtils,1.7.0,maven,commons-beanutils:commons-beanutils:1.7.0,1.7.0,CVE-2019-10086,"In Apache Commons Beanutils 1.9.2, a special BeanIntrospector class was added which allows suppressing the ability for an attacker to access the classloader via the class property available on all Java objects. We, however were not using this by default characteristic of the PropertyUtilsBean.",8/21/19,1/29/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2019-10086,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-502],false,false,false,CVSS 2.0,Exact,false
,87ac8642-f639-46fe-ab2b-66e89cca0130,a332c228-85e0-4134-8e4a-7ee111477c9a,03280f1e-c004-40f6-8956-0b3295d1d6b6,Apache Commons Collections,3.0,maven,commons-collections:commons-collections:3.0,3.0,CVE-2015-6420,"Serialized-object interfaces in certain Cisco Collaboration and Social Media; Endpoint Clients and Client Software; Network Application, Service, and Acceleration; Network and Content Security Devices; Network Management and Provisioning; Routing and Switching - Enterprise and Service Provider; Unified Computing; Voice and Unified Communications Devices; Video, Streaming, TelePresence, and Transcoding Devices; Wireless; and Cisco Hosted Services products allow remote attackers to execute arbitrary commands via a crafted serialized Java object, related to the Apache Commons Collections (ACC) library.",12/15/15,3/11/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-6420,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-502],false,false,false,CVSS 2.0,Exact,false
,87ac8642-f639-46fe-ab2b-66e89cca0130,a332c228-85e0-4134-8e4a-7ee111477c9a,03280f1e-c004-40f6-8956-0b3295d1d6b6,Apache Commons Collections,3.0,maven,commons-collections:commons-collections:3.0,3.0,CVE-2017-15708,"In Apache Synapse, by default no authentication is required for Java Remote Method Invocation (RMI). So Apache Synapse 3.0.1 or all previous releases (3.0.0, 2.1.0, 2.0.0, 1.2, 1.1.2, 1.1.1) allows remote code execution attacks that can be performed by injecting specially crafted serialized objects. And the presence of Apache Commons Collections 3.2.1 (commons-collections-3.2.1.jar) or previous versions in Synapse distribution makes this exploitable. To mitigate the issue, we need to limit RMI access to trusted users only. Further upgrading to 3.0.1 version will eliminate the risk of having said Commons Collection version. In Synapse 3.0.1, Commons Collection has been updated to 3.2.2 version.",12/11/17,4/2/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2017-15708,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-74],false,false,false,CVSS 2.0,Exact,false
,18dbecb7-a3b5-418b-9af1-44bf61ae0319,9cfc4dad-1394-4bbc-902a-a53516ed3550,9a0a260a-314c-4a41-9386-17c1da62b474,Apache Commons FileUpload,1.0,maven,commons-fileupload:commons-fileupload:1.0,1.0,CVE-2013-0248,"The default configuration of javax.servlet.context.tempdir in Apache Commons FileUpload 1.0 through 1.2.2 uses the /tmp directory for uploaded files, which allows local users to overwrite arbitrary files via an unspecified symlink attack.",3/16/13,10/20/17,3.3,3.4,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-0248,LOW,snippet_test_2 2021.08.19_1_2,3.3,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,18dbecb7-a3b5-418b-9af1-44bf61ae0319,9cfc4dad-1394-4bbc-902a-a53516ed3550,9a0a260a-314c-4a41-9386-17c1da62b474,Apache Commons FileUpload,1.0,maven,commons-fileupload:commons-fileupload:1.0,1.0,CVE-2013-2186,"The DiskFileItem class in Apache Commons FileUpload, as used in Red Hat JBoss BRMS 5.3.1; JBoss Portal 4.3 CP07, 5.2.2, and 6.0.0; and Red Hat JBoss Web Server 1.0.2 allows remote attackers to write to arbitrary files via a NULL byte in a file name in a serialized instance.",10/29/13,1/9/18,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-2186,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-20],false,false,false,CVSS 2.0,Exact,false
,18dbecb7-a3b5-418b-9af1-44bf61ae0319,9cfc4dad-1394-4bbc-902a-a53516ed3550,9a0a260a-314c-4a41-9386-17c1da62b474,Apache Commons FileUpload,1.0,maven,commons-fileupload:commons-fileupload:1.0,1.0,CVE-2014-0050,"MultipartStream.java in Apache Commons FileUpload before 1.3.1, as used in Apache Tomcat, JBoss Web, and other products, allows remote attackers to cause a denial of service (infinite loop and CPU consumption) via a crafted Content-Type header that bypasses a loop's intended exit conditions.",4/1/14,10/10/18,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0050,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,18dbecb7-a3b5-418b-9af1-44bf61ae0319,9cfc4dad-1394-4bbc-902a-a53516ed3550,9a0a260a-314c-4a41-9386-17c1da62b474,Apache Commons FileUpload,1.0,maven,commons-fileupload:commons-fileupload:1.0,1.0,CVE-2016-3092,"The MultipartStream class in Apache Commons Fileupload before 1.3.2, as used in Apache Tomcat 7.x before 7.0.70, 8.x before 8.0.36, 8.5.x before 8.5.3, and 9.x before 9.0.0.M7 and other products, allows remote attackers to cause a denial of service (CPU consumption) via a long boundary string.",7/5/16,4/24/19,7.8,10.0,6.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-3092,HIGH,snippet_test_2 2021.08.19_1_2,7.8,[CWE-20],false,false,false,CVSS 2.0,Exact,false
,18dbecb7-a3b5-418b-9af1-44bf61ae0319,9cfc4dad-1394-4bbc-902a-a53516ed3550,9a0a260a-314c-4a41-9386-17c1da62b474,Apache Commons FileUpload,1.0,maven,commons-fileupload:commons-fileupload:1.0,1.0,CVE-2016-1000031,Apache Commons FileUpload before 1.3.3 DiskFileItem File Manipulation Remote Code Execution,10/25/16,1/20/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-1000031,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-284],false,false,false,CVSS 2.0,Exact,false
,08b461c4-5c21-4839-a503-1ae6e43d1585,d38ab1f5-3791-4671-9b95-f52a315f46c1,f392a515-e206-4e07-a924-281ca3915d0b,Apache log4j,1.2.13,maven,log4j:log4j:1.2.13,1.2.13,CVE-2019-17571,Included in Log4j 1.2 is a SocketServer class that is vulnerable to deserialization of untrusted data which can be exploited to remotely execute arbitrary code when combined with a deserialization gadget when listening to untrusted network traffic for log data. This affects Log4j versions up to 1.2 up to 1.2.17.,12/21/19,5/1/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2019-17571,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-502],false,false,false,CVSS 2.0,Exact,false
,08b461c4-5c21-4839-a503-1ae6e43d1585,d38ab1f5-3791-4671-9b95-f52a315f46c1,f392a515-e206-4e07-a924-281ca3915d0b,Apache log4j,1.2.13,maven,log4j:log4j:1.2.13,1.2.13,CVE-2020-9488,Improper validation of certificate with host mismatch in Apache Log4j SMTP appender. This could allow an SMTPS connection to be intercepted by a man-in-the-middle attack which could leak any log messages sent through that appender.,4/28/20,3/16/21,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-9488,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-295],false,false,false,CVSS 2.0,Exact,false
,bb9a56d3-8a48-43fd-8db1-5a7529b857f0,05219967-6b43-4e84-9708-533d8fe46295,2b031136-d8b6-42e2-bc6f-910b3db04c0d,Apache Struts,1.2.9,maven,struts:struts:1.2.9,1.2.9,CVE-2014-0114,"Apache Commons BeanUtils, as distributed in lib/commons-beanutils-1.8.0.jar in Apache Struts 1.x through 1.3.10 and in other products requiring commons-beanutils through 1.9.2, does not suppress the class property, which allows remote attackers to ""manipulate"" the ClassLoader and execute arbitrary code via the class parameter, as demonstrated by the passing of this parameter to the getClass method of the ActionForm object in Struts 1.",4/30/14,1/27/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0114,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-20],false,false,false,CVSS 2.0,Exact,false
,bb9a56d3-8a48-43fd-8db1-5a7529b857f0,05219967-6b43-4e84-9708-533d8fe46295,2b031136-d8b6-42e2-bc6f-910b3db04c0d,Apache Struts,1.2.9,maven,struts:struts:1.2.9,1.2.9,CVE-2015-0899,The MultiPageValidator implementation in Apache Struts 1 1.1 through 1.3.10 allows remote attackers to bypass intended access restrictions via a modified page parameter.,7/5/16,7/1/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0899,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-20],false,false,false,CVSS 2.0,Exact,false
,bb9a56d3-8a48-43fd-8db1-5a7529b857f0,05219967-6b43-4e84-9708-533d8fe46295,2b031136-d8b6-42e2-bc6f-910b3db04c0d,Apache Struts,1.2.9,maven,struts:struts:1.2.9,1.2.9,CVE-2016-1181,"ActionServlet.java in Apache Struts 1 1.x through 1.3.10 mishandles multithreaded access to an ActionForm instance, which allows remote attackers to execute arbitrary code or cause a denial of service (unexpected memory access) via a multipart request, a related issue to CVE-2015-0899.",7/5/16,7/15/20,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-1181,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[],false,false,false,CVSS 2.0,Exact,false
,bb9a56d3-8a48-43fd-8db1-5a7529b857f0,05219967-6b43-4e84-9708-533d8fe46295,2b031136-d8b6-42e2-bc6f-910b3db04c0d,Apache Struts,1.2.9,maven,struts:struts:1.2.9,1.2.9,CVE-2016-1182,"ActionServlet.java in Apache Struts 1 1.x through 1.3.10 does not properly restrict the Validator configuration, which allows remote attackers to conduct cross-site scripting (XSS) attacks or cause a denial of service via crafted input, a related issue to CVE-2015-0899.",7/5/16,7/15/20,6.4,10.0,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-1182,MEDIUM,snippet_test_2 2021.08.19_1_2,6.4,[CWE-20],false,false,false,CVSS 2.0,Exact,false
,c4843275-b7af-40bb-808a-6df7bd48cbab,2e7cbfdb-bfa2-4e3a-ae17-f5da50be47d4,2ef04492-5a94-4adc-99f6-18ea3b0182ef,Apache Taglibs,1.1.2,maven,taglibs:standard:1.1.2,1.1.2,CVE-2015-0254,Apache Standard Taglibs before 1.2.3 allows remote attackers to execute arbitrary code or conduct external XML entity (XXE) attacks via a crafted XSLT extension in a (1)  or (2)  JSTL XML tag.,3/9/15,3/25/19,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0254,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[],false,false,false,CVSS 2.0,Exact,false
,243791f0-8298-4f1d-97d0-da3e2c3a6c5b,9053933d-fbbc-4fc7-8f27-45581fb8b79f,8035afbc-2b0f-4e47-9b2b-ab65b7888ffb,Apache Xalan (Java),2.5.1,maven,xalan:xalan:2.5.1,2.5.1,CVE-2014-0107,"The TransformerFactory in Apache Xalan-Java before 2.7.2 does not properly restrict access to certain properties when FEATURE_SECURE_PROCESSING is enabled, which allows remote attackers to bypass expected restrictions and load arbitrary classes or access external resources via a crafted (1) xalan:content-header, (2) xalan:entities, (3) xslt:content-header, or (4) xslt:entities property, or a Java property that is bound to the XSLT 1.0 system-property function.",4/16/14,4/24/19,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0107,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,50bca935-34bc-4929-b6f0-036189498786,b9c0ceb2-41bd-47e4-bf89-8c9bfe0a57ad,0961686f-4834-4bff-a312-2244751af581,dom4j: flexible XML framework for Java,1.6.1,maven,dom4j:dom4j:1.6.1,1.6.1,CVE-2018-1000632,"dom4j version prior to version 2.1.1 contains a CWE-91: XML Injection vulnerability in Class: Element. Methods: addElement, addAttribute that can result in an attacker tampering with XML documents through XML injection. This attack appear to be exploitable via an attacker specifying attributes or elements in the XML document. This vulnerability appears to have been fixed in 2.1.1 or later.",8/21/18,7/23/20,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1000632,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-91],false,false,false,CVSS 2.0,Exact,false
,50bca935-34bc-4929-b6f0-036189498786,b9c0ceb2-41bd-47e4-bf89-8c9bfe0a57ad,0961686f-4834-4bff-a312-2244751af581,dom4j: flexible XML framework for Java,1.6.1,maven,dom4j:dom4j:1.6.1,1.6.1,CVE-2020-10683,"dom4j before 2.0.3 and 2.1.x before 2.1.3 allows external DTDs and External Entities by default, which might enable XXE attacks. However, there is popular external documentation from OWASP showing how to enable the safe, non-default behavior in any application that uses dom4j.",5/2/20,1/20/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-10683,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-611],false,false,false,CVSS 2.0,Exact,false
,761bf1a2-49a5-48d4-b319-08d7cce5fec8,fecf0bb8-faa5-48df-940d-ab62cff5230c,1bcb4676-fa69-424a-a535-e144b4201e5b,Elasticsearch,v7.9.2,github,elastic/elasticsearch:v7.9.2,v7.9.2,CVE-2021-22132,Elasticsearch versions 7.7.0 to 7.10.1 contain an information disclosure flaw in the async search API. Users who execute an async search will improperly store the HTTP headers. An Elasticsearch user with the ability to read the .tasks index could obtain sensitive request headers of other users in the cluster. This issue is fixed in Elasticsearch 7.10.2,1/15/21,2/23/21,2.1,3.9,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2021-22132,LOW,snippet_test_2 2021.08.19_1_2,2.1,[CWE-522],false,false,false,CVSS 2.0,Manually Identified,false
,761bf1a2-49a5-48d4-b319-08d7cce5fec8,fecf0bb8-faa5-48df-940d-ab62cff5230c,1bcb4676-fa69-424a-a535-e144b4201e5b,Elasticsearch,v7.9.2,github,elastic/elasticsearch:v7.9.2,v7.9.2,CVE-2020-7021,Elasticsearch versions before 7.10.0 and 6.8.14 have an information disclosure issue when audit logging and the emit_request_body option is enabled. The Elasticsearch audit log could contain sensitive information such as password hashes or authentication tokens. This could allow an Elasticsearch administrator to view these details.,2/11/21,3/26/21,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-7021,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[CWE-532],false,false,false,CVSS 2.0,Manually Identified,false
,761bf1a2-49a5-48d4-b319-08d7cce5fec8,fecf0bb8-faa5-48df-940d-ab62cff5230c,1bcb4676-fa69-424a-a535-e144b4201e5b,Elasticsearch,v7.9.2,github,elastic/elasticsearch:v7.9.2,v7.9.2,CVE-2021-22134,A document disclosure flaw was found in Elasticsearch versions after 7.6.0 and before 7.11.0 when Document or Field Level Security is used. Get requests do not properly apply security permissions when executing a query against a recently updated document. This affects documents that have been updated and not yet refreshed in the index. This could result in the search disclosing the existence of documents and fields the attacker should not be able to view.,3/9/21,5/5/21,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2021-22134,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[CWE-200],false,false,false,CVSS 2.0,Manually Identified,false
,96eb75c4-7601-4394-8cc9-a005c5857f26,969e3f9d-3305-42a3-af68-78a3ddf547ba,80330f7e-32ae-4e0b-8513-eb745b60d653,JAMon API,1.0,maven,com.jamonapi:jamon:1.0,1.0,CVE-2013-6235,"Multiple cross-site scripting (XSS) vulnerabilities in JAMon (Java Application Monitor) 2.7 and earlier allow remote attackers to inject arbitrary web script or HTML via the (1) listenertype or (2) currentlistener parameter to mondetail.jsp or ArraySQL parameter to (3) mondetail.jsp, (4) jamonadmin.jsp, (5) sql.jsp, or (6) exceptions.jsp.",1/31/14,10/10/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-6235,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Exact,false
,7a777763-ac31-4919-8f49-52e373f3af41,43a5c082-e0d7-42c2-b2a3-61aa8412139c,049c45c0-ddeb-441b-a988-2aba1789f099,jQuery,1.3b2,bower,jquery-fork-11/1.3b2,1.3b2,CVE-2011-4969,"Cross-site scripting (XSS) vulnerability in jQuery before 1.6.3, when using location.hash to select elements, allows remote attackers to inject arbitrary web script or HTML via a crafted tag.",3/9/13,4/17/19,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-4969,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Manually Identified,false
,7a777763-ac31-4919-8f49-52e373f3af41,43a5c082-e0d7-42c2-b2a3-61aa8412139c,049c45c0-ddeb-441b-a988-2aba1789f099,jQuery,1.3b2,bower,jquery-fork-11/1.3b2,1.3b2,CVE-2012-6708,"jQuery before 1.9.0 is vulnerable to Cross-site Scripting (XSS) attacks. The jQuery(strInput) function does not differentiate selectors from HTML in a reliable fashion. In vulnerable versions, jQuery determined whether the input was HTML by looking for the '<' character anywhere in the string, giving attackers more flexibility when attempting to construct a malicious payload. In fixed versions, jQuery only deems the input to be HTML if it explicitly starts with the '<' character, limiting exploitability only to attackers who can control the beginning of a string, which is far less common.",1/19/18,3/26/21,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2012-6708,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Manually Identified,false
,7a777763-ac31-4919-8f49-52e373f3af41,43a5c082-e0d7-42c2-b2a3-61aa8412139c,049c45c0-ddeb-441b-a988-2aba1789f099,jQuery,1.3b2,bower,jquery-fork-11/1.3b2,1.3b2,CVE-2015-9251,"jQuery before 3.0.0 is vulnerable to Cross-site Scripting (XSS) attacks when a cross-domain Ajax request is performed without the dataType option, causing text/javascript responses to be executed.",1/19/18,1/8/21,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-9251,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Manually Identified,false
,7a777763-ac31-4919-8f49-52e373f3af41,43a5c082-e0d7-42c2-b2a3-61aa8412139c,049c45c0-ddeb-441b-a988-2aba1789f099,jQuery,1.3b2,bower,jquery-fork-11/1.3b2,1.3b2,CVE-2019-11358,"jQuery before 3.4.0, as used in Drupal, Backdrop CMS, and other products, mishandles jQuery.extend(true, {}, ...) because of Object.prototype pollution. If an unsanitized source object contained an enumerable __proto__ property, it could extend the native Object.prototype.",4/20/19,3/16/21,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2019-11358,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Manually Identified,false
,7a777763-ac31-4919-8f49-52e373f3af41,43a5c082-e0d7-42c2-b2a3-61aa8412139c,049c45c0-ddeb-441b-a988-2aba1789f099,jQuery,1.3b2,bower,jquery-fork-11/1.3b2,1.3b2,CVE-2020-11023,"In jQuery versions greater than or equal to 1.0.3 and before 3.5.0, passing HTML containing  elements from untrusted sources - even after sanitizing it - to one of jQuery's DOM manipulation methods (i.e. .html(), .append(), and others) may execute untrusted code. This problem is patched in jQuery 3.5.0.",4/30/20,5/5/21,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-11023,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Manually Identified,false
,7a777763-ac31-4919-8f49-52e373f3af41,43a5c082-e0d7-42c2-b2a3-61aa8412139c,049c45c0-ddeb-441b-a988-2aba1789f099,jQuery,1.3b2,bower,jquery-fork-11/1.3b2,1.3b2,CVE-2020-11022,"In jQuery versions greater than or equal to 1.2 and before 3.5.0, passing HTML from untrusted sources - even after sanitizing it - to one of jQuery's DOM manipulation methods (i.e. .html(), .append(), and others) may execute untrusted code. This problem is patched in jQuery 3.5.0.",4/30/20,5/5/21,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-11022,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Manually Identified,false
,7a777763-ac31-4919-8f49-52e373f3af41,43a5c082-e0d7-42c2-b2a3-61aa8412139c,049c45c0-ddeb-441b-a988-2aba1789f099,jQuery,1.3b2,bower,jquery-fork-11/1.3b2,1.3b2,CVE-2020-7656,"jquery prior to 1.9.0 allows Cross-site Scripting attacks via the load method. The load method fails to recognize and remove """" HTML tags that contain a whitespace character, i.e: """", which results in the enclosed script logic to be executed.",5/20/20,5/28/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-7656,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-79],false,false,false,CVSS 2.0,Manually Identified,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2017-3523,"Vulnerability in the MySQL Connectors component of Oracle MySQL (subcomponent: Connector/J). Supported versions that are affected are 5.1.40 and earlier. Difficult to exploit vulnerability allows low privileged attacker with network access via multiple protocols to compromise MySQL Connectors. While the vulnerability is in MySQL Connectors, attacks may significantly impact additional products. Successful attacks of this vulnerability can result in takeover of MySQL Connectors. CVSS 3.0 Base Score 8.5 (Confidentiality, Integrity and Availability impacts). CVSS Vector: (CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:C/C:H/I:H/A:H).",4/25/17,10/3/19,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2017-3523,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2017-3586,"Vulnerability in the MySQL Connectors component of Oracle MySQL (subcomponent: Connector/J). Supported versions that are affected are 5.1.41 and earlier. Easily ""exploitable"" vulnerability allows low privileged attacker with network access via multiple protocols to compromise MySQL Connectors. While the vulnerability is in MySQL Connectors, attacks may significantly impact additional products. Successful attacks of this vulnerability can result in unauthorized update, insert or delete access to some of MySQL Connectors accessible data as well as unauthorized read access to a subset of MySQL Connectors accessible data. CVSS 3.0 Base Score 6.4 (Confidentiality and Integrity impacts). CVSS Vector: (CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:L/A:N).",4/25/17,10/3/19,5.5,8.0,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2017-3586,MEDIUM,snippet_test_2 2021.08.19_1_2,5.5,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2017-3589,"Vulnerability in the MySQL Connectors component of Oracle MySQL (subcomponent: Connector/J). Supported versions that are affected are 5.1.41 and earlier. Easily ""exploitable"" vulnerability allows low privileged attacker with logon to the infrastructure where MySQL Connectors executes to compromise MySQL Connectors. Successful attacks of this vulnerability can result in unauthorized update, insert or delete access to some of MySQL Connectors accessible data. CVSS 3.0 Base Score 3.3 (Integrity impacts). CVSS Vector: (CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:N).",4/25/17,10/3/19,2.1,3.9,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2017-3589,LOW,snippet_test_2 2021.08.19_1_2,2.1,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2018-3258,"Vulnerability in the MySQL Connectors component of Oracle MySQL (subcomponent: Connector/J). Supported versions that are affected are 8.0.12 and prior. Easily exploitable vulnerability allows low privileged attacker with network access via multiple protocols to compromise MySQL Connectors. Successful attacks of this vulnerability can result in takeover of MySQL Connectors. CVSS 3.0 Base Score 8.8 (Confidentiality, Integrity and Availability impacts). CVSS Vector: (CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H).",10/17/18,10/3/19,6.5,8.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-3258,MEDIUM,snippet_test_2 2021.08.19_1_2,6.5,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2019-2692,"Vulnerability in the MySQL Connectors component of Oracle MySQL (subcomponent: Connector/J). Supported versions that are affected are 8.0.15 and prior. Difficult to exploit vulnerability allows high privileged attacker with logon to the infrastructure where MySQL Connectors executes to compromise MySQL Connectors. Successful attacks require human interaction from a person other than the attacker. Successful attacks of this vulnerability can result in takeover of MySQL Connectors. CVSS 3.0 Base Score 6.3 (Confidentiality, Integrity and Availability impacts). CVSS Vector: (CVSS:3.0/AV:L/AC:H/PR:H/UI:R/S:U/C:H/I:H/A:H).",4/24/19,8/25/20,3.5,1.5,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2019-2692,LOW,snippet_test_2 2021.08.19_1_2,3.5,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2020-2875,"Vulnerability in the MySQL Connectors product of Oracle MySQL (component: Connector/J). Supported versions that are affected are 8.0.14 and prior and 5.1.48 and prior. Difficult to exploit vulnerability allows unauthenticated attacker with network access via multiple protocols to compromise MySQL Connectors. Successful attacks require human interaction from a person other than the attacker and while the vulnerability is in MySQL Connectors, attacks may significantly impact additional products. Successful attacks of this vulnerability can result in unauthorized update, insert or delete access to some of MySQL Connectors accessible data as well as unauthorized read access to a subset of MySQL Connectors accessible data. CVSS 3.0 Base Score 4.7 (Confidentiality and Integrity impacts). CVSS Vector: (CVSS:3.0/AV:N/AC:H/PR:N/UI:R/S:C/C:L/I:L/A:N).",4/15/20,9/26/20,4.0,4.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-2875,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2020-2933,Vulnerability in the MySQL Connectors product of Oracle MySQL (component: Connector/J). Supported versions that are affected are 5.1.48 and prior. Difficult to exploit vulnerability allows high privileged attacker with network access via multiple protocols to compromise MySQL Connectors. Successful attacks of this vulnerability can result in unauthorized ability to cause a partial denial of service (partial DOS) of MySQL Connectors. CVSS 3.0 Base Score 2.2 (Availability impacts). CVSS Vector: (CVSS:3.0/AV:N/AC:H/PR:H/UI:N/S:U/C:N/I:N/A:L).,4/15/20,9/26/20,3.5,6.8,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-2933,LOW,snippet_test_2 2021.08.19_1_2,3.5,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2020-2934,"Vulnerability in the MySQL Connectors product of Oracle MySQL (component: Connector/J). Supported versions that are affected are 8.0.19 and prior and 5.1.48 and prior. Difficult to exploit vulnerability allows unauthenticated attacker with network access via multiple protocols to compromise MySQL Connectors. Successful attacks require human interaction from a person other than the attacker. Successful attacks of this vulnerability can result in unauthorized update, insert or delete access to some of MySQL Connectors accessible data as well as unauthorized read access to a subset of MySQL Connectors accessible data and unauthorized ability to cause a partial denial of service (partial DOS) of MySQL Connectors. CVSS 3.0 Base Score 5.0 (Confidentiality, Integrity and Availability impacts). CVSS Vector: (CVSS:3.0/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L).",4/15/20,9/26/20,5.1,4.9,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-2934,MEDIUM,snippet_test_2 2021.08.19_1_2,5.1,[],false,false,false,CVSS 2.0,Exact,false
,a8327ed9-1cc7-409a-9135-d222514a13c3,af23eeff-d197-46a4-acc5-d90f278a24cb,072284cc-5136-4c52-afe1-3a926bd2d579,MySQL Connector/J,5.1.5,maven,mysql:mysql-connector-java:5.1.5,5.1.5,CVE-2020-1967,"Server or client applications that call the SSL_check_chain() function during or after a TLS 1.3 handshake may crash due to a NULL pointer dereference as a result of incorrect handling of the ""signature_algorithms_cert"" TLS extension. The crash occurs if an invalid or unrecognised signature algorithm is received from the peer. This could be exploited by a malicious peer in a Denial of Service attack. OpenSSL version 1.1.1d, 1.1.1e, and 1.1.1f are affected by this issue. This issue did not affect OpenSSL versions prior to 1.1.1d. Fixed in OpenSSL 1.1.1g (Affected 1.1.1d-1.1.1f).",4/21/20,2/9/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-1967,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-476],false,false,false,CVSS 2.0,Exact,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-1999-0428,OpenSSL and SSLeay allow remote attackers to reuse SSL sessions and bypass access controls.,3/22/99,10/14/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-1999-0428,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-384],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2006-4339,"OpenSSL before 0.9.7, 0.9.7 before 0.9.7k, and 0.9.8 before 0.9.8c, when using an RSA key with exponent 3, removes PKCS-1 padding before generating a hash, which allows remote attackers to forge a PKCS #1 v1.5 signature that is signed by that RSA key and prevents OpenSSL from correctly verifying X.509 and other certificates that use PKCS #1.",9/6/06,10/18/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2006-4339,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2007-3108,"The BN_from_montgomery function in crypto/bn/bn_mont.c in OpenSSL 0.9.8e and earlier does not properly perform Montgomery multiplication, which might allow local users to conduct a side-channel attack and retrieve RSA private keys.",8/8/07,10/17/18,1.2,1.9,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2007-3108,LOW,snippet_test_2 2021.08.19_1_2,1.2,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2008-5077,"OpenSSL 0.9.8i and earlier does not properly check the return value from the EVP_VerifyFinal function, which allows remote attackers to bypass validation of the certificate chain via a malformed SSL/TLS signature for DSA and ECDSA keys.",1/8/09,10/12/18,5.8,8.6,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2008-5077,MEDIUM,snippet_test_2 2021.08.19_1_2,5.8,[CWE-20],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-0590,The ASN1_STRING_print_ex function in OpenSSL before 0.9.8k allows remote attackers to cause a denial of service (invalid memory access and application crash) via vectors that trigger printing of a (1) BMPString or (2) UniversalString with an invalid encoded length.,3/28/09,11/4/20,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-0590,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-0789,"OpenSSL before 0.9.8k on WIN64 and certain other platforms does not properly handle a malformed ASN.1 structure, which allows remote attackers to cause a denial of service (invalid memory access and application crash) by placing this structure in the public key of a certificate, as demonstrated by an RSA public key.",3/28/09,8/17/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-0789,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-189],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-1377,"The dtls1_buffer_record function in ssl/d1_pkt.c in OpenSSL 0.9.8k and earlier 0.9.8 versions allows remote attackers to cause a denial of service (memory consumption) via a large series of ""future epoch"" DTLS records that are buffered in a queue, aka ""DTLS record buffer limitation bug.""",5/20/09,9/29/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-1377,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-1378,"Multiple memory leaks in the dtls1_process_out_of_seq_message function in ssl/d1_both.c in OpenSSL 0.9.8k and earlier 0.9.8 versions allow remote attackers to cause a denial of service (memory consumption) via DTLS records that (1) are duplicates or (2) have sequence numbers much greater than current sequence numbers, aka ""DTLS fragment handling memory leak.""",5/20/09,9/29/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-1378,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-1386,ssl/s3_pkt.c in OpenSSL before 0.9.8i allows remote attackers to cause a denial of service (NULL pointer dereference and daemon crash) via a DTLS ChangeCipherSpec packet that occurs before ClientHello.,6/5/09,9/29/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-1386,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-1387,"The dtls1_retrieve_buffered_fragment function in ssl/d1_both.c in OpenSSL before 1.0.0 Beta 2 allows remote attackers to cause a denial of service (NULL pointer dereference and daemon crash) via an out-of-sequence DTLS handshake message, related to a ""fragment bug.""",6/5/09,9/29/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-1387,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-3555,"The TLS protocol, and the SSL protocol 3.0 and possibly earlier, as used in Microsoft Internet Information Services (IIS) 7.0, mod_ssl in the Apache HTTP Server 2.2.14 and earlier, OpenSSL before 0.9.8l, GnuTLS 2.8.5 and earlier, Mozilla Network Security Services (NSS) 3.12.4 and earlier, multiple Cisco products, and other products, does not properly associate renegotiation handshakes with an existing connection, which allows man-in-the-middle attackers to insert data into HTTPS sessions, and possibly other types of sessions protected by TLS or SSL, by sending an unauthenticated request that is processed retroactively by a server in a post-renegotiation context, related to a ""plaintext injection"" attack, aka the ""Project Mogul"" issue.",11/10/09,2/5/21,5.8,8.6,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-3555,MEDIUM,snippet_test_2 2021.08.19_1_2,5.8,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-4355,"Memory leak in the zlib_stateful_finish function in crypto/comp/c_zlib.c in OpenSSL 0.9.8l and earlier and 1.0.0 Beta through Beta 4 allows remote attackers to cause a denial of service (memory consumption) via vectors that trigger incorrect calls to the CRYPTO_cleanup_all_ex_data function, as demonstrated by use of SSLv3 and PHP with the Apache HTTP Server, a related issue to CVE-2008-1678.",1/15/10,9/19/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-4355,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2009-3245,"OpenSSL before 0.9.8m does not check for a NULL return value from bn_wexpand function calls in (1) crypto/bn/bn_div.c, (2) crypto/bn/bn_gf2m.c, (3) crypto/ec/ec2_smpl.c, and (4) engines/e_ubsec.c, which has unspecified impact and context-dependent attack vectors.",3/6/10,9/19/17,10.0,10.0,10.0,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2009-3245,HIGH,snippet_test_2 2021.08.19_1_2,10.0,[CWE-20],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2010-0433,"The kssl_keytab_is_available function in ssl/kssl.c in OpenSSL before 0.9.8n, when Kerberos is enabled but Kerberos configuration files cannot be opened, does not check a certain return value, which allows remote attackers to cause a denial of service (NULL pointer dereference and daemon crash) via SSL cipher negotiation, as demonstrated by a chroot installation of Dovecot or stunnel without Kerberos configuration files inside the chroot.",3/6/10,10/11/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-0433,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-20],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2010-0742,"The Cryptographic Message Syntax (CMS) implementation in crypto/cms/cms_asn1.c in OpenSSL before 0.9.8o and 1.x before 1.0.0a does not properly handle structures that contain OriginatorInfo, which allows context-dependent attackers to modify invalid memory locations or conduct double-free attacks, and possibly execute arbitrary code, via unspecified vectors.",6/3/10,9/19/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-0742,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2010-4180,"OpenSSL before 0.9.8q, and 1.0.x before 1.0.0c, when SSL_OP_NETSCAPE_REUSE_CIPHER_CHANGE_BUG is enabled, does not properly prevent modification of the ciphersuite in the session cache, which allows remote attackers to force the downgrade to an unintended cipher via vectors involving sniffing network traffic to discover a session identifier.",12/7/10,9/19/17,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-4180,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2010-4252,"OpenSSL before 1.0.0c, when J-PAKE is enabled, does not properly validate the public parameters in the J-PAKE protocol, which allows remote attackers to bypass the need for knowledge of the shared secret, and successfully authenticate, by sending crafted values in each round of the protocol.",12/7/10,9/19/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-4252,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-287],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2008-7270,"OpenSSL before 0.9.8j, when SSL_OP_NETSCAPE_REUSE_CIPHER_CHANGE_BUG is enabled, does not prevent modification of the ciphersuite in the session cache, which allows remote attackers to force the use of a disabled cipher via vectors involving sniffing network traffic to discover a session identifier, a different vulnerability than CVE-2010-4180.",12/7/10,4/6/12,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2008-7270,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2011-1945,"The elliptic curve cryptography (ECC) subsystem in OpenSSL 1.0.0d and earlier, when the Elliptic Curve Digital Signature Algorithm (ECDSA) is used for the ECDHE_ECDSA cipher suite, does not properly implement curves over binary fields, which makes it easier for context-dependent attackers to determine private keys via a timing attack and a lattice calculation.",6/1/11,6/6/13,2.6,4.9,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-1945,LOW,snippet_test_2 2021.08.19_1_2,2.6,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2011-4108,"The DTLS implementation in OpenSSL before 0.9.8s and 1.x before 1.0.0f performs a MAC check only if certain padding is valid, which makes it easier for remote attackers to recover plaintext via a padding oracle attack.",1/6/12,8/23/16,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-4108,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2011-4576,"The SSL 3.0 implementation in OpenSSL before 0.9.8s and 1.x before 1.0.0f does not properly initialize data structures for block cipher padding, which might allow remote attackers to obtain sensitive information by decrypting the padding data sent by an SSL peer.",1/6/12,8/23/16,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-4576,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2011-4577,"OpenSSL before 0.9.8s and 1.x before 1.0.0f, when RFC 3779 support is enabled, allows remote attackers to cause a denial of service (assertion failure) via an X.509 certificate containing certificate-extension data associated with (1) IP address blocks or (2) Autonomous System (AS) identifiers.",1/6/12,3/26/14,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-4577,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2011-4619,"The Server Gated Cryptography (SGC) implementation in OpenSSL before 0.9.8s and 1.x before 1.0.0f does not properly handle handshake restarts, which allows remote attackers to cause a denial of service (CPU consumption) via unspecified vectors.",1/6/12,8/23/16,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-4619,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2012-0027,"The GOST ENGINE in OpenSSL before 1.0.0f does not properly handle invalid parameters for the GOST block cipher, which allows remote attackers to cause a denial of service (daemon crash) via crafted data from a TLS client.",1/6/12,3/26/14,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2012-0027,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2011-4354,"crypto/bn/bn_nist.c in OpenSSL before 0.9.8h on 32-bit platforms, as used in stunnel and other products, in certain circumstances involving ECDH or ECDHE cipher suites, uses an incorrect modular reduction algorithm in its implementation of the P-256 and P-384 NIST elliptic curves, which allows remote attackers to obtain the private key of a TLS server via multiple handshake attempts.",1/27/12,11/6/12,5.8,8.6,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-4354,MEDIUM,snippet_test_2 2021.08.19_1_2,5.8,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2006-7250,The mime_hdr_cmp function in crypto/asn1/asn_mime.c in OpenSSL 0.9.8t and earlier allows remote attackers to cause a denial of service (NULL pointer dereference and application crash) via a crafted S/MIME message.,2/29/12,1/6/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2006-7250,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2012-0884,"The implementation of Cryptographic Message Syntax (CMS) and PKCS #7 in OpenSSL before 0.9.8u and 1.x before 1.0.0h does not properly restrict certain oracle behavior, which makes it easier for context-dependent attackers to decrypt data via a Million Message Attack (MMA) adaptive chosen ciphertext attack.",3/13/12,1/10/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2012-0884,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2012-1165,"The mime_param_cmp function in crypto/asn1/asn_mime.c in OpenSSL before 0.9.8u and 1.x before 1.0.0h allows remote attackers to cause a denial of service (NULL pointer dereference and application crash) via a crafted S/MIME message, a different vulnerability than CVE-2006-7250.",3/16/12,1/13/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2012-1165,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2012-2110,"The asn1_d2i_read_bio function in crypto/asn1/a_d2i_fp.c in OpenSSL before 0.9.8v, 1.0.0 before 1.0.0i, and 1.0.1 before 1.0.1a does not properly interpret integer data, which allows remote attackers to conduct buffer overflow attacks, and cause a denial of service (memory corruption) or possibly have unspecified other impact, via crafted DER data, as demonstrated by an X.509 certificate or an RSA public key.",4/20/12,1/5/18,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2012-2110,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2012-2333,"Integer underflow in OpenSSL before 0.9.8x, 1.0.0 before 1.0.0j, and 1.0.1 before 1.0.1c, when TLS 1.1, TLS 1.2, or DTLS is used with CBC encryption, allows remote attackers to cause a denial of service (buffer over-read) or possibly have unspecified other impact via a crafted TLS packet that is not properly handled during a certain explicit IV calculation.",5/15/12,1/5/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2012-2333,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-189],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2011-1473,"** DISPUTED ** OpenSSL before 0.9.8l, and 0.9.8m through 1.x, does not properly restrict client-initiated renegotiation within the SSL and TLS protocols, which might make it easier for remote attackers to cause a denial of service (CPU consumption) by performing many renegotiations within a single connection, a different vulnerability than CVE-2011-5094.  NOTE: it can also be argued that it is the responsibility of server deployments, not a security library, to prevent or limit renegotiation when it is inappropriate within a specific environment.",6/17/12,4/21/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-1473,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2013-6449,"The ssl_get_algorithm2 function in ssl/s3_lib.c in OpenSSL before 1.0.2 obtains a certain version number from an incorrect data structure, which allows remote attackers to cause a denial of service (daemon crash) via crafted traffic from a TLS 1.2 client.",12/24/13,10/10/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-6449,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-0076,"The Montgomery ladder implementation in OpenSSL through 1.0.0l does not ensure that certain swap operations have a constant-time behavior, which makes it easier for local users to obtain ECDSA nonces via a FLUSH+RELOAD cache side-channel attack.",3/25/14,12/16/17,1.9,3.4,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0076,LOW,snippet_test_2 2021.08.19_1_2,1.9,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2010-5298,"Race condition in the ssl3_read_bytes function in s3_pkt.c in OpenSSL through 1.0.1g, when SSL_MODE_RELEASE_BUFFERS is enabled, allows remote attackers to inject data across sessions or cause a denial of service (use-after-free and parsing error) via an SSL connection in a multithreaded environment.",4/15/14,10/11/18,4.0,4.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-5298,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[CWE-362],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-0195,"The dtls1_reassemble_fragment function in d1_both.c in OpenSSL before 0.9.8za, 1.0.0 before 1.0.0m, and 1.0.1 before 1.0.1h does not properly validate fragment lengths in DTLS ClientHello messages, which allows remote attackers to execute arbitrary code or cause a denial of service (buffer overflow and application crash) via a long non-initial fragment.",6/6/14,4/23/19,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0195,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-0221,"The dtls1_get_message_fragment function in d1_both.c in OpenSSL before 0.9.8za, 1.0.0 before 1.0.0m, and 1.0.1 before 1.0.1h allows remote attackers to cause a denial of service (recursion and client crash) via a DTLS hello message in an invalid DTLS handshake.",6/6/14,4/23/19,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0221,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-0224,"OpenSSL before 0.9.8za, 1.0.0 before 1.0.0m, and 1.0.1 before 1.0.1h does not properly restrict processing of ChangeCipherSpec messages, which allows man-in-the-middle attackers to trigger use of a zero-length master key in certain OpenSSL-to-OpenSSL communications, and consequently hijack sessions or obtain sensitive information, via a crafted TLS handshake, aka the ""CCS Injection"" vulnerability.",6/6/14,7/29/20,5.8,8.6,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0224,MEDIUM,snippet_test_2 2021.08.19_1_2,5.8,[CWE-326],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-3470,"The ssl3_send_client_key_exchange function in s3_clnt.c in OpenSSL before 0.9.8za, 1.0.0 before 1.0.0m, and 1.0.1 before 1.0.1h, when an anonymous ECDH cipher suite is used, allows remote attackers to cause a denial of service (NULL pointer dereference and client crash) by triggering a NULL certificate value.",6/6/14,4/23/19,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-3470,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-3567,"Memory leak in the tls_decrypt_ticket function in t1_lib.c in OpenSSL before 0.9.8zc, 1.0.0 before 1.0.0o, and 1.0.1 before 1.0.1j allows remote attackers to cause a denial of service (memory consumption) via a crafted session ticket that triggers an integrity-check failure.",10/19/14,11/15/17,7.1,8.6,6.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-3567,HIGH,snippet_test_2 2021.08.19_1_2,7.1,"[CWE-20, CWE-399]",false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-3568,"OpenSSL before 0.9.8zc, 1.0.0 before 1.0.0o, and 1.0.1 before 1.0.1j does not properly enforce the no-ssl3 build option, which allows remote attackers to bypass intended access restrictions via an SSL 3.0 handshake, related to s23_clnt.c and s23_srvr.c.",10/19/14,11/15/17,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-3568,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-3570,"The BN_sqr implementation in OpenSSL before 0.9.8zd, 1.0.0 before 1.0.0p, and 1.0.1 before 1.0.1k does not properly calculate the square of a BIGNUM value, which might make it easier for remote attackers to defeat cryptographic protection mechanisms via unspecified vectors, related to crypto/bn/asm/mips.pl, crypto/bn/asm/x86_64-gcc.c, and crypto/bn/bn_asm.c.",1/9/15,11/15/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-3570,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-3571,"OpenSSL before 0.9.8zd, 1.0.0 before 1.0.0p, and 1.0.1 before 1.0.1k allows remote attackers to cause a denial of service (NULL pointer dereference and application crash) via a crafted DTLS message that is processed with a different read operation for the handshake header than for the handshake body, related to the dtls1_get_record function in d1_pkt.c and the ssl3_read_n function in s3_pkt.c.",1/9/15,10/20/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-3571,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-3572,"The ssl3_get_key_exchange function in s3_clnt.c in OpenSSL before 0.9.8zd, 1.0.0 before 1.0.0p, and 1.0.1 before 1.0.1k allows remote SSL servers to conduct ECDHE-to-ECDH downgrade attacks and trigger a loss of forward secrecy by omitting the ServerKeyExchange message.",1/9/15,11/15/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-3572,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-8275,"OpenSSL before 0.9.8zd, 1.0.0 before 1.0.0p, and 1.0.1 before 1.0.1k does not enforce certain constraints on certificate data, which allows remote attackers to defeat a fingerprint-based certificate-blacklist protection mechanism by including crafted data within a certificate's unsigned portion, related to crypto/asn1/a_verify.c, crypto/dsa/dsa_asn1.c, crypto/ecdsa/ecs_vrf.c, and crypto/x509/x_all.c.",1/9/15,11/15/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-8275,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0204,"The ssl3_get_key_exchange function in s3_clnt.c in OpenSSL before 0.9.8zd, 1.0.0 before 1.0.0p, and 1.0.1 before 1.0.1k allows remote SSL servers to conduct RSA-to-EXPORT_RSA downgrade attacks and facilitate brute-force decryption by offering a weak ephemeral RSA key in a noncompliant role, related to the ""FREAK"" issue.  NOTE: the scope of this CVE is only client code based on OpenSSL, not EXPORT_RSA issues associated with servers or other TLS implementations.",1/9/15,7/19/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0204,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0209,"Use-after-free vulnerability in the d2i_ECPrivateKey function in crypto/ec/ec_asn1.c in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a might allow remote attackers to cause a denial of service (memory corruption and application crash) or possibly have unspecified other impact via a malformed Elliptic Curve (EC) private-key file that is improperly handled during import.",3/20/15,1/5/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0209,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0286,"The ASN1_TYPE_cmp function in crypto/asn1/a_type.c in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a does not properly perform boolean-type comparisons, which allows remote attackers to cause a denial of service (invalid read operation and application crash) via a crafted X.509 certificate to an endpoint that uses the certificate-verification feature.",3/20/15,1/5/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0286,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-17],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0287,"The ASN1_item_ex_d2i function in crypto/asn1/tasn_dec.c in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a does not reinitialize CHOICE and ADB data structures, which might allow attackers to cause a denial of service (invalid write operation and memory corruption) by leveraging an application that relies on ASN.1 structure reuse.",3/20/15,11/15/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0287,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-17],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0288,"The X509_to_X509_REQ function in crypto/x509/x509_req.c in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a might allow attackers to cause a denial of service (NULL pointer dereference and application crash) via an invalid certificate key.",3/20/15,11/15/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0288,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0289,"The PKCS#7 implementation in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a does not properly handle a lack of outer ContentInfo, which allows attackers to cause a denial of service (NULL pointer dereference and application crash) by leveraging an application that processes arbitrary PKCS#7 data and providing malformed data with ASN.1 encoding, related to crypto/pkcs7/pk7_doit.c and crypto/pkcs7/pk7_lib.c.",3/20/15,10/20/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0289,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0292,"Integer underflow in the EVP_DecodeUpdate function in crypto/evp/encode.c in the base64-decoding implementation in OpenSSL before 0.9.8za, 1.0.0 before 1.0.0m, and 1.0.1 before 1.0.1h allows remote attackers to cause a denial of service (memory corruption) or possibly have unspecified other impact via crafted base64 data that triggers a buffer overflow.",3/20/15,11/15/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0292,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-0293,"The SSLv2 implementation in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a allows remote attackers to cause a denial of service (s2_lib.c assertion failure and daemon exit) via a crafted CLIENT-MASTER-KEY message.",3/20/15,1/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-0293,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-20],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-4000,"The TLS protocol 1.2 and earlier, when a DHE_EXPORT ciphersuite is enabled on a server but not on a client, does not properly convey a DHE_EXPORT choice, which allows man-in-the-middle attackers to conduct cipher-downgrade attacks by rewriting a ClientHello with DHE replaced by DHE_EXPORT and then rewriting a ServerHello with DHE_EXPORT replaced by DHE, aka the ""Logjam"" issue.",5/21/15,1/20/21,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-4000,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2014-8176,"The dtls1_clear_queues function in ssl/d1_lib.c in OpenSSL before 0.9.8za, 1.0.0 before 1.0.0m, and 1.0.1 before 1.0.1h frees data structures without considering that application data can arrive between a ChangeCipherSpec message and a Finished message, which allows remote DTLS peers to cause a denial of service (memory corruption and application crash) or possibly have unspecified other impact via unexpected application data.",6/13/15,1/5/18,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-8176,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-1788,"The BN_GF2m_mod_inv function in crypto/bn/bn_gf2m.c in OpenSSL before 0.9.8s, 1.0.0 before 1.0.0e, 1.0.1 before 1.0.1n, and 1.0.2 before 1.0.2b does not properly handle ECParameters structures in which the curve is over a malformed binary polynomial field, which allows remote attackers to cause a denial of service (infinite loop) via a session that uses an Elliptic Curve algorithm, as demonstrated by an attack against a server that supports client authentication.",6/13/15,11/15/17,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-1788,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-1789,"The X509_cmp_time function in crypto/x509/x509_vfy.c in OpenSSL before 0.9.8zg, 1.0.0 before 1.0.0s, 1.0.1 before 1.0.1n, and 1.0.2 before 1.0.2b allows remote attackers to cause a denial of service (out-of-bounds read and application crash) via a crafted length field in ASN1_TIME data, as demonstrated by an attack against a server that supports client authentication with a custom verification callback.",6/13/15,11/15/17,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-1789,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-1790,"The PKCS7_dataDecodefunction in crypto/pkcs7/pk7_doit.c in OpenSSL before 0.9.8zg, 1.0.0 before 1.0.0s, 1.0.1 before 1.0.1n, and 1.0.2 before 1.0.2b allows remote attackers to cause a denial of service (NULL pointer dereference and application crash) via a PKCS#7 blob that uses ASN.1 encoding and lacks inner EncryptedContent data.",6/13/15,10/20/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-1790,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-1791,"Race condition in the ssl3_get_new_session_ticket function in ssl/s3_clnt.c in OpenSSL before 0.9.8zg, 1.0.0 before 1.0.0s, 1.0.1 before 1.0.1n, and 1.0.2 before 1.0.2b, when used for a multi-threaded client, allows remote attackers to cause a denial of service (double free and application crash) or possibly have unspecified other impact by providing a NewSessionTicket during an attempt to reuse a ticket that had been obtained earlier.",6/13/15,11/15/17,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-1791,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-362],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-1792,"The do_free_upto function in crypto/cms/cms_smime.c in OpenSSL before 0.9.8zg, 1.0.0 before 1.0.0s, 1.0.1 before 1.0.1n, and 1.0.2 before 1.0.2b allows remote attackers to cause a denial of service (infinite loop) via vectors that trigger a NULL value of a BIO data structure, as demonstrated by an unrecognized X.660 OID for a hash function.",6/13/15,11/15/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-1792,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2015-3195,"The ASN1_TFLG_COMBINE implementation in crypto/asn1/tasn_dec.c in OpenSSL before 0.9.8zh, 1.0.0 before 1.0.0t, 1.0.1 before 1.0.1q, and 1.0.2 before 1.0.2e mishandles errors caused by malformed X509_ATTRIBUTE data, which allows remote attackers to obtain sensitive information from process memory by triggering a decoding failure in a PKCS#7 or CMS application.",12/7/15,1/20/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3195,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-200],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-0703,"The get_client_master_key function in s2_srvr.c in the SSLv2 implementation in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a accepts a nonzero CLIENT-MASTER-KEY CLEAR-KEY-LENGTH value for an arbitrary cipher, which allows man-in-the-middle attackers to determine the MASTER-KEY value and decrypt TLS ciphertext data by leveraging a Bleichenbacher RSA padding oracle, a related issue to CVE-2016-0800.",3/2/16,1/19/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-0703,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-200],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-0704,"An oracle protection mechanism in the get_client_master_key function in s2_srvr.c in the SSLv2 implementation in OpenSSL before 0.9.8zf, 1.0.0 before 1.0.0r, 1.0.1 before 1.0.1m, and 1.0.2 before 1.0.2a overwrites incorrect MASTER-KEY bytes during use of export cipher suites, which makes it easier for remote attackers to decrypt TLS ciphertext data by leveraging a Bleichenbacher RSA padding oracle, a related issue to CVE-2016-0800.",3/2/16,1/19/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-0704,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-200],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2000-1254,"crypto/rsa/rsa_gen.c in OpenSSL before 0.9.6 mishandles C bitwise-shift operations that exceed the size of an expression, which makes it easier for remote attackers to defeat cryptographic protection mechanisms by leveraging improper RSA key generation on 64-bit HP-UX platforms.",5/5/16,2/2/17,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2000-1254,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-310],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-2106,Integer overflow in the EVP_EncryptUpdate function in crypto/evp/evp_enc.c in OpenSSL before 1.0.1t and 1.0.2 before 1.0.2h allows remote attackers to cause a denial of service (heap memory corruption) via a large amount of data.,5/5/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-2106,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-189],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-2107,"The AES-NI implementation in OpenSSL before 1.0.1t and 1.0.2 before 1.0.2h does not consider memory allocation during a certain padding check, which allows remote attackers to obtain sensitive cleartext information via a padding-oracle attack against an AES CBC session. NOTE: this vulnerability exists because of an incorrect fix for CVE-2013-0169.",5/5/16,10/31/18,2.6,4.9,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-2107,LOW,snippet_test_2 2021.08.19_1_2,2.6,"[CWE-200, CWE-310]",false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-2108,"The ASN.1 implementation in OpenSSL before 1.0.1o and 1.0.2 before 1.0.2c allows remote attackers to execute arbitrary code or cause a denial of service (buffer underflow and memory corruption) via an ANY field in crafted serialized data, aka the ""negative zero"" issue.",5/5/16,1/5/18,10.0,10.0,10.0,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-2108,HIGH,snippet_test_2 2021.08.19_1_2,10.0,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-2109,The asn1_d2i_read_bio function in crypto/asn1/a_d2i_fp.c in the ASN.1 BIO implementation in OpenSSL before 1.0.1t and 1.0.2 before 1.0.2h allows remote attackers to cause a denial of service (memory consumption) via a short invalid encoding.,5/5/16,7/19/18,7.8,10.0,6.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-2109,HIGH,snippet_test_2 2021.08.19_1_2,7.8,[CWE-399],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-2176,The X509_NAME_oneline function in crypto/x509/x509_obj.c in OpenSSL before 1.0.1t and 1.0.2 before 1.0.2h allows remote attackers to obtain sensitive information from process stack memory or cause a denial of service (buffer over-read) via crafted EBCDIC ASN.1 data.,5/5/16,7/19/18,6.4,10.0,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-2176,MEDIUM,snippet_test_2 2021.08.19_1_2,6.4,[CWE-119],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2016-7056,A timing attack flaw was found in OpenSSL 1.0.1u and before that could allow a malicious user with local access to recover ECDSA P-256 private keys.,9/11/18,10/10/19,2.1,3.9,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-7056,LOW,snippet_test_2 2021.08.19_1_2,2.1,[CWE-320],false,false,false,CVSS 2.0,Manually Identified,false
,cc8d0e2b-f88f-49c8-b42f-42626640f589,1e758e94-291d-4c3c-b529-bc25a2df874e,96cf8511-ab97-4764-a972-71a007c6e998,OpenSSL,0.9.1b,github,openssl/openssl:SSLeay_0_9_1b,SSLeay_0_9_1b,CVE-2018-20997,An issue was discovered in the openssl crate before 0.10.9 for Rust. A use-after-free occurs in CMS Signing.,8/27/19,8/30/19,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-20997,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-416],false,false,false,CVSS 2.0,Manually Identified,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,78c44924-5bf7-4849-8b49-87c7bf762650,Spring Framework,2.5.6,maven,org.springframework:spring-aop:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,dcadf6a2-38df-44d0-adff-8d8179013441,Spring Framework,2.5.6,maven,org.springframework:spring-beans:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,5bb06b54-5528-4fb3-baae-42e9e1739a5b,Spring Framework,2.5.6,maven,org.springframework:spring-context-support:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,d615debc-5738-40b7-9f99-9a20c140af2e,Spring Framework,2.5.6,maven,org.springframework:spring-context:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,898c6907-7de2-423c-ba28-521641f8dccf,Spring Framework,2.5.6,maven,org.springframework:spring-core:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,27507fb9-b5ed-482d-8a82-51eca69bd8b5,Spring Framework,2.5.6,maven,org.springframework:spring-jdbc:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,20d0592f-4beb-46f4-a37b-c9a7478ccfd2,Spring Framework,2.5.6,maven,org.springframework:spring-orm:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,98e0b102-9868-45f4-bae3-1a8c47f3f18a,Spring Framework,2.5.6,maven,org.springframework:spring-web:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,cccdaa1e-51c5-41f1-aecb-843a2f1d76d5,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc-struts:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2010-1622,"SpringSource Spring Framework 2.5.x before 2.5.6.SEC02, 2.5.7 before 2.5.7.SR01, and 3.0.x before 3.0.3 allows remote attackers to execute arbitrary code via an HTTP request containing class.classLoader.URLs[0]=jar: followed by a URL of a crafted .jar file.",6/22/10,12/7/16,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2010-1622,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[CWE-94],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2011-2730,"VMware SpringSource Spring Framework before 2.5.6.SEC03, 2.5.7.SR023, and 3.x before 3.0.6, when a container supports Expression Language (EL), evaluates EL expressions in tags twice, which allows remote attackers to obtain sensitive information via a (1) name attribute in a (a) spring:hasBindErrors tag; (2) path attribute in a (b) spring:bind or (c) spring:nestedpath tag; (3) arguments, (4) code, (5) text, (6) var, (7) scope, or (8) message attribute in a (d) spring:message or (e) spring:theme tag; or (9) var, (10) scope, or (11) value attribute in a (f) spring:transform tag, aka ""Expression Language Injection.""",12/6/12,8/9/17,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2011-2730,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-16],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2013-4152,"The Spring OXM wrapper in Spring Framework before 3.2.4 and 4.0.0.M1, when using the JAXB marshaller, does not disable entity resolution, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via an XML external entity declaration in conjunction with an entity reference in a (1) DOMSource, (2) StAXSource, (3) SAXSource, or (4) StreamSource, aka an XML External Entity (XXE) issue.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-4152,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2013-7315,"The Spring MVC in Spring Framework before 3.2.4 and 4.0.0.M1 through 4.0.0.M2 does not disable external entity resolution for the StAX XMLInputFactory, which allows context-dependent attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML with JAXB, aka an XML External Entity (XXE) issue, and a different vulnerability than CVE-2013-4152.  NOTE: this issue was SPLIT from CVE-2013-4152 due to different affected versions.",1/24/14,11/29/16,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2013-7315,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2014-0054,"The Jaxb2RootElementHttpMessageConverter in Spring MVC in Spring Framework before 3.2.8 and 4.0.0 before 4.0.2 does not disable external entity resolution, which allows remote attackers to read arbitrary files, cause a denial of service, and conduct CSRF attacks via crafted XML, aka an XML External Entity (XXE) issue.  NOTE: this vulnerability exists because of an incomplete fix for CVE-2013-4152, CVE-2013-7315, and CVE-2013-6429.",4/17/14,4/20/18,6.8,8.6,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2014-0054,MEDIUM,snippet_test_2 2021.08.19_1_2,6.8,[CWE-352],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2015-3192,"Pivotal Spring Framework before 3.2.14 and 4.x before 4.1.7 do not properly process inline DTD declarations when DTD is not entirely disabled, which allows remote attackers to cause a denial of service (memory consumption and out-of-memory errors) via a crafted XML file.",7/13/16,1/5/18,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2015-3192,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-119],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2016-9878,"An issue was discovered in Pivotal Spring Framework before 3.2.18, 4.2.x before 4.2.9, and 4.3.x before 4.3.5. Paths provided to the ResourceServlet were not properly sanitized and as a result exposed to directory traversal attacks.",12/29/16,7/19/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-9878,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2016-5007,"Both Spring Security 3.2.x, 4.0.x, 4.1.0 and the Spring Framework 3.2.x, 4.0.x, 4.1.x, 4.2.x rely on URL pattern mappings for authorization and for mapping requests to controllers respectively. Differences in the strictness of the pattern matching mechanisms, for example with regards to space trimming in path segments, can lead Spring Security to not recognize certain paths as not protected that are in fact mapped to Spring MVC controllers that should be protected. The problem is compounded by the fact that the Spring Framework provides richer features with regards to pattern matching as well as by the fact that pattern matching in each Spring Security and the Spring Framework can easily be customized creating additional differences.",5/26/17,4/20/18,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2016-5007,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[CWE-264],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2018-1270,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack.",4/6/18,4/24/21,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1270,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2018-1271,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, allow applications to configure Spring MVC to serve static resources (e.g. CSS, JS, images). When static resources are served from a file system on Windows (as opposed to the classpath, or the ServletContext), a malicious user can send a request using a specially crafted URL that can lead a directory traversal attack.",4/6/18,7/15/20,4.3,8.6,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1271,MEDIUM,snippet_test_2 2021.08.19_1_2,4.3,[CWE-22],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2018-1272,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.15 and older unsupported versions, provide client-side support for multipart requests. When Spring MVC or Spring WebFlux server application (server A) receives input from a remote client, and then uses that input to make a multipart request to another server (server B), it can be exposed to an attack, where an extra multipart is inserted in the content of the request from server A, causing server B to use the wrong value for a part it expects. This could to lead privilege escalation, for example, if the part content represents a username or user roles.",4/6/18,7/15/20,6.0,6.8,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1272,MEDIUM,snippet_test_2 2021.08.19_1_2,6.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2018-1275,"Spring Framework, versions 5.0 prior to 5.0.5 and versions 4.3 prior to 4.3.16 and older unsupported versions, allow applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a remote code execution attack. This CVE addresses the partial fix for CVE-2018-1270 in the 4.3.x branch of the Spring Framework.",4/11/18,7/15/20,7.5,10.0,6.4,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1275,HIGH,snippet_test_2 2021.08.19_1_2,7.5,[CWE-358],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2018-1257,"Spring Framework, versions 5.0.x prior to 5.0.6, versions 4.3.x prior to 4.3.17, and older unsupported versions allows applications to expose STOMP over WebSocket endpoints with a simple, in-memory STOMP broker through the spring-messaging module. A malicious user (or attacker) can craft a message to the broker that can lead to a regular expression, denial of service attack.",5/12/18,8/25/20,4.0,8.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-1257,MEDIUM,snippet_test_2 2021.08.19_1_2,4.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2018-15756,"Spring Framework, version 5.1, versions 5.0.x prior to 5.0.10, versions 4.3.x prior to 4.3.20, and older unsupported versions on the 4.2.x branch provide support for range requests when serving static resources through the ResourceHttpRequestHandler, or starting in 5.0 when an annotated controller returns an org.springframework.core.io.Resource. A malicious user (or attacker) can add a range header with a high number of ranges, or with wide ranges that overlap, or both, for a denial of service attack. This vulnerability affects applications that depend on either spring-webmvc or spring-webflux. Such applications must also have a registration for serving static resources (e.g. JS, CSS, images, and others), or have an annotated controller that returns an org.springframework.core.io.Resource. Spring Boot applications that depend on spring-boot-starter-web or spring-boot-starter-webflux are ready to serve static resources out of the box and are therefore vulnerable.",10/19/18,4/24/21,5.0,10.0,2.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2018-15756,MEDIUM,snippet_test_2 2021.08.19_1_2,5.0,[],false,false,false,CVSS 2.0,Exact,false
,08f3bea3-fbfb-4f01-97dd-3f49419f3ea9,4c0efb5b-acfb-4b53-80ed-692ad542e165,6b234bac-c5c2-492a-9523-e8918e20e680,Spring Framework,2.5.6,maven,org.springframework:spring-webmvc:2.5.6,2.5.6,CVE-2020-5421,"In Spring Framework versions 5.2.0 - 5.2.8, 5.1.0 - 5.1.17, 5.0.0 - 5.0.18, 4.3.0 - 4.3.28, and older unsupported versions, the protections against RFD attacks from CVE-2015-5211 may be bypassed depending on the browser used through the use of a jsessionid path parameter.",9/19/20,1/20/21,3.6,3.9,4.9,NVD,NEW,,,,https://nvd.nist.gov/vuln/detail/CVE-2020-5421,LOW,snippet_test_2 2021.08.19_1_2,3.6,[],false,false,false,CVSS 2.0,Exact,false
