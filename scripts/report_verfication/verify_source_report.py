import glob
import json
import os
import shutil

import pandas as pd
import time
import zipfile

from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    # token='YzljY2M5YzgtNTE1OS00NDEyLWFjMjMtYmYxMTliOTUwMjM3Ojc0MGRhMzA4LTA4NjMtNGVmNS05MDM4LWFlZGI2YmU5NzY5MA==',
    # base_url='https://us1a-sup-cn-hub10.nprd.sig.synopsys.com/',
    token='OTNmNTMxNWItMzk0ZC00NjgzLTg4ZDQtZDgyZDdlNjc3YWVjOmM5NGFlYWQ4LTdjZGMtNDNlYy1iOGRiLWJkNzUyNGQ0NmIzYw==',
    base_url='https://cn58sigkb01/',
    # token='OTkyMGRkMWMtYmRjNC00ODk1LTkwMDktNDE1NWI1YjBmMTA0OjRjMDRjNTdlLWFlYzgtNDU5OC05NGM5LWZiMTBhYzMwNzI4Mg==',
    # base_url='https://qa-hub31.dc1.lan/',
    # token='OGM1MjQ1ZDYtMTBmZC00MTk2LWJmMGEtMzAyMzQwZDUxZDBlOjgyM2RjNTYzLTI3MzItNDRhMy1hY2ExLTVjMjFkODQxODQyMg==',
    # base_url='https://test-yuan.app.blackduck.com',
    verify=False  # TLS certificate verification
)


def get_snippet_from_web(project_id, project_version_id):
    api = f'/api/internal/projects/{project_id}/versions/{project_version_id}' \
          f'/source-bom-entries?limit=999999&sort=&' \
          f'filter=bomMatchReviewStatus:not_reviewed&filter=' \
          f'bomMatchReviewStatus:reviewed&filter=bomMatchType:snippet'
    r = bd.session.get(api)
    data = json.loads(r.text)
    return data


def generate_snippet_record_from_web(project_id=None, project_version_id=None,
                                     fp=None):
    """

    :param fp:
    :return:
    """
    if project_id is not None and project_version_id is not None:
        data = get_snippet_from_web(project_id, project_version_id)
    elif fp is not None:
        data = json.load(open(fp, encoding='utf-8'))
    else:
        raise ValueError('Either project_id/project_version_id or fp needs to '
                         'have values')
    record_dict = {}
    for it in data['items']:
        path = it['compositePath']['path']
        archive_context = it['compositePath']['archiveContext']
        for com in it['fileSnippetBomComponents']:
            cid = com['project']['id']
            vid = com['release']['id']
            if 'channelRelease' not in com:
                # There could be no origin for a component/version
                oid = ''
            else:
                oid = com['channelRelease']['id']
            key = ' '.join([path, archive_context, cid, vid, oid])
            # if path == 'codeUniBTS/bsp/modules/broadcom-sdk/include/shared/et/proto/802.1d.h':
            #     print(key)
            adjusted_status = com['adjusted']
            ignored_status = com['ignored']
            review_status = com['reviewStatus']
            component_name = com['project']['name']
            component_version_name = com['release']['version']
            record_dict[key] = {'adjusted': adjusted_status,
                                'ignored': ignored_status,
                                'reviewed': review_status,
                                'component_name': component_name,
                                'component_version_name': component_version_name}
            if key == 'sql/rpl_handler.cc  1c8d49e8-b312-40e4-bfd5-0b6f115ce454 cf0a094d-39a5-4d36-b20d-b9e08aac350f fe7b7beb-426a-45b5-bfdb-4a9d0b26abd6':
                print(len(it['fileSnippetBomComponents']))
    return record_dict


def generate_ignored_snippet_record_from_web(project_id=None,
                                             project_version_id=None, fp=None):
    if project_id is not None and project_version_id is not None:
        data = get_snippet_from_web(project_id, project_version_id)
    elif fp is not None:
        data = json.load(open(fp))
    else:
        raise ValueError('Either project_id/project_version_id or fp needs to '
                         'have values')
    ignored_record_dict = {}
    for it in data['items']:
        path = it['compositePath']['path']
        archive_context = it['compositePath']['archiveContext']
        for com in it['fileSnippetBomComponents']:
            cid = com['project']['id']
            vid = com['release']['id']
            if 'channelRelease' not in com:
                # There could be no origin for a component/version
                oid = ''
            else:
                oid = com['channelRelease']['id']
            key = ' '.join([path, archive_context, cid, vid, oid])
            adjusted_status = com['adjusted']
            ignored_status = com['ignored']
            review_status = com['reviewStatus']
            component_name = com['project']['name']
            component_version_name = com['release']['version']
            if ignored_status:
                ignored_record_dict[key] = {'adjusted': adjusted_status,
                                            'ignored': ignored_status,
                                            'reviewed': review_status,
                                            'component_name': component_name,
                                            'component_version_name': component_version_name}
    return ignored_record_dict


def generate_version_report(project_version_id, max_wait=300):
    """
    :param project_version_id: project version id
    :param max_wait: max wait time (seconds), 300 by default
    :return: report_id
    """
    generate_report_api = f'/api/versions/{project_version_id}/reports'
    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.report-4+json'
    }
    payload = {
        "reportFormat": "CSV",
        "locale": "en_US",
        "versionId": project_version_id,
        "categories": [
            "VERSION",
            "CODE_LOCATIONS",
            "COMPONENTS",
            "SECURITY",
            "FILES"
        ],
        "reportType": "VERSION"
    }
    r = bd.session.post(generate_report_api, data=json.dumps(payload),
                        headers=headers)
    if r.status_code == 201:
        report_id = r.headers['Location'].split('/')[-1]
        # return report_id
    else:
        raise Exception(f'Failed to generate report for {project_version_id}. '
                        f'Error status code: {r.status_code}. '
                        f'Error text: {r.text} ')

    get_report_api = f'/api/versions/{project_version_id}/reports/{report_id}'
    headers = {
        'Accept': 'application/vnd.blackducksoftware.report-4+json'
    }
    start = time.time()
    while time.time() - start <= max_wait:
        try:
            r = bd.session.get(get_report_api, headers=headers)
            data = json.loads(r.text)
            if data['status'] == 'COMPLETED':
                return report_id
            time.sleep(1)
        except:
            raise
    else:
        print(f'Failed to complete report generation within {max_wait} seconds')
        return None


def download_report_file(report_id):
    # clean up before download and extraction
    if os.path.exists('./report.zip'):
        os.remove('./report.zip')
    if os.path.exists('./report'):
        shutil.rmtree('./report')
    download_api = f'/api/reports/{report_id}'
    r = bd.session.get(download_api, stream=True)
    if r.status_code == 200:
        with open('./report.zip', 'wb') as f:
            for chunk in r.iter_content():
                f.write(chunk)
    with zipfile.ZipFile("report.zip", "r") as zip_ref:
        zip_ref.extractall("report")


def generate_snippet_record_from_report_file(project_id=None,
                                             project_version_id=None, fp=None):
    if project_id is not None and project_version_id is not None:
        report_id = generate_version_report(project_version_id)
        download_report_file(report_id)
        currenty_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(os.path.join(currenty_dir, 'report'))
        sub_dir = glob.glob('*')[0]
        os.chdir(os.path.join(currenty_dir, 'report', sub_dir))
        source_report = glob.glob('source*')[0]
        fp = os.path.join(currenty_dir, 'report', sub_dir, source_report)
    report_data = pd.read_csv(fp)
    snippet_report_data = report_data.loc[
        report_data['Match type'] == 'Snippet']
    snippet_record = []
    for i, r in snippet_report_data.iterrows():
        key = ' '.join([str(r['Path']), str(r['Archive context']).replace('nan', ''),
                        str(r['Component id']), str(r['Version id']),
                        str(r['Origin id']).replace('nan', '')])
        # if str(r['Path']) == 'codeUniBTS/bsp/modules/broadcom-sdk/include/shared/et/proto/802.1d.h':
        #     print(key)
        snippet_record.append(key)
    return snippet_record


def _url_to_ids(url):
    """
    url to project id and project version id
    """
    url_split = url.split('/')
    return url_split[5], url_split[7]


if __name__ == '__main__':
    json_fp = r'C:\Projects\Blackduck\Support Cases\2022\ZTE Corporation - 【hub咨询】hub无法正常添加组件\attachment\source-bom-entries.json'
    report_fp = r'C:\Projects\Blackduck\Support Cases\2022\ZTE Corporation - hub ignore掉的片段在重新扫描后再次出现\attachment\#2(b.txt.tar\#2(b.txt\#1(b.txt'
    # snippet_record = generate_snippet_record_from_report_file(fp=report_fp)
    # snippet_record_dict = generate_snippet_record_from_web(fp=json_fp)
    project_version_url = 'https://cn58sigkb01/api/projects/283886f8-1d72-4a8f-bfaf-92bc1446125d/versions/33d23a9e-9718-481e-9d23-a52c56c7baeb/components?sort=projectName%20ASC&offset=0&limit=100'
    project_id, project_version_id = _url_to_ids(project_version_url)
    # project_id = '795e7618-f97f-4750-9a7f-6f214bf0f2ef'
    # project_version_id = 'be2400d9-480f-4431-891d-406128950253'
    # print(generate_version_report(project_version_id))


    # snippet_record = generate_snippet_record_from_report_file(project_id=project_id, project_version_id=project_version_id)
    # snippet_record_dict = generate_snippet_record_from_web(project_id=project_id, project_version_id=project_version_id)
    #
    # snippet_record = generate_snippet_record_from_report_file(fp=report_fp)
    snippet_record_dict = generate_snippet_record_from_web(fp=json_fp)

    print(f'Record count on the UI: {len(snippet_record_dict)}')
    # print(f'Record count in the source csv: {len(snippet_record)}')

    # print(snippet_record[0])
    # print(snippet_record_dict)

    for k, v in snippet_record_dict.items():
        if v['component_name'] == 'Termd Core':
            # if not v['ignored']:
        # if not v['ignored'] and v['reviewed'] == 'NOT_REVIEWED':
            print(k, snippet_record_dict[k])

    # for k in snippet_record_dict:
    #     # if k not in snippet_record and not snippet_record_dict[k]['ignored']:
    #     if k not in snippet_record:
    #         print(k, snippet_record_dict[k])

    print('========================================')

    # for k in snippet_record_dict:
    #     # if k in snippet_record and snippet_record_dict[k]['ignored']:
    #     if k in snippet_record and snippet_record_dict[k]['component_name'] == 'facebookresearch/faiss':
    #     # if k not in snippet_record:
    #         print(k, snippet_record_dict[k])
    # igored_snippet_record_dict = generate_ignored_snippet_record_from_web(project_id=project_id, project_version_id=project_version_id)
    # for k in igored_snippet_record_dict:
    #         print(k, snippet_record_dict[k])