"""
BOM v.s. component.csv
"""

import glob
import json
import os
import shutil

import pandas as pd
import time
import zipfile

from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token='OTNmNTMxNWItMzk0ZC00NjgzLTg4ZDQtZDgyZDdlNjc3YWVjOmM5NGFlYWQ4LTdjZGMtNDNlYy1iOGRiLWJkNzUyNGQ0NmIzYw==',
    base_url='https://cn58sigkb01/',
    verify=False  # TLS certificate verification
)


def _url_to_ids(url):
    """
    url to project id and project version id
    """
    url_split = url.split('/')
    return url_split[5], url_split[7]


def get_bom_components(project_id, project_version_id):
    api = f'/api/projects/{project_id}/versions/{project_version_id}/components'
    params = {
        'limit': 999999,
        'filter': ['bomInclusion:false', 'bomInclusion:true']

    }
    r = bd.session.get(api, params=params)
    return json.loads(r.text)


def generate_version_report(project_version_id, max_wait=300):
    """
    :param project_version_id: project version id
    :param max_wait: max wait time (seconds), 300 by default
    :return: report_id
    """
    generate_report_api = f'/api/versions/{project_version_id}/reports'
    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.report-4+json'
    }
    payload = {
        "reportFormat": "CSV",
        "locale": "en_US",
        "versionId": project_version_id,
        "categories": [
            "VERSION",
            "CODE_LOCATIONS",
            "COMPONENTS",
            "SECURITY",
            "FILES"
        ],
        "reportType": "VERSION"
    }
    r = bd.session.post(generate_report_api, data=json.dumps(payload),
                        headers=headers)
    if r.status_code == 201:
        report_id = r.headers['Location'].split('/')[-1]
        # return report_id
    else:
        raise Exception(f'Failed to generate report for {project_version_id}. '
                        f'Error status code: {r.status_code}. '
                        f'Error text: {r.text} ')

    get_report_api = f'/api/versions/{project_version_id}/reports/{report_id}'
    headers = {
        'Accept': 'application/vnd.blackducksoftware.report-4+json'
    }
    start = time.time()
    while time.time() - start <= max_wait:
        try:
            r = bd.session.get(get_report_api, headers=headers)
            data = json.loads(r.text)
            if data['status'] == 'COMPLETED':
                return report_id
            time.sleep(1)
        except:
            raise
    else:
        print(f'Failed to complete report generation within {max_wait} seconds')
        return None


def download_report_file(report_id):
    # clean up before download and extraction
    if os.path.exists('./report.zip'):
        os.remove('./report.zip')
    if os.path.exists('./report'):
        shutil.rmtree('./report')
    download_api = f'/api/reports/{report_id}'
    r = bd.session.get(download_api, stream=True)
    if r.status_code == 200:
        with open('./report.zip', 'wb') as f:
            for chunk in r.iter_content():
                f.write(chunk)
    with zipfile.ZipFile("report.zip", "r") as zip_ref:
        zip_ref.extractall("report")


def generate_component_record_from_bom(project_id=None,
                                       project_version_id=None, fp=None):
    component_record = {}
    if project_id is not None and project_version_id is not None:
        component_data = get_bom_components(project_id, project_version_id)
    elif fp is not None:
        component_data = json.load(fp)
    else:
        raise ValueError('You should either specify the project_id / '
                         'project_version_id or the fp.')
    for it in component_data['items']:
        cid = it['component'].split('/')[-1]
        if 'componentVersion' in it:
            vid = it['componentVersion'].split('/')[-1]
        else:
            vid = ''
        key = cid + ' ' + vid
        component_name = it['componentName']
        if 'componentVersionName' in it:
            component_version_name = it['componentVersionName']
        else:
            component_version_name = ''
        ignored = it['ignored']
        match_types = ','.join(it['matchTypes'])
        values = {'component_name': component_name,
                  'component_version_name': component_version_name,
                  'ignored': ignored,
                  'match_types': match_types}
        component_record[key] = values
    return component_record


def generate_component_record_from_report(project_id=None,
                                          project_version_id=None, fp=None):
    if project_id is not None and project_version_id is not None:
        report_id = generate_version_report(project_version_id)
        download_report_file(report_id)
        currenty_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(os.path.join(currenty_dir, 'report'))
        sub_dir = glob.glob('*')[0]
        os.chdir(os.path.join(currenty_dir, 'report', sub_dir))
        source_report = glob.glob('components*')[0]
        fp = os.path.join(currenty_dir, 'report', sub_dir, source_report)
    component_data = pd.read_csv(fp)
    component_record = {}
    for i, r in component_data.iterrows():
        key = r['Component id'] + ' ' + r['Version id']
        values = {'component_name': r['Component name'],
                  'component_version_name': r['Component version name'],
                  'match_types': r['Match type'],
                  'snippet_review_status': r['Snippet Review status']}
        component_record[key] = values

    return component_record


if __name__ == '__main__':
    project_version_url = 'https://cn58sigkb01/api/projects/300afd72-6ff3-4f83-92ec-31867b65c2c6/versions/48500007-e194-463b-ad51-49212f68b86d/components?sort=projectName%20ASC&offset=0&limit=100&filter=bomInclusion%3Afalse&filter=bomInclusion%3Atrue'
    project_id, project_version_id = _url_to_ids(project_version_url)

    bom_component_rec = generate_component_record_from_bom(project_id,
                                                           project_version_id)
    report_component_rec = generate_component_record_from_report(
        project_id, project_version_id)
    print('Bom component (including "ignored") record count:',
          len(bom_component_rec))
    print('Report component record count:', len(report_component_rec))

    for k, v in report_component_rec.items():
        if k not in bom_component_rec:
            print(k, v)
    print('=========================================================')
    # for k, v in bom_component_rec.items():
    #     if k not in report_component_rec:
    #         print(k, v)
    for k,v in report_component_rec.items():
        if v['snippet_review_status'] == 'Needs confirmation' and k in bom_component_rec:
            print(k, v)
