import json
from blackduck.HubRestApi import HubInstance

# Note: change below settings in accord with your server
username = 'sysadmin'
password = 'blackduck'
urlbase = 'https://sup-cn-hub10.dc1.lan/'

hub = HubInstance(urlbase, username, password, insecure=True)
url_base = hub.get_urlbase()

user_api_url = url_base + '/api/users?limit=999999&filter=userStatus:true'
roles_api_url = url_base + '/api/users/{}/roles?filter=scope:server'


def run():
    # Get all user information
    r = hub.execute_get(user_api_url)
    data = json.loads(r.text)
    print(f'{data["totalCount"]} users to check.')
    for it in data['items']:
        user_roles = [role['name'] for role in it['roles']]
        user_id = it['_meta']['href'].split('/')[-1]
        r = hub.execute_get(roles_api_url.format(user_id))
        # Use role API to get verified role assignment
        verified_data = json.loads(r.text)
        verified_user_roles = [role['name'] for role in verified_data['items']]
        # Get roles displayed on the user management page ui/admin/users but
        # not on the drill down page ui/admin/users/{user_id}
        missing_roles = set(user_roles) - set(verified_user_roles)
        if missing_roles:
            print(f'User {it["userName"]} misses below roles: '
                  f'{", ".join(missing_roles)}')


if __name__ == '__main__':
    run()
