import json

from blackduck import Client
import logging
import os

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)


def get_config():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'config.json')
    with open(config_path) as f:
        config = json.load(f)
    return config


class ScanFinder:
    def __init__(self):
        self.config = get_config()
        self.codelocation_id_dict = self.config['codelocation_id_dict']
        self.bd = Client(
            token=self.config['token'],
            base_url=self.config['url'],
            verify=False  # TLS certificate verification
        )

    def check_if_component_exists_in_scan(self, scan_id, component_name,
                                          component_version_name):
        api = f'/api/scan/{scan_id}/bom-entries'
        params = {
            'limit': 99999,
        }
        r = self.bd.session.get(api, params=params)
        data = json.loads(r.text)
        for it in data['items']:
            if it['componentName'] == component_name and \
                    it['componentVersionName'] == component_version_name:
                return True
        return False

    def _codelocation_id_to_scan_id(self, codelocation_id):
        api = f'api/codelocations/{codelocation_id}/latest-scan-summary'
        r = self.bd.session.get(api)
        data = json.loads(r.text)
        if data['status'] != 'COMPLETE':
            logging.warning(f'扫描{self.codelocation_id_dict[codelocation_id]}'
                            f'最新状态是data["status"]')
        scan_id = data['_meta']['href'].split('/')[-1]
        return scan_id

    def find_scan(self):
        component_name = self.config['component_name']
        component_version_name = self.config['component_version_name']
        # scan_id_dict = self.config['scan_id_dict']
        results = []
        for codelocation_id in self.codelocation_id_dict:
            scan_id = self._codelocation_id_to_scan_id(codelocation_id)
            if self.check_if_component_exists_in_scan(scan_id, component_name,
                                                      component_version_name):
                results.append(codelocation_id)
        if results:
            print(f'如下扫描中包含{component_name} / {component_version_name}:')
            for codelocation_id in results:
                print(f'{self.codelocation_id_dict[codelocation_id]}')
        else:
            print(f'未在找到包含{component_name} / {component_version_name}的扫描')


if __name__ == '__main__':
    # Change the url and token in the config.json file
    sf = ScanFinder()
    sf.find_scan()
