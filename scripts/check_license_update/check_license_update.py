import re
import json
import subprocess
from time import sleep
from urllib.parse import urljoin

from blackduck import Client
import logging
import os

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)


def get_config():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'config.json')
    with open(config_path) as f:
        config = json.load(f)
    return config


class LicenseUpdateChecker:
    def __init__(self):
        self.config = get_config()
        self.bd = Client(
            token=self.config['token'],
            base_url=self.config['url'],
            verify=False  # TLS certificate verification
        )

    def run(self):
        for project in self.bd.get_resource(name='projects'):
            project_name = project['name']
            for version in self.bd.get_resource('versions', project):
                version_name = version['versionName']
                print(f'Checking project/version: {project_name} - {version_name}')
                for bom_components in self.bd.get_resource('components', version):
                    component_name = bom_components['componentName']
                    component_version = bom_components['componentVersionName']
                    if bom_components['licenses'][0]['licenses']:
                        bom_licenses = [lic['licenseDisplay'] for lic in bom_components['licenses'][0]['licenses']]
                    else:
                        bom_licenses = [lic['licenseDisplay'] for lic in bom_components['licenses']]
                    bom_licenses = sorted(bom_licenses)
                    # print(bom_licenses)
                    api = bom_components['componentVersion']
                    r = self.bd.session.get(api)
                    data = json.loads(r.text)
                    com_licenses = [lic['licenseDisplay'] for lic in data['license']['licenses']]
                    com_licenses = sorted(com_licenses)
                    # print(com_licenses)
                    if bom_licenses != com_licenses:
                        print(f'{project_name} - {version_name} : {component_name} - {component_version} : {bom_licenses} - {com_licenses}')


if __name__ == '__main__':
    checker = LicenseUpdateChecker()
    checker.run()