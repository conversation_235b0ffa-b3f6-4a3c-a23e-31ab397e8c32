from blackduck import Client
import logging
import threading
import time
import concurrent.futures
import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# 禁用不安全请求的警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

bd = Client(
    token='N2U2ZWIzYjAtNzg4NS00MWE5LTgyNWUtOTU5MWEwYzA2ZDQ4OjA1MjZjY2U1LTM0ZjgtNGM4Zi04MmY5LTg0ZDczOTY2MWY5Ng==',
    base_url='https://127.0.0.1',
    verify=False  # TLS certificate verification
)

bom_status_url = '/api/projects/91c74a0f-dfe2-4977-b3fc-4652a53dc264/versions/221d264c-a658-45ee-8969-96ea5b3586a9/bom-status'
codelocation_url = '/api/projects/91c74a0f-dfe2-4977-b3fc-4652a53dc264/versions/221d264c-a658-45ee-8969-96ea5b3586a9?codelocationslimit=10000&offset=0'


# 简单的并发请求函数
def send_requests(url, requests_per_second):
    """发送请求但不等待响应"""
    try:
        bd.session.get(url, verify=False, timeout=0.1)
    except:
        # 忽略所有异常
        pass


# 主函数
def main():
    # 配置
    requests_per_second = 15  # 每秒请求数
    test_duration = 60  # 测试持续时间(秒)

    logger.info(f"开始压力测试: 每个URL每秒 {requests_per_second} 请求, 持续 {test_duration} 秒")

    start_time = time.time()
    end_time = start_time + test_duration

    # 使用线程池执行并发请求
    with concurrent.futures.ThreadPoolExecutor(max_workers=requests_per_second * 2) as executor:
        while time.time() < end_time:
            batch_start = time.time()

            # 发送一批BOM状态请求
            for _ in range(requests_per_second):
                executor.submit(send_requests, bom_status_url, requests_per_second)

            # 发送一批代码位置请求
            for _ in range(requests_per_second):
                executor.submit(send_requests, codelocation_url, requests_per_second)

            # 控制请求速率
            batch_duration = time.time() - batch_start
            if batch_duration < 1.0:
                time.sleep(1.0 - batch_duration)

    logger.info(f"压力测试完成，总计约 {requests_per_second * 2 * test_duration} 请求已发送")


if __name__ == "__main__":
    main()