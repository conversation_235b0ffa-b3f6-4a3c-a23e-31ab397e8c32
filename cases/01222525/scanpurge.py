"""
清理时间过久并没有没有map到任何项目的scan
"""

from blackduck import Client
import logging
from datetime import datetime, timedelta

BD_URL = 'https://54.199.136.211/'
BD_TOKEN = 'bdtoken'
DAYS_TO_KEEP = 900   # 默认清理超过900天且未map到任何项目的scan
DRY_RUN = True  # 设置为True时，只打印需要删除的扫描，不实际删除

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token=BD_TOKEN,
    base_url=BD_URL,
    verify=False  # TLS certificate verification
)

def purge_old_codelocations(days_to_keep=DAYS_TO_KEEP, dryrun=False):
    """
    清理超过指定天数且未map到任何项目的scan

    参数:
        days_to_keep: 保留的天数，超过这个天数的未映射扫描将被删除
        dryrun: 如果为True，只打印需要删除的扫描而不实际删除
    """
    if dryrun:
        logging.info(f'模拟清理超过 {days_to_keep} 天的未映射到任何项目的扫描（不会实际删除）...')
    else:
        logging.info(f'正在清理超过 {days_to_keep} 天的未映射到任何项目的扫描...')

    current_date = datetime.now()
    threshold_date = current_date - timedelta(days=days_to_keep)
    logging.info(f'阈值日期: {threshold_date}')

    codelocation_count = 0
    scans_to_delete = []

    # 第一步：找出所有需要删除的扫描
    for codelocation in bd.get_resource('codeLocations'):
        codelocation_count += 1
        if 'mappedProjectVersion' not in codelocation:
            latest_scan_url = get_latest_scan_url(codelocation)
            # 获取codelocation中最新scan的"updatedAt"信息
            latest_scan = bd.get_json(latest_scan_url)
            latest_scan_updated_at = datetime.strptime(latest_scan['updatedAt'], '%Y-%m-%dT%H:%M:%S.%fZ')

            if latest_scan_updated_at < threshold_date:
                codelocation_name = codelocation['name']
                codelocation_url = codelocation['_meta']['href']
                scans_to_delete.append({
                    'name': codelocation_name,
                    'url': codelocation_url,
                    'updated_at': latest_scan_updated_at
                })

    # 第二步：打印需要删除的扫描信息
    logging.info(f'找到 {len(scans_to_delete)} 个需要删除的扫描:')
    for scan in scans_to_delete:
        logging.info(f'- {scan["name"]} (最后更新: {scan["updated_at"]})')

    # 第三步：如果不是dryrun模式，则实际删除
    if not dryrun and scans_to_delete:
        codelocation_deleted = 0
        for scan in scans_to_delete:
            logging.info(f'正在删除扫描: {scan["name"]}')
            response = bd.session.delete(scan['url'])
            if response.status_code == 204:
                logging.info(f'已成功删除扫描: {scan["name"]}')
                codelocation_deleted += 1
            else:
                logging.error(f'删除扫描失败: {scan["name"]}，返回状态码: {response.status_code}')

        logging.info(f'总共检查了 {codelocation_count} 个扫描。')
        logging.info(f'总共删除了 {codelocation_deleted} 个扫描。')
    elif dryrun:
        logging.info(f'总共检查了 {codelocation_count} 个扫描。')
        logging.info('模拟运行完成，未执行实际删除操作。')


def get_latest_scan_url(codelocation_json):
    """
    从codelocation JSON中提取latest-scan的URL
    """
    meta = codelocation_json.get('_meta', {})
    links = meta.get('links', [])

    for link in links:
        if link.get('rel') == 'latest-scan':
            return link.get('href')

    return None

if __name__ == '__main__':
    purge_old_codelocations(DAYS_TO_KEEP, DRY_RUN)


