#!/usr/bin/env python3
import subprocess
import re
import time
import os
import sys
import datetime
from typing import List, Set

# 设置日志文件
LOG_FILE = f"orphan_scans_cleanup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

def log(message: str) -> None:
    """将消息写入日志文件并打印到控制台"""
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(f"{message}\n")
    print(message)

def run_docker_psql_command(sql_command: str) -> str:
    """Execute a PostgreSQL command in the BlackDuck postgres container"""
    cmd = f"""docker exec $(docker ps | grep blackduck-postgres | awk '{{print $1}}') \
             psql -U blackduck -d bds_hub -t -c "{sql_command}" """
    log(f"执行SQL命令: {sql_command[:100]}{'...' if len(sql_command) > 100 else ''}")
    # 使用Python 3.6兼容的参数
    result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
    if result.returncode != 0:
        error_msg = f"执行SQL命令失败: {result.stderr}"
        log(error_msg)
        raise Exception(error_msg)
    return result.stdout

def get_snippet_signature_tables() -> List[str]:
    """Get all snippet signature table names"""
    log("正在获取snippet signature表...")
    sql = """SELECT t.tablename
             FROM pg_catalog.pg_tables AS t
             WHERE lower(t.schemaname) = 'st'
             AND t.tablename LIKE 'snippet_signature_%'"""

    tables = run_docker_psql_command(sql).strip().split('\n')
    filtered_tables = [t.strip() for t in tables if t.strip()]
    log(f"找到 {len(filtered_tables)} 个snippet signature表")
    if filtered_tables:
        sample_tables = ', '.join(filtered_tables[:5])
        if len(filtered_tables) > 5:
            sample_tables += '...'
        log(f"表名示例: {sample_tables}")
    return filtered_tables

def table_name_to_uuid(table_name: str) -> str:
    """Convert table name to UUID format"""
    # Extract UUID part and replace underscores with hyphens
    uuid_match = re.search(r'snippet_signature_(.+)$', table_name)
    if uuid_match:
        return uuid_match.group(1).replace('_', '-')
    log(f"警告: 无法从表名提取UUID: {table_name}")
    return ''

def get_existing_scan_ids() -> Set[str]:
    """Get all existing scan IDs from st.scan_scan table"""
    log("正在获取现有的scan IDs...")
    sql = "SELECT id FROM st.scan_scan"
    scan_ids = run_docker_psql_command(sql).strip().split('\n')
    valid_scan_ids = {sid.strip() for sid in scan_ids if sid.strip()}
    log(f"找到 {len(valid_scan_ids)} 个有效的scan IDs")
    if valid_scan_ids:
        sample_ids = list(valid_scan_ids)[:5]
        sample_text = ', '.join(sample_ids)
        if len(valid_scan_ids) > 5:
            sample_text += '...'
        log(f"ID示例: {sample_text}")
    return valid_scan_ids

def drop_orphan_tables(tables: List[str], existing_scan_ids: Set[str]) -> None:
    """Drop tables whose UUIDs don't exist in scan_scan table"""
    batch_size = 1000
    dropped_count = 0
    skipped_count = 0
    invalid_uuid_count = 0
    total_tables = len(tables)

    log(f"开始处理 {total_tables} 个表，批处理大小为 {batch_size}")

    for i in range(0, len(tables), batch_size):
        batch = tables[i:i + batch_size]
        drop_commands = []
        batch_start_time = time.time()

        log(f"正在处理第 {i//batch_size + 1} 批，包含 {len(batch)} 个表")

        for table in batch:
            uuid = table_name_to_uuid(table)
            if not uuid:
                invalid_uuid_count += 1
                continue

            if uuid not in existing_scan_ids:
                drop_commands.append(f"DROP TABLE IF EXISTS st.{table} CASCADE;")
                dropped_count += 1
            else:
                skipped_count += 1

        if drop_commands:
            log(f"准备删除当前批次中的 {len(drop_commands)} 个表")
            combined_sql = '\n'.join(drop_commands)
            run_docker_psql_command(combined_sql)
        else:
            log("当前批次中没有需要删除的表")

        batch_time = time.time() - batch_start_time
        progress = (i + len(batch)) / total_tables * 100
        log(f"进度: {progress:.1f}% - 批处理用时 {batch_time:.2f}秒 - 本批次删除了 {len(drop_commands)} 个表")
        log(f"统计: 总共删除: {dropped_count}, 跳过: {skipped_count}, 无效UUID: {invalid_uuid_count}")

def main():
    try:
        # 创建日志文件并写入头信息
        with open(LOG_FILE, 'w', encoding='utf-8') as f:
            f.write(f"=== 孤立扫描清理脚本日志 ===\n")
            f.write(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Python版本: {sys.version}\n")
            f.write("===========================\n\n")

        log(f"日志文件已创建: {os.path.abspath(LOG_FILE)}")
        start_time = time.time()

        log("\n=== 阶段1: 获取snippet signature表 ===")
        phase1_start = time.time()
        tables = get_snippet_signature_tables()
        log(f"阶段1完成，用时 {time.time() - phase1_start:.2f} 秒")

        log("\n=== 阶段2: 获取现有scan IDs ===")
        phase2_start = time.time()
        existing_scan_ids = get_existing_scan_ids()
        log(f"阶段2完成，用时 {time.time() - phase2_start:.2f} 秒")

        log("\n=== 阶段3: 删除孤立表 ===")
        phase3_start = time.time()
        drop_orphan_tables(tables, existing_scan_ids)
        log(f"阶段3完成，用时 {time.time() - phase3_start:.2f} 秒")

        elapsed_time = time.time() - start_time
        log(f"\n=== 摘要 ===\n清理成功完成，总用时 {elapsed_time:.1f} 秒")
        log(f"处理了 {len(tables)} 个表，对比了 {len(existing_scan_ids)} 个scan IDs")
        log(f"详细日志已保存到: {os.path.abspath(LOG_FILE)}")

    except Exception as e:
        import traceback
        error_msg = f"清理过程中出错: {str(e)}"
        log(error_msg)
        log("错误堆栈跟踪:")
        log(traceback.format_exc())
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write("\n=== 脚本异常终止 ===\n")
        exit(1)

    # 写入日志文件结束标记
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(f"\n=== 脚本正常结束 ===\n")
        f.write(f"结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

if __name__ == "__main__":
    main()
