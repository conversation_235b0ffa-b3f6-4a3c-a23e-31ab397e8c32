from blackduck import Client
import logging

BD_URL = 'https://54.199.136.211/'
BD_TOKEN = 'bdtoken'

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)

bd = Client(
    token=BD_TOKEN,
    base_url=BD_URL,
    verify=False  # TLS certificate verification
)

# def clone_project_version(project_id, project_version_id, new_project_version_name):
#     url = f"/api/projects/{project_id}/versions"
#     # product_license = product["license"]["name"].replace("'", '"')
#     # distribution = "EXTERNAL" if product_license == "Enflame License" else "OPENSOURCE"
#     # license_url = bdc.get_license(self.hub, product_license)
#     post_data = {"versionUrl": url,
#                  "distribution": 'EXTERNAL',
#                  "releaseComments": "Copyright 2023 Enflame. All Rights Reserved.",
#                  "phase": "DEVELOPMENT",
#                  "versionName": new_project_version_name,
#                  # "license": {"license": license_url, "name": product_license},
#                  "cloneCategories": ["COMPONENT_DATA", "LICENSE_TERM_FULFILLMENT", "CUSTOM_FIELD_DATA", "VULN_DATA",
#                                      "VERSION_SETTINGS"],
#                  "projectCloneRequest": {
#                      "cloningSettings": ["CUSTOM_FIELD_DATA"],
#                      "projectVersionsToClone": [f"https://**************/api/projects/{project_id}/versions/{project_version_id}"],
#                      "projectToClone": f"https://**************/api/projects/{project_id}"
#                  }
#                  }
#     r = bd.session.post(url, json=post_data)
#     return r

def clone_project_version(project_id, project_version_id, new_project_version_name):
    url = f"/api/projects/{project_id}/versions"
    post_data = {
        "versionName" : new_project_version_name,
        "nickname" : "nickname",
        "license" : {
            # "license" : "https://**************/api/licenses/00000000-0010-0000-0000-000000000000"
            "license": f"{BD_URL.rstrip('/')}/api/licenses/00000000-0010-0000-0000-000000000000"
        },
        "phase" : "DEVELOPMENT",
        "distribution" : "EXTERNAL",
        "cloneFromReleaseUrl" : f"{BD_URL.rstrip('/')}/api/projects/{project_id}/versions/{project_version_id}",
    }
    r = bd.session.post(url, json=post_data)
    return r

if __name__ == '__main__':
    project_id = '0a3215b2-41ee-40b5-bc42-76f904f56670'
    project_version_id = '193b1ce1-69cf-4769-87d6-257a8e181204'
    r = clone_project_version(project_id, project_version_id, 'test798')
    print(r.status_code, r.text)


