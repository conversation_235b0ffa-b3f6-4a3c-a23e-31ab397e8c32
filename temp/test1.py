from blackduck import Client
import logging
import requests
import json

# logging.basicConfig(
#     level=logging.INFO,
#     format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
# )
#
# bd = Client(
#     token='	ZjBiYjljZWMtM2NmMS00MmNhLTk0Y2YtMmY0MGYyZjE1M2ZjOmVkYmNhY2E5LWNmNDYtNGFiMS05NGYxLWFhNjI3ZjNiMmUzNg==',
#     base_url='https://zhex.app.blackduck.com/',
#     verify=False  # TLS certificate verification
# )

if __name__ == '__main__':
    """
    curl -H "Authorization: token YjgxMWQ5MjItNTM2My00Yjg1LWJiYmQtNWMwZTYyZWRmNjU5OmY1NGQ5OThkLTJmN2ItNGVkYy1hMzhjLWVjNTQwYmUxMDQ4Mw==" --insecure -X POST https://sup-cn-hub01.dc1.lan/api/tokens/authenticate
    """
    token = 'ZjBiYjljZWMtM2NmMS00MmNhLTk0Y2YtMmY0MGYyZjE1M2ZjOmVkYmNhY2E5LWNmNDYtNGFiMS05NGYxLWFhNjI3ZjNiMmUzNg=='
    base_url = 'https://zhex.app.blackduck.com'
    project_version_id = '8ffd0826-b8a8-49fa-b08e-72a7c772bf74'
    auth_url = "{}/api/tokens/authenticate".format(base_url)
    headers = {
        'Authorization': f'token {token}'
    }
    response = requests.post(auth_url, headers=headers, verify=False)
    data = response.json()
    print(data)
    bearerToken = data['bearerToken']

    url = "{}/api/versions/{}/reports".format(base_url, project_version_id)
    print(url)
    payload = {
        "reportFormat": "CSV",
        "reportType": "VERSION",
        "categories": ["VERSION", "CODE_LOCATIONS", "COMPONENTS", "SECURITY", "FILES"],
        "includeSubprojects": True
    }
    headers = {
        'Authorization': f'Bearer {bearerToken}',  # bearerToken,
        "Content-Type": 'application/vnd.blackducksoftware.report-4+json',
    }
    response = requests.request("POST", url, headers=headers, data=json.dumps(payload), verify=False)
    print(response.text)