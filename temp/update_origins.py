import json
from blackduck.HubRestApi import Hub<PERSON>nst<PERSON>



def update_origins(pid, pvid, cid, cvid, originId, originName, href):

    url = urlbase + "/api/projects/{}/versions/{}/components/{}/versions/{}".format(pid, pvid, cid, cvid)
    custom_headers = {'Content-Type': 'application/vnd.blackducksoftware.internal-1+json'}
    update = '{ \
     "component": "https://10.88.193.93/api/components/' + cid + '", \
     "componentVersion": "https://10.88.193.93/api/components/' + cid + '/versions/' + cvid + '", \
     "origins": [{\
             "externalId": "' + originId + '",\
             "externalNamespace": "' + originName + '",\
             "origin": "' + href + '"\
     }],\
     "usages": ["SOURCE_CODE"],\
     "componentModified": false\
     }'
    json_data = json.loads(update)
    print(json_data)

    response = hub.execute_put(url, data=json_data, custom_headers=custom_headers)
    print(response.status_code)
    print(response.text)

if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub10.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)

    update_origins(
        pid='f88f37e6-04bc-4c8c-bb13-11a4ca76ed04',
        pvid='140924e8-a1ea-4467-9464-2ccb0c37375c',
        cid='eba7e446-3a8f-41b2-a394-cb903c2ff110',
        cvid='10a0ec49-5d85-4837-8fbf-3eeb59e76c34',
        originId='xerces-c-devel/2.7.0-8.el5.centos/x86_64',
        originName='centos',
        href = 'https://sup-cn-hub10.dc1.lan/api/components/eba7e446-3a8f-41b2-a394-cb903c2ff110/versions/10a0ec49-5d85-4837-8fbf-3eeb59e76c34/origins/06b3873b-1aa7-445d-b1f4-4e72fa45e4e0'
    )
