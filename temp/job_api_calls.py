import time

from blackduck.HubRestApi import HubInstance


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub10.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    url_base = hub.get_urlbase()

    for i in range(1000):
        print(f'第{i+1}次调用')
        r1 = hub.execute_get(url_base + '/api/codelocations?offset=0&sort=updatedAt%20DESC&order=desc&includeErrors=true&filter=codeLocationStatus%3Acompleted')
        r2 = hub.execute_get(url_base + '/api/codelocations?offset=0&sort=updatedAt%20DESC&order=desc&includeErrors=true&filter=codeLocationStatus%3Aerror')
        r3 = hub.execute_get(url_base + '/api/codelocations?offset=0&sort=updatedAt%20DESC&order=desc&includeErrors=true&filter=codeLocationStatus%3Ain_progress')
        # print(r1.text)
        # print(r2.text)
        # print(r3.text)
        time.sleep(60)