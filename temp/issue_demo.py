# -*- coding:utf-8 -*-
import json
import sys
from time import sleep

import requests
import pandas as pd

requests.packages.urllib3.disable_warnings()

reload(sys)
sys.setdefaultencoding('utf-8')


class HubApi(object):
    def __init__(self):
        self.config = {}
        with open('HubConf.json', 'r') as f:
            self.config = json.load(f)

    def get_token(self):
        url = self.config["baseurl"] + "/j_spring_security_check"
        session = requests.session()
        credentials = {'j_username': self.config["username"], 'j_password': self.config["password"]}
        response = session.post(url, credentials, verify=False)
        cookie = response.headers['Set-Cookie']
        token = cookie[cookie.index('=') + 1:cookie.index(';')]
        return token


    def update_compontent_usage(self, project_id, project_versions_id, component_id, component_version_id):
        token = self.get_token()
        url = "{0}/api/projects/{1}/versions/{2}/components/{3}/versions/{4}".format(self.config["baseurl"], project_id,
                                                                                     project_versions_id, component_id,
                                                                                     component_version_id)
        headers = {
            "Authorization": "Bearer " + token,
            'Content-Type': 'application/vnd.blackducksoftware.internal-1+json'}
        #payload = json.dumps({'usages': ['SOURCE_CODE']})
        payload = json.dumps({
            'component': self.config["baseurl"] + '/api/components/{0}'.format(component_id),
            'componentVersion': self.config["baseurl"] + '/api/components/{0}/versions/{1}'.format(component_id,
                                                                                     component_version_id),
            'usages': ['SOURCE_CODE'],
            'componentModified': False
        })
        r = requests.put(url, data=payload, headers=headers, verify=False)
        print r.status_code

    def updateCVEStatus(self,project_id, project_versions_id, component_id, component_version_id, originId, CVEId, CVEStatus):
        print CVEId
        token = self.get_token()
        url = "{0}/api/projects/{1}/versions/{2}/components/{3}/versions/{4}/origins/{5}/vulnerabilities/{6}/remediation".format(self.config["baseurl"], project_id,
                                                                                     project_versions_id, component_id,
                                                                                     component_version_id, originId, CVEId)
        headers = {
            "Authorization": "Bearer " + token,
            'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'}

        payload = json.dumps({"comment": "updated comment",
                              "remediationStatus": CVEStatus})

        self.putRestJsonData(url, payload, headers)

    def putRestJsonData(self, url, payload, headers, times=1):
        global req
        try:
            req = requests.put(url, data=payload, headers=headers, verify=False)
            req.raise_for_status()
        except requests.RequestException:
                print u'现在第%d 次重新申请url为%s的缺陷详情' % (times, url)
                sleep(0.5)
                if times < 10:
                    self.putRestJsonData(url, payload, headers, times + 1)
        print req.status_code


    def main(self, project_name, version_name, componentfile):
        project_id = self.get_project_id_by_name(project_name)
        #version_id = self.get_version_id_by_project_id(project_id, version_name)
        version_id ="818047eb-d7db-4557-b2a3-fd077349766f"
        fileData = pd.read_csv(componentfile, encoding='ISO-8859-1', header=1).values
        for rowData in fileData:
            print rowData[0]
            # if rowData[5] == 'yes':
            #     self.update_compontent_usage(project_id, version_id, rowData[7], rowData[8])
            if rowData[5] == 'no':
                self.ignore_compontent(project_id, version_id, rowData[7], rowData[8])


if __name__ == '__main__':
    hubApi = HubApi()
    hubApi.main("ZXSVP V6.20[ZXSVP]", "CGSL V6.02.50CP1", "D:/01gitcode/BlackDuck/resultFile/60250cp.csv")

