"""
依赖模块：pip install -U blackduck
"""
from blackduck.HubRestApi import HubInstance


username = 'sysadmin'
password = 'blackduck'
urlbase = 'https://ec2-52-197-5-213.ap-northeast-1.compute.amazonaws.com'

hub = HubInstance(urlbase, username, password, insecure=True)


def delete_code_locations(code_location_id):
        response = hub.delete_codelocation(code_location_id)
        if response.status_code == 204: # 这里服务器给了204状态成功删除
            print(f"删除扫描代码成功 code location: {code_location_id}")
        else:
            print(f"删除扫描代码失败 code location: {code_location_id}, status code ： {response.status_code}")


if __name__ == '__main__':
    """
    在scan界面点进扫描的详情页面得到的链接最后的id为code_location_id
    例：https://<HUB_URL>/api/codelocations/8071eca8-c67e-48a9-925b-3ace87382a7b
    """
    delete_code_locations('9597a86a-8dad-4845-9ce2-04cba0f17c63')