import json
import warnings

# import requests
from blackduck.HubRestApi import <PERSON><PERSON><PERSON>nstance

username = 'sysadmin'
password = 'blackduck'
urlbase = 'https://cn58sigkb01/'

hub = HubInstance(urlbase, username, password, insecure=True)

url_base = hub.get_urlbase()
headers = hub.get_headers()

def update_compontent_usage(project_id, project_versions_id, component_id,
                            component_version_id):

    url = urlbase + 'api/projects/{}/versions/{}/components/{}/versions/{}'.format(
        project_id, project_versions_id, component_id, component_version_id
    )

    response = hub.execute_get(url)

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.internal-1+json'}

    # response = requests.get(url, headers=headers, verify=False)

    payload = json.loads(response.text)

    del payload['licenseRiskProfile']

    del payload['securityRiskProfile']

    del payload['versionRiskProfile']

    del payload['activityRiskProfile']

    del payload['operationalRiskProfile']

    del payload['_meta']

    payload['usages'] = ["SOURCE_CODE"]

    r = hub.execute_put(url, data=json.dumps(payload), custom_headers=headers)

    if str(r.status_code) == '200':
        print(f'Succeed to update Usage of component version: {component_id} / '
              f'{component_version_id}')
        # print("Succeed to update Usage of component {}".format(component_id))
    else:
        print(f'Failed to update Usage of component version: {component_id} / '
              f'{component_version_id}. Status code: {r.status_code}')
        # print("Fail to update Usage of component {}".format(component_id))


if __name__ == '__main__':
    bom_url = 'https://cn58sigkb01/api/projects/71cb53e5-fb6f-4d4b-a35f-ec1aded543d8/versions/29085a3e-a4b4-450d-9b7f-19980ec7925b/components'
    bom_url_split = bom_url.split('/')
    assert len(bom_url_split) == 9
    project_id = bom_url_split[-4]
    project_version_id = bom_url_split[-2]

    # project_id = 'f88f37e6-04bc-4c8c-bb13-11a4ca76ed04'
    # project_version_id = '140924e8-a1ea-4467-9464-2ccb0c37375c'
    url = url_base + f'api/projects/{project_id}/versions/{project_version_id}/components?limit=99999'

    r = hub.execute_get(url)
    if r.status_code != 200:
        raise Exception('Failed to get the BOM information')
    data = json.loads(r.text)

    for it in data['items']:
        if 'componentVersion' not in it:
            warnings.warn(f'Not component version for it["componentName"]')
            continue
        component_version_url = it['componentVersion']
        component_id = component_version_url.split('/')[-3]
        component_version_id = component_version_url.split('/')[-1]
        update_compontent_usage(project_id, project_version_id,
                                component_id, component_version_id)

    # component_id = '0be5e619-2387-4fe3-915e-8a60146b57dd'
    # component_version_id = 'e16032b0-5802-4426-b1e6-0adfd6088b66'
    # update_compontent_usage(project_id, project_version_id,
    #                         component_id, component_version_id)