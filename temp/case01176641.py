from datetime import datetime

from blackduck import Client
import logging

# logging.basicConfig(
#     level=logging.INFO,
#     format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
# )

bd = Client(
    token='NTU5NTBmNTUtNjdkYi00ZDE4LTliMTAtNzBkMjBjZWQ5MjRjOmM3NmE1YWM0LTFhZDktNDBlZC1hYjE5LWIxNGE4MDU3Mzc0NA==',
    base_url='https://lobsterapj.app.blackduck.com/',
    verify=False  # TLS certificate verification
)



def get_report(project_name, version_name):
    params = {

        'q': [f"name:{project_name}"]

    }

    try:
        projects = [p for p in bd.get_resource('projects', params=params) if p['name'] == project_name]
        assert len(
            projects) == 1, f"There should be one, and only one project named {project_name}. We found {len(projects)}"
        project = projects[0]

    except AssertionError:
        message = f"Project named '{project_name}' not found. Skipping"
        logging.warning(message)
        return None

    params = {
        'q': [f"versionName:{version_name}"]
    }

    try:
        versions = [v for v in bd.get_resource('versions', project, params=params) if
                    v['versionName'] == version_name]
        assert len(
            versions) == 1, f"There should be one, and only one version named {version_name}. We found {len(versions)}"
        version = versions[0]

    except AssertionError:
        message = f"Version name '{version_name}' for project {project_name} was not found, skipping"
        logging.warning(message)
        return None

    logging.debug(f"Found {project['name']}:{version['versionName']}")

    reports_l = ['VERSION', 'CODE_LOCATIONS', 'COMPONENTS',

                 'SECURITY', 'FILES', 'CRYPTO_ALGORITHMS']

    post_data = {

        'categories': reports_l,

        'versionId': version['_meta']['href'].split("/")[-1],

        'reportType': 'VERSION',

        'reportFormat': "CSV"

    }

    version_reports_url = bd.list_resources(version).get('versionReport')

    assert version_reports_url, "Ruh-roh, a version should always have a versionReport resource under it"

    r = bd.session.post(version_reports_url, json=post_data)

    r.raise_for_status()

    location = r.headers.get('Location')

    assert location, "Hmm, this does not make sense. If we successfully created a report then there needs to be a location where we can get it from"

    logging.debug(
        f"Created version details report for project {project_name}, version {version_name} at location {location}")

    report_file_name = "./reports/" + project_name + "-" + \
                       version_name + "-" + datetime.now().strftime('%Y%m%d') + ".zip"


# logging.info("Successfully created reports ({}) for project {} and version {}".format(report_file_name, project_name, version_name))
# self.download_report(location, report_file_name)
# return report_file_name
if __name__ == '__main__':
    project_name = 'proj_snippet_20240825T212954'
    version_name = 'test'

    get_report(project_name, version_name)