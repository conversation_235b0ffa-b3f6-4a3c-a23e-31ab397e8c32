"""
The hub-rest-api-python package is needed. Install it through pip:
pip install blackduck

Below APIs are used:
GET /api/projects
GET /api/projects/{projectId}/versions
GET /api/components/{componentId}/versions/{componentVersionId}/risk-profile
GET /api/projects/{projectId}/versions/{projectVersionId}/components
"""

import json
from blackduck.HubRestApi import HubInstance


def get_all_project_ids():
    """
    Get all project ids on the server
    """
    endpoint = f'/api/projects?limit=999999'
    url = url_base + endpoint
    r = hub.execute_get(url)
    data = json.loads(r.text)
    return [p['_meta']['href'].split('/')[-1] for p in data['items']]


def get_all_project_version_ids(project_id):
    """
    Get all project version ids of a project id
    """
    endpoint = f'/api/projects/{project_id}/versions?limit=999999'
    url = url_base + endpoint
    r = hub.execute_get(url)
    data = json.loads(r.text)
    return [v['_meta']['href'].split('/')[-1] for v in data['items']]


def get_bom_components(project_id, project_version_id):
    """
    Get bom components through the following public API
    GET /api/projects/{projectId}/versions/{projectVersionId}/components
    Note that ignored components are listed as well
    """
    endpoint = f'/api/projects/{project_id}/versions/{project_version_id}' \
               f'/components?limit=999999&filter=bomInclusion:true&' \
               f'filter=bomInclusion:false'
    url = url_base + endpoint
    r = hub.execute_get(url)
    return json.loads(r.text)


def get_component_version_risk_profile(component_id, component_version_id):
    """
    Get risk profile by component id, component version id through the
    following public API
    GET /api/components/{componentId}/versions/{componentVersionId}/risk-profile
    """
    endpoint = f'/api/components/{component_id}/versions/' \
               f'{component_version_id}/risk-profile'
    url = url_base + endpoint
    r = hub.execute_get(url)
    return json.loads(r.text)


def verify(project_id, project_version_id):
    """
    Verify if BOM vulnerabilities are in accord with KB data
    """
    project_name = hub.get_project_by_id(project_id).get('name')
    project_version_name = hub.get_version_by_id(
        project_id, project_version_id).get('versionName')
    bom_components = get_bom_components(project_id, project_version_id)
    print(f'Checking project/version: {project_name}/{project_version_name}, '
          f'{bom_components["totalCount"]} components in total.')
    count_types = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']
    for cp in bom_components['items']:
        # Total Security Risk number displayed on Bom page
        bom_counts = cp['securityRiskProfile']['counts']
        bom_security_risk_num = sum(
            [c['count'] for c in bom_counts if c['countType'] in count_types]
        )

        component_name = cp['componentName']
        component_version_name = cp.get('componentVersionName')
        # No need to check components without versions, 0 vuls in such case
        if not component_version_name:
            continue
        component_id = cp['component'].split('/')[-1]
        component_version_id = cp['componentVersion'].split('/')[-1]
        # Component Version Risk Profile
        component_version_risk_profile = get_component_version_risk_profile(
            component_id, component_version_id
        )
        if 'errorMessage' in component_version_risk_profile and \
                component_version_risk_profile['errorMessage'] == \
                'No data could be found.':
            # No need to check if the component was added from another
            # project/version as that project/version will be checked eventually
            continue
        kb_counts = component_version_risk_profile['riskData']['counts']
        component_security_risk_num = sum(
            [c['count'] for c in kb_counts if c['countType'] in count_types]
        )

        if bom_security_risk_num != component_security_risk_num:
            print(f'Found {component_name}/{component_version_name}: '
                  f'{bom_security_risk_num} vuls on BOM page, '
                  f'{component_security_risk_num} vuls in current KB')
            break


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub01.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    url_base = hub.get_urlbase()
    project_ids = get_all_project_ids()
    for project_id in project_ids:
        project_version_ids = get_all_project_version_ids(project_id)
        for project_version_id in project_version_ids:
            verify(project_id, project_version_id)
