import time

from blackduck.HubRestApi import HubInstance

if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub10.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    for i in range(100):
        r = hub.upload_scan(r'data/46f6cb23-0ffa-4922-9777-b0272441d6d3.bdio')
        print(f'第{i+1}次上传')
        time.sleep(30)

