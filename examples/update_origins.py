"""
Demo for origin update on the BOM page.
1. Env: python 3.x
2. pip install -U blackduck
3. Create a project / version, manually add below component / version:
   GNU C Library / 2.24
4. Update the project id, project version id, component id, component version id
   and origin id accordingly.
5. To get all origins for a components, use below API:
   GET /api/components/{componentId}/versions/{componentVersionId}/origins
   (Check https://<HUB_URL>/api-doc/public.html#_component_origin_endpoints
"""
import json
from blackduck.HubRestApi import HubInstance


def update_origins(project_id, project_version_id, component_id,
                   component_version_id, origin_id):
    url_base = hub.get_urlbase()
    url = f'api/projects/{project_id}/versions/{project_version_id}/' \
          f'components/{component_id}/versions/{component_version_id}'
    url = url_base + url
    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.internal-1+json',
    }
    r = hub.execute_get(url, custom_headers=headers)
    payload = json.loads(r.text)
    # delete unnecessary payload content
    del payload['licenseRiskProfile']
    del payload['securityRiskProfile']
    del payload['versionRiskProfile']
    del payload['activityRiskProfile']
    del payload['operationalRiskProfile']
    del payload['_meta']

    origin = _get_origin_dict(url_base, component_id, component_version_id,
                              origin_id)
    payload['origins'] = [origin]

    print(payload)

    r = hub.execute_put(url, data=payload, custom_headers=headers)

    if r.status_code != 200:
        print(f'Failed to update the origin. {r.status_code} {r.text}')
    else:
        print('Successfully updated the origin')


def _get_origin_dict(url_base, component_id, component_version_id, origin_id):
    origin_url = f'api/components/{component_id}/versions/' \
                 f'{component_version_id}/origins/{origin_id}'
    origin_url = url_base + origin_url
    origin_headers = {
        'Accept': 'application/vnd.blackducksoftware.component-detail-5+json',
    }
    r = hub.execute_get(origin_url, custom_headers=origin_headers)
    if r.status_code != 200:
        raise Exception(f'Cannot get the origin information. The status code '
                        f'is {r.status_code}.')

    origin_data = json.loads(r.text)
    # name = '/'.join(origin_data['externalId'].split('/')[1:])
    # if name == '':
    #     name = origin_data['externalId'].split(':')[-1]
    # if name == '':
    #     raise ValueError(f"Unable to get value of name field in origin dict. "
    #                      "Checked the raw data of origin_data['externalId']:"
    #                      "{origin_data['externalId']}")

    origin = {
        # "name": name,
        "origin": origin_url,
        "externalNamespace": origin_data.get('externalNamespace', 'unknown'),
        # "externalId": origin_data['externalId'],
    }
    external_id = origin_data.get('externalId')
    if external_id is not None:
        origin['externalId'] = external_id

    return origin


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub10.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)

    update_origins(
        project_id='f88f37e6-04bc-4c8c-bb13-11a4ca76ed04',
        project_version_id='140924e8-a1ea-4467-9464-2ccb0c37375c',
        component_id='eba7e446-3a8f-41b2-a394-cb903c2ff110',
        component_version_id='10a0ec49-5d85-4837-8fbf-3eeb59e76c34',
        origin_id='06b3873b-1aa7-445d-b1f4-4e72fa45e4e0'
    )
