"""
Check codelocations status for a give project/version
"""
from blackduck.HubRestApi import <PERSON>b<PERSON><PERSON><PERSON>


def get_codelocations_status(project_id, project_version_id, limit=9999):
    url = '/api/projects/{}/versions/{}/codelocations?limit={}'.format(
        project_id, project_version_id, limit
    )
    url = hub.get_urlbase() + url
    r = hub.execute_get(url)
    if r.status_code == 200:
        return r.text
    else:
        raise Exception('Failed to get codelocations for {} {}, '
                        'status code {}'.format(project_id, project_version_id,
                                                r.status_code))


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub01.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    # 将组件的usage改为Source Code
    print(get_codelocations_status('304f4d2a-b625-4c86-b727-26ee2ba70474',
                                   '3d24c8c5-8042-451f-a6f1-af80ac4ca027'))