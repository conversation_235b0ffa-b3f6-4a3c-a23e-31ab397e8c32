"""
Demo for vulnerability status update
Need to install blackduck module first: pip install blackduck
Run under Python 3.x
"""
from blackduck.HubRestApi import <PERSON><PERSON><PERSON><PERSON><PERSON>

def update_vul_status(project_id, project_version_id, component_id,
                      component_version_id, vul_id, vul_status, comment=None,
                      origin_id=None):
    """
    Supported vulnerability status:
    ['DUPLICATE', 'IGNORED', 'MITIGATED', 'NEEDS_REVIEW', 'NEW', 'PATCHED',
    'REMEDIATION_COMPLETE', 'REMEDIATION_REQUIRED']
    """
    url_base = hub.get_urlbase()
    if origin_id is None:
        api = f'/api/projects/{project_id}/versions/{project_version_id}/' \
              f'components/{component_id}/versions/{component_version_id}/' \
              f'vulnerabilities/{vul_id}/remediation'
        url = url_base + api
    else:
        api = f'/api/projects/{project_id}/versions/{project_version_id}/' \
              f'components/{component_id}/versions/{component_version_id}/' \
              f'origins/{origin_id}/vulnerabilities/{vul_id}/remediation'
        url = url_base + api

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }

    pay_load = {
        'comment': comment,
        "remediationStatus": vul_status
    }

    r = hub.execute_put(url, data=pay_load, custom_headers=headers)
    if r.status_code == 202:
        print('Successfully updated the status of the vulnerability.')
    else:
        print(f'Failed to update the status of the vulnerability: '
              f'{r.status_code}, {r.text}')


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://cn58sigkb01/'

    hub = HubInstance(urlbase, username, password, insecure=True)

    update_vul_status(
        project_id='08660ce0-0d4d-4a82-a5e6-569097b68dad',
        project_version_id='3a3853f4-9a3a-45ad-89c1-38402bbea11b',
        component_id='5e5aadab-ffba-418f-85ee-6af15fe6848d',
        component_version_id='7fd24678-9218-4ca7-acfd-ad78aeef3e77',
        vul_id='CVE-2019-20006',
        vul_status='IGNORED',
        origin_id='bf624777-5d8c-4b69-914a-4e741ac49f6b',
    )


