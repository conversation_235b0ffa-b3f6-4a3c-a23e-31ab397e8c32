"""
使用API修改组件usage的调用例子
PUT /api/projects/{projectId}/versions/{projectVersionId}/components/{componentId}/versions/{componentVersionId}

usage在UI和API调用的对照表
UI                                     API
Source Code                            SOURCE_CODE
Statically Linked                      STATICALLY_LINKED
Dynamically Linked                     DYNAMICALLY_LINKED
Separate Work                          SEPARATE_WORK
Implementation of Standard             IMPLEMENTATION_OF_STANDARD
Development Tool Excluded              DEV_TOOL_EXCLUDED
Prerequisite                           PREREQUISITE
Merely Aggregated                      MERELY_AGGREGATED
"""
from blackduck.HubRestApi import HubInstance
import json

def change_usage(project_id, project_version_id, component_id,
                 component_version_id, usage):
    url_base = hub.get_urlbase()
    url = '/api/projects/{}/versions/{}/components/{}/versions/{}'.format(
        project_id, project_version_id, component_id, component_version_id
    )
    url = url_base + url
    # headers = {
    #     'Content-Type': 'application/vnd.blackducksoftware.internal-1+json'
    # }

    headers = {
        'Accept': 'application/json'
    }

    r = hub.execute_get(url, custom_headers=headers)
    payload = json.loads(r.text)
    # 删除不必要的payload
    del payload['licenseRiskProfile']
    del payload['securityRiskProfile']
    del payload['versionRiskProfile']
    del payload['activityRiskProfile']
    del payload['operationalRiskProfile']
    del payload['_meta']

    payload['usages'] = [usage]

    # payload = {
    #     'component': url_base + f'api/components/{component_id}',
    #     'componentVersion': url_base + f'api/components/{component_id}/versions/{component_version_id}',
    #     'usages': [usage],
    #     'componentModified': False
    # }
    r = hub.execute_put(url, data=payload, custom_headers=headers)
    if r.status_code == 200:
        print('修改组件usage为"{}"成功'.format(usage))
    else:
        print('修改组件usage为"{}"失败, 返回码{}'.format(usage, r.status_code))


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://cn58sigkb01/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    # 将组件的usage改为Source Code
    change_usage(
        project_id='ef9727cd-0baf-4b2f-9004-6194c76d64b3',
        project_version_id='5091ba50-bc69-4f75-9a26-8b3e48640e26',
        component_id='5baf441a-8153-44f7-88d7-726c943b03d0',
        component_version_id='01b6b9c6-3c10-4ca1-86c2-5f8649ebd9c3',
        usage='SOURCE_CODE'
    )
