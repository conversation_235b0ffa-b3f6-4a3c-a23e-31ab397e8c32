import json

from blackduck.HubRestApi import <PERSON><PERSON><PERSON><PERSON><PERSON>


def update_component_version_and_usage(
        project_id, project_version_id, component_id, component_version_id,
        new_component_id=None, new_component_version_id=None,
        new_origin_id=None, usage=None):
    """
    usage的UI和API传参的对应表
    UI                                     API
    Source Code                            SOURCE_CODE
    Statically Linked                      STATICALLY_LINKED
    Dynamically Linked                     DYNAMICALLY_LINKED
    Separate Work                          SEPARATE_WORK
    Implementation of Standard             IMPLEMENTATION_OF_STANDARD
    Development Tool Excluded              DEV_TOOL_EXCLUDED
    Prerequisite                           PREREQUISITE
    Merely Aggregated                      MERELY_AGGREGATED
    """
    if new_component_id is None and new_component_version_id is None and \
        new_origin_id is None and usage is None:
        print('未设置更新组件或usage信息')
        return
    urlbase = hub.get_urlbase()
    api_ep = 'api/projects/{}/versions/{}/components/{}/versions/{}'
    url = urlbase + api_ep.format(
        project_id, project_version_id, component_id, component_version_id
    )

    current_data = json.loads(hub.execute_get(url).text)

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.internal-1+json'
    }

    component_or_version_changed = False

    if new_component_id is None:
        new_component = current_data['component']
    else:
        new_component = urlbase + f'api/components/{new_component_id}'
        component_or_version_changed = True

    if new_component_version_id is None:
        new_component_version = current_data['componentVersion']
    else:
        new_component_version = new_component + \
                                f'/versions/{new_component_version_id}'
        component_or_version_changed = True

    if usage is None:
        usages = current_data['usages']
    else:
        usages = [usage]

    pay_load = {
        'component': new_component,
        'componentVersion': new_component_version,
        'usages': usages,
        'componentModified': False
    }

    if new_origin_id is not None:
        new_origin = new_component_version + f'/origins/{new_origin_id}'
        pay_load['origins'] = [
            {
                'origin': new_origin
            }
        ]
    elif not component_or_version_changed:
        pay_load['origins'] = current_data['origins']

    print(pay_load)
    r = hub.execute_put(url, data=pay_load, custom_headers=headers)
    print(r.status_code, r.text)
    if r.status_code == 200:
        print('修改成功')


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub10.dc1.lan/'
    project_id = '04ae3da7-2bb2-43ad-a8fa-3cf6d3bfd760'
    project_version_id = '1bd740f1-dd9d-4142-a1a9-82f38830a4a3'
    component_id = 'b3729272-1dbb-4af3-adf5-69bf8f6db1cd'
    component_version_id = '16c03c15-9d0b-4be1-b174-7e9efe4bfbae'
    # component_version_id = '770a5676-b81d-407f-b77d-102113ed342e'

    hub = HubInstance(urlbase, username, password, insecure=True)

    new_component_id = None
    # new_component_id = '5ac9cea5-5893-4e80-9d8f-5d98069ef69c'
    new_component_version_id = None
    # new_component_version_id = '16c03c15-9d0b-4be1-b174-7e9efe4bfbae'
    new_origin_id = None
    usage = 'DYNAMICALLY_LINKED'

    update_component_version_and_usage(
        project_id, project_version_id, component_id, component_version_id,
        new_component_id=new_component_id,
        new_component_version_id=new_component_version_id,
        new_origin_id=new_origin_id,
        usage=usage
    )
