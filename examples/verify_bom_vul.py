"""
GET /api/components/{componentId}/vulnerabilities
GET /api/components/{componentId}/versions/{componentVersionId}/vulnerabilities
GET /api/components/{componentId}/versions/{componentVersionId}/origin/{componentVersionOriginId}/vulnerabilities
GET /api/projects/{projectId}/versions/{projectVersionId}/vulnerable-bom-components
GET /api/projects/{projectId}/versions/{projectVersionId}/components
"""

import json
from blackduck.HubRestApi import HubInstance


def get_vulnerable_bom_components(project_id, project_version_id):
    """
    Get vulnerable bom components through the following public API
    GET /api/projects/{projectId}/versions/{projectVersionId}/vulnerable-bom-components
    """
    endpoint = f'/api/projects/{project_id}/versions/{project_version_id}' \
               f'/vulnerable-bom-components?limit=999999'
    url = url_base + endpoint
    r = hub.execute_get(url)
    return json.loads(r.text)


def get_bom_components(project_id, project_version_id):
    """
    Get bom components through the following public API
    GET /api/projects/{projectId}/versions/{projectVersionId}/components
    Note that ignored components are listed as well
    """
    endpoint = f'/api/projects/{project_id}/versions/{project_version_id}' \
               f'/components?limit=999999&filter=bomInclusion:true&' \
               f'filter=bomInclusion:false'
    url = url_base + endpoint
    r = hub.execute_get(url)
    return json.loads(r.text)


def get_component_vules(component_id):
    """
    Get vulnerabilities by component id through the following public API
    GET /api/components/{componentId}/vulnerabilities
    """
    endpoint = f'/api/components/{component_id}/vulnerabilities?limit=999999'
    url = url_base + endpoint
    r = hub.execute_get(url)
    return json.loads(r.text)


def get_component_version_vuls(component_id, version_id):
    """
    Get vulnerabilities by component id and version id through the following
    public API
    GET /api/components/{componentId}/versions/{componentVersionId}/vulnerabilities
    """
    endpoint = f'/api/components/{component_id}/versions/{version_id}' \
               f'/vulnerabilities?limit=999999'
    url = url_base + endpoint
    r = hub.execute_get(url)
    return json.loads(r.text)


def get_component_version_origin_vuls(component_id, version_id, origin_id):
    """
    Get vulnerabilities by component id, version id and origin id through the
    following public API
    GET /api/components/{componentId}/versions/{componentVersionId}/origin/{componentVersionOriginId}/vulnerabilities
    """
    endpoint = f'/api/components/{component_id}/versions/{version_id}/' \
               f'origin/{origin_id}/vulnerabilities?limit=999999'
    url = url_base + endpoint
    headers = hub.get_headers()
    headers['Accept'] = 'application/vnd.blackducksoftware.vulnerability-4+json'
    r = hub.execute_get(url, custom_headers=headers)
    return json.loads(r.text)


def verify(project_id, project_version_id):
    """
    Verify if BOM vulnerabilities are in accord with KB data
    """
    # bom_components = get_bom_components(project_id, project_version_id)
    # print(bom_components)
    # vulnerable_bom_components = get_vulnerable_bom_components(project_id, project_version_id)
    # print(vulnerable_bom_components)
    print(get_component_version_origin_vuls('5ac9cea5-5893-4e80-9d8f-5d98069ef69c',
                                      'd8a3134c-0e5b-4d30-b96e-86bba56f4a4a',
                                      '49c48ee7-9c60-4538-899c-9b48950bf57d'))


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'uqPxUVuCUQdAlnve'
    urlbase = 'https://test-yuan.app.blackduck.com/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    url_base = hub.get_urlbase()

    project_id = '3693a489-43d9-4283-836c-4913c8b7bea3'
    project_version_id = 'ab9afd26-537a-4e05-abcc-7c40fd66c981'
    verify(project_id, project_version_id)