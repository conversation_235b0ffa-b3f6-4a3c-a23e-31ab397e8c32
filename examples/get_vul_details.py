"""
Use below API to get the vulnerability details
GET /api/vulnerabilities/{vulnerabilityId}
Accept: application/vnd.blackducksoftware.vulnerability-4+json
"""

from blackduck.HubRestApi import HubInstance
from urllib.parse import urljoin


def get_vul_details(vul_id):
    url_base = hub.get_urlbase()
    url = f'/api/vulnerabilities/{vul_id}'
    url = urljoin(url_base, url)
    headers = {
        'Accept': 'application/vnd.blackducksoftware.vulnerability-4+json',
    }
    r = hub.execute_get(url, custom_headers=headers)
    return r.text


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://cn58sigkb01/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    print(get_vul_details('BDSA-2016-1597'))
