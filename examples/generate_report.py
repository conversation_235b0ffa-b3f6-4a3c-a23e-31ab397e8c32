"""
Generate report through HUB API (components.csv only)
Please install the blackduck python package first
pip install blackduck
"""
from blackduck.HubRestApi import <PERSON><PERSON><PERSON><PERSON><PERSON>


def create_version_reports(version_id, report_list, format='CSV'):
    """
    Create version report
    """
    url = hub.get_urlbase() + '/api/versions/{}/reports'.format(version_id)
    data = {
        'reportFormat': format,
        'locale': 'en_US',
        'versionId': version_id,
        'categories': list(report_list),
        'reportType': 'VERSION'
    }
    r = hub.execute_post(url, data=data)
    if r.status_code == 201:
        return True
    else:
        raise Exception('Failed to create report with status code '
                        '{}'.format(r.status_code))


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub01.dc1.lan/'

    version_id = 'f5a4a410-e564-4821-890e-c286b3b2fcc6'

    report_list = ['COMPONENTS']
    hub = HubInstance(urlbase, username, password, insecure=True)
    if create_version_reports(version_id, report_list):
        print('Issued a report creation command successfully.')

