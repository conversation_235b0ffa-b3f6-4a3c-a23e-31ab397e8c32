from blackduck.HubRestApi import HubInstance


def update_component_version(project_id, project_version_id, component_id,
                             component_version_id, new_component_id,
                             new_component_version_id, new_origin_id=None):
    urlbase = hub.get_urlbase()
    api_ep = 'api/projects/{}/versions/{}/components/{}/versions/{}'
    url = urlbase + api_ep.format(
        project_id, project_version_id, component_id, component_version_id
    )
    new_component = urlbase + f'api/components/{new_component_id}'
    new_component_version = new_component + f'/versions/{new_component_version_id}'
    if new_origin_id is not None:
        new_origin = new_component_version + f'/origins/{new_origin_id}'

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.internal-1+json'
    }

    pay_load = {
        'component': new_component,
        'componentVersion': new_component_version,
        'usages': [
            'DYNAMICALLY_LINKED'
        ],
        'componentModified': False
    }

    if new_origin_id is not None:
        pay_load['origins'] = [
            {
                'origin': new_origin
            }
        ]

    r = hub.execute_put(url, data=pay_load, custom_headers=headers)
    print(r.status_code)
    print(r.text)
    if r.status_code == 200:
        print('修改成功')


def update_component_usage(project_id, project_version_id, component_id,
                           component_version_id, usage):
    """
    UI                                     API
    Source Code                            SOURCE_CODE
    Statically Linked                      STATICALLY_LINKED
    Dynamically Linked                     DYNAMICALLY_LINKED
    Separate Work                          SEPARATE_WORK
    Implementation of Standard             IMPLEMENTATION_OF_STANDARD
    Development Tool Excluded              DEV_TOOL_EXCLUDED
    Prerequisite                           PREREQUISITE
    Merely Aggregated                      MERELY_AGGREGATED
    """
    urlbase = hub.get_urlbase()
    api_ep = 'api/projects/{}/versions/{}/components/{}/versions/{}'
    url = urlbase + api_ep.format(
        project_id, project_version_id, component_id, component_version_id
    )
    print(url)

    headers = {
        'Content-Type': 'application/vnd.blackducksoftware.internal-1+json'
    }

    pay_load = {
        "component": "https://cn58sigkb01/api/components/b3f578fd-f966-4953-9a8d-6cb28323e7d4",
        "componentVersion": "https://cn58sigkb01/api/components/b3f578fd-f966-4953-9a8d-6cb28323e7d4/versions/ed9ce8d8-bc5d-41d1-90d4-771c3dd27b54",
        'usages': [
            usage
        ],
        'componentModified': False
    }
    r = hub.execute_put(url, data=pay_load, custom_headers=headers)
    print(r.text)
    if r.status_code == 200:
        print('usage修改成功')


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://cn58sigkb01/'
    project_id = 'f5982b8c-5252-4af1-b8cf-a4c7784ca2e0'
    project_version_id = '1cf389e6-4c48-4a18-a99c-e96b05c15978'
    component_id = 'b3f578fd-f966-4953-9a8d-6cb28323e7d4'
    component_version_id = 'ed9ce8d8-bc5d-41d1-90d4-771c3dd27b54'
    new_component_id = 'b3f578fd-f966-4953-9a8d-6cb28323e7d4'
    new_component_version_id = 'ed9ce8d8-bc5d-41d1-90d4-771c3dd27b54'
    new_origin_id = '9d257165-0581-4a69-b3a9-70fa463a3cbd'

    hub = HubInstance(urlbase, username, password, insecure=True)

    # update_component_version(
    #     project_id, project_version_id, component_id, component_version_id,
    #     new_component_id, new_component_version_id, new_origin_id
    # )

    usage = 'STATICALLY_LINKED'
    update_component_usage(project_id, project_version_id, component_id,
                           component_version_id, usage)

