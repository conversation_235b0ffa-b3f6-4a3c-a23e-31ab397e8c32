"""
Use internal API to list and ignore all snippet components
https://sup-cn-hub05.dc1.lan/api/v1/releases/2a66f8b6-e230-49f0-a696-dc551ba540e9/snippet-bom-entries
https://sup-cn-hub05.dc1.lan/api/internal/projects/d84d2d42-439d-4aec-a638-0c190717b0ee/versions/2a66f8b6-e230-49f0-a696-dc551ba540e9/source-bom-entries?offset=0&limit=100&sort=&filter=bomMatchInclusion%3Afalse&filter=bomMatchReviewStatus%3Anot_reviewed&filter=bomMatchType%3Asnippet
"""
import time
import json
from blackduck.HubRestApi import HubInstance


def _set_ignored_to_true(data):
    data = dict(data)
    for it in data['items']:
        for cp in it['fileSnippetBomComponents']:
            cp['ignored'] = True
    return data


def ignore_all_snippet_components(project_id, project_version_id):
    apibase = hub.get_apibase()
    while True:
        r = hub.execute_get(
            f'{apibase}/internal/projects/{project_id}/'
            f'versions/{project_version_id}/source-bom-entries?offset=0'
            f'&limit=100&sort=&filter=bomMatchInclusion%3Afalse&'
            f'filter=bomMatchReviewStatus%3Anot_reviewed&'
            f'filter=bomMatchType%3Asnippet')
        data = json.loads(r.text)
        if data['totalCount'] == 0:
            print('Completed')
            break
        else:
            print(f"{data['totalCount']} left")
        data = _set_ignored_to_true(data)
        r = hub.execute_put(f'{apibase}/v1/releases/{project_version_id}/'
                            f'snippet-bom-entries', data=data['items'])
        if r.status_code != 200:
            print('Error happened')
            break
        time.sleep(2)


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub05.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)
    ignore_all_snippet_components(
        project_id='d84d2d42-439d-4aec-a638-0c190717b0ee',
        project_version_id='2a66f8b6-e230-49f0-a696-dc551ba540e9'
    )

