{"type":"progressStart","data":{"id":0,"total":1965}}
{"type":"progressTick","data":{"id":0,"current":1}}
{"type":"progressTick","data":{"id":0,"current":2}}
{"type":"progressTick","data":{"id":0,"current":3}}
{"type":"progressTick","data":{"id":0,"current":4}}
{"type":"progressTick","data":{"id":0,"current":5}}
{"type":"progressTick","data":{"id":0,"current":6}}
{"type":"progressTick","data":{"id":0,"current":7}}
{"type":"progressTick","data":{"id":0,"current":8}}
{"type":"progressTick","data":{"id":0,"current":9}}
{"type":"progressTick","data":{"id":0,"current":10}}
{"type":"progressTick","data":{"id":0,"current":11}}
{"type":"progressTick","data":{"id":0,"current":12}}
{"type":"progressTick","data":{"id":0,"current":13}}
{"type":"progressTick","data":{"id":0,"current":14}}
{"type":"progressTick","data":{"id":0,"current":15}}
{"type":"progressTick","data":{"id":0,"current":16}}
{"type":"progressTick","data":{"id":0,"current":17}}
{"type":"progressTick","data":{"id":0,"current":18}}
{"type":"progressTick","data":{"id":0,"current":19}}
{"type":"progressTick","data":{"id":0,"current":20}}
{"type":"progressTick","data":{"id":0,"current":21}}
{"type":"progressTick","data":{"id":0,"current":22}}
{"type":"progressTick","data":{"id":0,"current":23}}
{"type":"progressTick","data":{"id":0,"current":24}}
{"type":"progressTick","data":{"id":0,"current":25}}
{"type":"progressTick","data":{"id":0,"current":26}}
{"type":"progressTick","data":{"id":0,"current":27}}
{"type":"progressTick","data":{"id":0,"current":28}}
{"type":"progressTick","data":{"id":0,"current":29}}
{"type":"progressTick","data":{"id":0,"current":30}}
{"type":"progressTick","data":{"id":0,"current":31}}
{"type":"progressTick","data":{"id":0,"current":32}}
{"type":"progressTick","data":{"id":0,"current":33}}
{"type":"progressTick","data":{"id":0,"current":34}}
{"type":"progressTick","data":{"id":0,"current":35}}
{"type":"progressTick","data":{"id":0,"current":36}}
{"type":"progressTick","data":{"id":0,"current":37}}
{"type":"progressTick","data":{"id":0,"current":38}}
{"type":"progressTick","data":{"id":0,"current":39}}
{"type":"progressTick","data":{"id":0,"current":40}}
{"type":"progressTick","data":{"id":0,"current":41}}
{"type":"progressTick","data":{"id":0,"current":42}}
{"type":"progressTick","data":{"id":0,"current":43}}
{"type":"progressTick","data":{"id":0,"current":44}}
{"type":"progressTick","data":{"id":0,"current":45}}
{"type":"progressTick","data":{"id":0,"current":46}}
{"type":"progressTick","data":{"id":0,"current":47}}
{"type":"progressTick","data":{"id":0,"current":48}}
{"type":"progressTick","data":{"id":0,"current":49}}
{"type":"progressTick","data":{"id":0,"current":50}}
{"type":"progressTick","data":{"id":0,"current":51}}
{"type":"progressTick","data":{"id":0,"current":52}}
{"type":"progressTick","data":{"id":0,"current":53}}
{"type":"progressTick","data":{"id":0,"current":54}}
{"type":"progressTick","data":{"id":0,"current":55}}
{"type":"progressTick","data":{"id":0,"current":56}}
{"type":"progressTick","data":{"id":0,"current":57}}
{"type":"progressTick","data":{"id":0,"current":58}}
{"type":"progressTick","data":{"id":0,"current":59}}
{"type":"progressTick","data":{"id":0,"current":60}}
{"type":"progressTick","data":{"id":0,"current":61}}
{"type":"progressTick","data":{"id":0,"current":62}}
{"type":"progressTick","data":{"id":0,"current":63}}
{"type":"progressTick","data":{"id":0,"current":64}}
{"type":"progressTick","data":{"id":0,"current":65}}
{"type":"progressTick","data":{"id":0,"current":66}}
{"type":"progressTick","data":{"id":0,"current":67}}
{"type":"progressTick","data":{"id":0,"current":68}}
{"type":"progressTick","data":{"id":0,"current":69}}
{"type":"progressTick","data":{"id":0,"current":70}}
{"type":"progressTick","data":{"id":0,"current":71}}
{"type":"progressTick","data":{"id":0,"current":72}}
{"type":"progressTick","data":{"id":0,"current":73}}
{"type":"progressTick","data":{"id":0,"current":74}}
{"type":"progressTick","data":{"id":0,"current":75}}
{"type":"progressTick","data":{"id":0,"current":76}}
{"type":"progressTick","data":{"id":0,"current":77}}
{"type":"progressTick","data":{"id":0,"current":78}}
{"type":"progressTick","data":{"id":0,"current":79}}
{"type":"progressTick","data":{"id":0,"current":80}}
{"type":"progressTick","data":{"id":0,"current":81}}
{"type":"progressTick","data":{"id":0,"current":82}}
{"type":"progressTick","data":{"id":0,"current":83}}
{"type":"progressTick","data":{"id":0,"current":84}}
{"type":"progressTick","data":{"id":0,"current":85}}
{"type":"progressTick","data":{"id":0,"current":86}}
{"type":"progressTick","data":{"id":0,"current":87}}
{"type":"progressTick","data":{"id":0,"current":88}}
{"type":"progressTick","data":{"id":0,"current":89}}
{"type":"progressTick","data":{"id":0,"current":90}}
{"type":"progressTick","data":{"id":0,"current":91}}
{"type":"progressTick","data":{"id":0,"current":92}}
{"type":"progressTick","data":{"id":0,"current":93}}
{"type":"progressTick","data":{"id":0,"current":94}}
{"type":"progressTick","data":{"id":0,"current":95}}
{"type":"progressTick","data":{"id":0,"current":96}}
{"type":"progressTick","data":{"id":0,"current":97}}
{"type":"progressTick","data":{"id":0,"current":98}}
{"type":"progressTick","data":{"id":0,"current":99}}
{"type":"progressTick","data":{"id":0,"current":100}}
{"type":"progressTick","data":{"id":0,"current":101}}
{"type":"progressTick","data":{"id":0,"current":102}}
{"type":"progressTick","data":{"id":0,"current":103}}
{"type":"progressTick","data":{"id":0,"current":104}}
{"type":"progressTick","data":{"id":0,"current":105}}
{"type":"progressTick","data":{"id":0,"current":106}}
{"type":"progressTick","data":{"id":0,"current":107}}
{"type":"progressTick","data":{"id":0,"current":108}}
{"type":"progressTick","data":{"id":0,"current":109}}
{"type":"progressTick","data":{"id":0,"current":110}}
{"type":"progressTick","data":{"id":0,"current":111}}
{"type":"progressTick","data":{"id":0,"current":112}}
{"type":"progressTick","data":{"id":0,"current":113}}
{"type":"progressTick","data":{"id":0,"current":114}}
{"type":"progressTick","data":{"id":0,"current":115}}
{"type":"progressTick","data":{"id":0,"current":116}}
{"type":"progressTick","data":{"id":0,"current":117}}
{"type":"progressTick","data":{"id":0,"current":118}}
{"type":"progressTick","data":{"id":0,"current":119}}
{"type":"progressTick","data":{"id":0,"current":120}}
{"type":"progressTick","data":{"id":0,"current":121}}
{"type":"progressTick","data":{"id":0,"current":122}}
{"type":"progressTick","data":{"id":0,"current":123}}
{"type":"progressTick","data":{"id":0,"current":124}}
{"type":"progressTick","data":{"id":0,"current":125}}
{"type":"progressTick","data":{"id":0,"current":126}}
{"type":"progressTick","data":{"id":0,"current":127}}
{"type":"progressTick","data":{"id":0,"current":128}}
{"type":"progressTick","data":{"id":0,"current":129}}
{"type":"progressTick","data":{"id":0,"current":130}}
{"type":"progressTick","data":{"id":0,"current":131}}
{"type":"progressTick","data":{"id":0,"current":132}}
{"type":"progressTick","data":{"id":0,"current":133}}
{"type":"progressTick","data":{"id":0,"current":134}}
{"type":"progressTick","data":{"id":0,"current":135}}
{"type":"progressTick","data":{"id":0,"current":136}}
{"type":"progressTick","data":{"id":0,"current":137}}
{"type":"progressTick","data":{"id":0,"current":138}}
{"type":"progressTick","data":{"id":0,"current":139}}
{"type":"progressTick","data":{"id":0,"current":140}}
{"type":"progressTick","data":{"id":0,"current":141}}
{"type":"progressTick","data":{"id":0,"current":142}}
{"type":"progressTick","data":{"id":0,"current":143}}
{"type":"progressTick","data":{"id":0,"current":144}}
{"type":"progressTick","data":{"id":0,"current":145}}
{"type":"progressTick","data":{"id":0,"current":146}}
{"type":"progressTick","data":{"id":0,"current":147}}
{"type":"progressTick","data":{"id":0,"current":148}}
{"type":"progressTick","data":{"id":0,"current":149}}
{"type":"progressTick","data":{"id":0,"current":150}}
{"type":"progressTick","data":{"id":0,"current":151}}
{"type":"progressTick","data":{"id":0,"current":152}}
{"type":"progressTick","data":{"id":0,"current":153}}
{"type":"progressTick","data":{"id":0,"current":154}}
{"type":"progressTick","data":{"id":0,"current":155}}
{"type":"progressTick","data":{"id":0,"current":156}}
{"type":"progressTick","data":{"id":0,"current":157}}
{"type":"progressTick","data":{"id":0,"current":158}}
{"type":"progressTick","data":{"id":0,"current":159}}
{"type":"progressTick","data":{"id":0,"current":160}}
{"type":"progressTick","data":{"id":0,"current":161}}
{"type":"progressTick","data":{"id":0,"current":162}}
{"type":"progressTick","data":{"id":0,"current":163}}
{"type":"progressTick","data":{"id":0,"current":164}}
{"type":"progressTick","data":{"id":0,"current":165}}
{"type":"progressTick","data":{"id":0,"current":166}}
{"type":"progressTick","data":{"id":0,"current":167}}
{"type":"progressTick","data":{"id":0,"current":168}}
{"type":"progressTick","data":{"id":0,"current":169}}
{"type":"progressTick","data":{"id":0,"current":170}}
{"type":"progressTick","data":{"id":0,"current":171}}
{"type":"progressTick","data":{"id":0,"current":172}}
{"type":"progressTick","data":{"id":0,"current":173}}
{"type":"progressTick","data":{"id":0,"current":174}}
{"type":"progressTick","data":{"id":0,"current":175}}
{"type":"progressTick","data":{"id":0,"current":176}}
{"type":"progressTick","data":{"id":0,"current":177}}
{"type":"progressTick","data":{"id":0,"current":178}}
{"type":"progressTick","data":{"id":0,"current":179}}
{"type":"progressTick","data":{"id":0,"current":180}}
{"type":"progressTick","data":{"id":0,"current":181}}
{"type":"progressTick","data":{"id":0,"current":182}}
{"type":"progressTick","data":{"id":0,"current":183}}
{"type":"progressTick","data":{"id":0,"current":184}}
{"type":"progressTick","data":{"id":0,"current":185}}
{"type":"progressTick","data":{"id":0,"current":186}}
{"type":"progressTick","data":{"id":0,"current":187}}
{"type":"progressTick","data":{"id":0,"current":188}}
{"type":"progressTick","data":{"id":0,"current":189}}
{"type":"progressTick","data":{"id":0,"current":190}}
{"type":"progressTick","data":{"id":0,"current":191}}
{"type":"progressTick","data":{"id":0,"current":192}}
{"type":"progressTick","data":{"id":0,"current":193}}
{"type":"progressTick","data":{"id":0,"current":194}}
{"type":"progressTick","data":{"id":0,"current":195}}
{"type":"progressTick","data":{"id":0,"current":196}}
{"type":"progressTick","data":{"id":0,"current":197}}
{"type":"progressTick","data":{"id":0,"current":198}}
{"type":"progressTick","data":{"id":0,"current":199}}
{"type":"progressTick","data":{"id":0,"current":200}}
{"type":"progressTick","data":{"id":0,"current":201}}
{"type":"progressTick","data":{"id":0,"current":202}}
{"type":"progressTick","data":{"id":0,"current":203}}
{"type":"progressTick","data":{"id":0,"current":204}}
{"type":"progressTick","data":{"id":0,"current":205}}
{"type":"progressTick","data":{"id":0,"current":206}}
{"type":"progressTick","data":{"id":0,"current":207}}
{"type":"progressTick","data":{"id":0,"current":208}}
{"type":"progressTick","data":{"id":0,"current":209}}
{"type":"progressTick","data":{"id":0,"current":210}}
{"type":"progressTick","data":{"id":0,"current":211}}
{"type":"progressTick","data":{"id":0,"current":212}}
{"type":"progressTick","data":{"id":0,"current":213}}
{"type":"progressTick","data":{"id":0,"current":214}}
{"type":"progressTick","data":{"id":0,"current":215}}
{"type":"progressTick","data":{"id":0,"current":216}}
{"type":"progressTick","data":{"id":0,"current":217}}
{"type":"progressTick","data":{"id":0,"current":218}}
{"type":"progressTick","data":{"id":0,"current":219}}
{"type":"progressTick","data":{"id":0,"current":220}}
{"type":"progressTick","data":{"id":0,"current":221}}
{"type":"progressTick","data":{"id":0,"current":222}}
{"type":"progressTick","data":{"id":0,"current":223}}
{"type":"progressTick","data":{"id":0,"current":224}}
{"type":"progressTick","data":{"id":0,"current":225}}
{"type":"progressTick","data":{"id":0,"current":226}}
{"type":"progressTick","data":{"id":0,"current":227}}
{"type":"progressTick","data":{"id":0,"current":228}}
{"type":"progressTick","data":{"id":0,"current":229}}
{"type":"progressTick","data":{"id":0,"current":230}}
{"type":"progressTick","data":{"id":0,"current":231}}
{"type":"progressTick","data":{"id":0,"current":232}}
{"type":"progressTick","data":{"id":0,"current":233}}
{"type":"progressTick","data":{"id":0,"current":234}}
{"type":"progressTick","data":{"id":0,"current":235}}
{"type":"progressTick","data":{"id":0,"current":236}}
{"type":"progressTick","data":{"id":0,"current":237}}
{"type":"progressTick","data":{"id":0,"current":238}}
{"type":"progressTick","data":{"id":0,"current":239}}
{"type":"progressTick","data":{"id":0,"current":240}}
{"type":"progressTick","data":{"id":0,"current":241}}
{"type":"progressTick","data":{"id":0,"current":242}}
{"type":"progressTick","data":{"id":0,"current":243}}
{"type":"progressTick","data":{"id":0,"current":244}}
{"type":"progressTick","data":{"id":0,"current":245}}
{"type":"progressTick","data":{"id":0,"current":246}}
{"type":"progressTick","data":{"id":0,"current":247}}
{"type":"progressTick","data":{"id":0,"current":248}}
{"type":"progressTick","data":{"id":0,"current":249}}
{"type":"progressTick","data":{"id":0,"current":250}}
{"type":"progressTick","data":{"id":0,"current":251}}
{"type":"progressTick","data":{"id":0,"current":252}}
{"type":"progressTick","data":{"id":0,"current":253}}
{"type":"progressTick","data":{"id":0,"current":254}}
{"type":"progressTick","data":{"id":0,"current":255}}
{"type":"progressTick","data":{"id":0,"current":256}}
{"type":"progressTick","data":{"id":0,"current":257}}
{"type":"progressTick","data":{"id":0,"current":258}}
{"type":"progressTick","data":{"id":0,"current":259}}
{"type":"progressTick","data":{"id":0,"current":260}}
{"type":"progressTick","data":{"id":0,"current":261}}
{"type":"progressTick","data":{"id":0,"current":262}}
{"type":"progressTick","data":{"id":0,"current":263}}
{"type":"progressTick","data":{"id":0,"current":264}}
{"type":"progressTick","data":{"id":0,"current":265}}
{"type":"progressTick","data":{"id":0,"current":266}}
{"type":"progressTick","data":{"id":0,"current":267}}
{"type":"progressTick","data":{"id":0,"current":268}}
{"type":"progressTick","data":{"id":0,"current":269}}
{"type":"progressTick","data":{"id":0,"current":270}}
{"type":"progressTick","data":{"id":0,"current":271}}
{"type":"progressTick","data":{"id":0,"current":272}}
{"type":"progressTick","data":{"id":0,"current":273}}
{"type":"progressTick","data":{"id":0,"current":274}}
{"type":"progressTick","data":{"id":0,"current":275}}
{"type":"progressTick","data":{"id":0,"current":276}}
{"type":"progressTick","data":{"id":0,"current":277}}
{"type":"progressTick","data":{"id":0,"current":278}}
{"type":"progressTick","data":{"id":0,"current":279}}
{"type":"progressTick","data":{"id":0,"current":280}}
{"type":"progressTick","data":{"id":0,"current":281}}
{"type":"progressTick","data":{"id":0,"current":282}}
{"type":"progressTick","data":{"id":0,"current":283}}
{"type":"progressTick","data":{"id":0,"current":284}}
{"type":"progressTick","data":{"id":0,"current":285}}
{"type":"progressTick","data":{"id":0,"current":286}}
{"type":"progressTick","data":{"id":0,"current":287}}
{"type":"progressTick","data":{"id":0,"current":288}}
{"type":"progressTick","data":{"id":0,"current":289}}
{"type":"progressTick","data":{"id":0,"current":290}}
{"type":"progressTick","data":{"id":0,"current":291}}
{"type":"progressTick","data":{"id":0,"current":292}}
{"type":"progressTick","data":{"id":0,"current":293}}
{"type":"progressTick","data":{"id":0,"current":294}}
{"type":"progressTick","data":{"id":0,"current":295}}
{"type":"progressTick","data":{"id":0,"current":296}}
{"type":"progressTick","data":{"id":0,"current":297}}
{"type":"progressTick","data":{"id":0,"current":298}}
{"type":"progressTick","data":{"id":0,"current":299}}
{"type":"progressTick","data":{"id":0,"current":300}}
{"type":"progressTick","data":{"id":0,"current":301}}
{"type":"progressTick","data":{"id":0,"current":302}}
{"type":"progressTick","data":{"id":0,"current":303}}
{"type":"progressTick","data":{"id":0,"current":304}}
{"type":"progressTick","data":{"id":0,"current":305}}
{"type":"progressTick","data":{"id":0,"current":306}}
{"type":"progressTick","data":{"id":0,"current":307}}
{"type":"progressTick","data":{"id":0,"current":308}}
{"type":"progressTick","data":{"id":0,"current":309}}
{"type":"progressTick","data":{"id":0,"current":310}}
{"type":"progressTick","data":{"id":0,"current":311}}
{"type":"progressTick","data":{"id":0,"current":312}}
{"type":"progressTick","data":{"id":0,"current":313}}
{"type":"progressTick","data":{"id":0,"current":314}}
{"type":"progressTick","data":{"id":0,"current":315}}
{"type":"progressTick","data":{"id":0,"current":316}}
{"type":"progressTick","data":{"id":0,"current":317}}
{"type":"progressTick","data":{"id":0,"current":318}}
{"type":"progressTick","data":{"id":0,"current":319}}
{"type":"progressTick","data":{"id":0,"current":320}}
{"type":"progressTick","data":{"id":0,"current":321}}
{"type":"progressTick","data":{"id":0,"current":322}}
{"type":"progressTick","data":{"id":0,"current":323}}
{"type":"progressTick","data":{"id":0,"current":324}}
{"type":"progressTick","data":{"id":0,"current":325}}
{"type":"progressTick","data":{"id":0,"current":326}}
{"type":"progressTick","data":{"id":0,"current":327}}
{"type":"progressTick","data":{"id":0,"current":328}}
{"type":"progressTick","data":{"id":0,"current":329}}
{"type":"progressTick","data":{"id":0,"current":330}}
{"type":"progressTick","data":{"id":0,"current":331}}
{"type":"progressTick","data":{"id":0,"current":332}}
{"type":"progressTick","data":{"id":0,"current":333}}
{"type":"progressTick","data":{"id":0,"current":334}}
{"type":"progressTick","data":{"id":0,"current":335}}
{"type":"progressTick","data":{"id":0,"current":336}}
{"type":"progressTick","data":{"id":0,"current":337}}
{"type":"progressTick","data":{"id":0,"current":338}}
{"type":"progressTick","data":{"id":0,"current":339}}
{"type":"progressTick","data":{"id":0,"current":340}}
{"type":"progressTick","data":{"id":0,"current":341}}
{"type":"progressTick","data":{"id":0,"current":342}}
{"type":"progressTick","data":{"id":0,"current":343}}
{"type":"progressTick","data":{"id":0,"current":344}}
{"type":"progressTick","data":{"id":0,"current":345}}
{"type":"progressTick","data":{"id":0,"current":346}}
{"type":"progressTick","data":{"id":0,"current":347}}
{"type":"progressTick","data":{"id":0,"current":348}}
{"type":"progressTick","data":{"id":0,"current":349}}
{"type":"progressTick","data":{"id":0,"current":350}}
{"type":"progressTick","data":{"id":0,"current":351}}
{"type":"progressTick","data":{"id":0,"current":352}}
{"type":"progressTick","data":{"id":0,"current":353}}
{"type":"progressTick","data":{"id":0,"current":354}}
{"type":"progressTick","data":{"id":0,"current":355}}
{"type":"progressTick","data":{"id":0,"current":356}}
{"type":"progressTick","data":{"id":0,"current":357}}
{"type":"progressTick","data":{"id":0,"current":358}}
{"type":"progressTick","data":{"id":0,"current":359}}
{"type":"progressTick","data":{"id":0,"current":360}}
{"type":"progressTick","data":{"id":0,"current":361}}
{"type":"progressTick","data":{"id":0,"current":362}}
{"type":"progressTick","data":{"id":0,"current":363}}
{"type":"progressTick","data":{"id":0,"current":364}}
{"type":"progressTick","data":{"id":0,"current":365}}
{"type":"progressTick","data":{"id":0,"current":366}}
{"type":"progressTick","data":{"id":0,"current":367}}
{"type":"progressTick","data":{"id":0,"current":368}}
{"type":"progressTick","data":{"id":0,"current":369}}
{"type":"progressTick","data":{"id":0,"current":370}}
{"type":"progressTick","data":{"id":0,"current":371}}
{"type":"progressTick","data":{"id":0,"current":372}}
{"type":"progressTick","data":{"id":0,"current":373}}
{"type":"progressTick","data":{"id":0,"current":374}}
{"type":"progressTick","data":{"id":0,"current":375}}
{"type":"progressTick","data":{"id":0,"current":376}}
{"type":"progressTick","data":{"id":0,"current":377}}
{"type":"progressTick","data":{"id":0,"current":378}}
{"type":"progressTick","data":{"id":0,"current":379}}
{"type":"progressTick","data":{"id":0,"current":380}}
{"type":"progressTick","data":{"id":0,"current":381}}
{"type":"progressTick","data":{"id":0,"current":382}}
{"type":"progressTick","data":{"id":0,"current":383}}
{"type":"progressTick","data":{"id":0,"current":384}}
{"type":"progressTick","data":{"id":0,"current":385}}
{"type":"progressTick","data":{"id":0,"current":386}}
{"type":"progressTick","data":{"id":0,"current":387}}
{"type":"progressTick","data":{"id":0,"current":388}}
{"type":"progressTick","data":{"id":0,"current":389}}
{"type":"progressTick","data":{"id":0,"current":390}}
{"type":"progressTick","data":{"id":0,"current":391}}
{"type":"progressTick","data":{"id":0,"current":392}}
{"type":"progressTick","data":{"id":0,"current":393}}
{"type":"progressTick","data":{"id":0,"current":394}}
{"type":"progressTick","data":{"id":0,"current":395}}
{"type":"progressTick","data":{"id":0,"current":396}}
{"type":"progressTick","data":{"id":0,"current":397}}
{"type":"progressTick","data":{"id":0,"current":398}}
{"type":"progressTick","data":{"id":0,"current":399}}
{"type":"progressTick","data":{"id":0,"current":400}}
{"type":"progressTick","data":{"id":0,"current":401}}
{"type":"progressTick","data":{"id":0,"current":402}}
{"type":"progressTick","data":{"id":0,"current":403}}
{"type":"progressTick","data":{"id":0,"current":404}}
{"type":"progressTick","data":{"id":0,"current":405}}
{"type":"progressTick","data":{"id":0,"current":406}}
{"type":"progressTick","data":{"id":0,"current":407}}
{"type":"progressTick","data":{"id":0,"current":408}}
{"type":"progressTick","data":{"id":0,"current":409}}
{"type":"progressTick","data":{"id":0,"current":410}}
{"type":"progressTick","data":{"id":0,"current":411}}
{"type":"progressTick","data":{"id":0,"current":412}}
{"type":"progressTick","data":{"id":0,"current":413}}
{"type":"progressTick","data":{"id":0,"current":414}}
{"type":"progressTick","data":{"id":0,"current":415}}
{"type":"progressTick","data":{"id":0,"current":416}}
{"type":"progressTick","data":{"id":0,"current":417}}
{"type":"progressTick","data":{"id":0,"current":418}}
{"type":"progressTick","data":{"id":0,"current":419}}
{"type":"progressTick","data":{"id":0,"current":420}}
{"type":"progressTick","data":{"id":0,"current":421}}
{"type":"progressTick","data":{"id":0,"current":422}}
{"type":"progressTick","data":{"id":0,"current":423}}
{"type":"progressTick","data":{"id":0,"current":424}}
{"type":"progressTick","data":{"id":0,"current":425}}
{"type":"progressTick","data":{"id":0,"current":426}}
{"type":"progressTick","data":{"id":0,"current":427}}
{"type":"progressTick","data":{"id":0,"current":428}}
{"type":"progressTick","data":{"id":0,"current":429}}
{"type":"progressTick","data":{"id":0,"current":430}}
{"type":"progressTick","data":{"id":0,"current":431}}
{"type":"progressTick","data":{"id":0,"current":432}}
{"type":"progressTick","data":{"id":0,"current":433}}
{"type":"progressTick","data":{"id":0,"current":434}}
{"type":"progressTick","data":{"id":0,"current":435}}
{"type":"progressTick","data":{"id":0,"current":436}}
{"type":"progressTick","data":{"id":0,"current":437}}
{"type":"progressTick","data":{"id":0,"current":438}}
{"type":"progressTick","data":{"id":0,"current":439}}
{"type":"progressTick","data":{"id":0,"current":440}}
{"type":"progressTick","data":{"id":0,"current":441}}
{"type":"progressTick","data":{"id":0,"current":442}}
{"type":"progressTick","data":{"id":0,"current":443}}
{"type":"progressTick","data":{"id":0,"current":444}}
{"type":"progressTick","data":{"id":0,"current":445}}
{"type":"progressTick","data":{"id":0,"current":446}}
{"type":"progressTick","data":{"id":0,"current":447}}
{"type":"progressTick","data":{"id":0,"current":448}}
{"type":"progressTick","data":{"id":0,"current":449}}
{"type":"progressTick","data":{"id":0,"current":450}}
{"type":"progressTick","data":{"id":0,"current":451}}
{"type":"progressTick","data":{"id":0,"current":452}}
{"type":"progressTick","data":{"id":0,"current":453}}
{"type":"progressTick","data":{"id":0,"current":454}}
{"type":"progressTick","data":{"id":0,"current":455}}
{"type":"progressTick","data":{"id":0,"current":456}}
{"type":"progressTick","data":{"id":0,"current":457}}
{"type":"progressTick","data":{"id":0,"current":458}}
{"type":"progressTick","data":{"id":0,"current":459}}
{"type":"progressTick","data":{"id":0,"current":460}}
{"type":"progressTick","data":{"id":0,"current":461}}
{"type":"progressTick","data":{"id":0,"current":462}}
{"type":"progressTick","data":{"id":0,"current":463}}
{"type":"progressTick","data":{"id":0,"current":464}}
{"type":"progressTick","data":{"id":0,"current":465}}
{"type":"progressTick","data":{"id":0,"current":466}}
{"type":"progressTick","data":{"id":0,"current":467}}
{"type":"progressTick","data":{"id":0,"current":468}}
{"type":"progressTick","data":{"id":0,"current":469}}
{"type":"progressTick","data":{"id":0,"current":470}}
{"type":"progressTick","data":{"id":0,"current":471}}
{"type":"progressTick","data":{"id":0,"current":472}}
{"type":"progressTick","data":{"id":0,"current":473}}
{"type":"progressTick","data":{"id":0,"current":474}}
{"type":"progressTick","data":{"id":0,"current":475}}
{"type":"progressTick","data":{"id":0,"current":476}}
{"type":"progressTick","data":{"id":0,"current":477}}
{"type":"progressTick","data":{"id":0,"current":478}}
{"type":"progressTick","data":{"id":0,"current":479}}
{"type":"progressTick","data":{"id":0,"current":480}}
{"type":"progressTick","data":{"id":0,"current":481}}
{"type":"progressTick","data":{"id":0,"current":482}}
{"type":"progressTick","data":{"id":0,"current":483}}
{"type":"progressTick","data":{"id":0,"current":484}}
{"type":"progressTick","data":{"id":0,"current":485}}
{"type":"progressTick","data":{"id":0,"current":486}}
{"type":"progressTick","data":{"id":0,"current":487}}
{"type":"progressTick","data":{"id":0,"current":488}}
{"type":"progressTick","data":{"id":0,"current":489}}
{"type":"progressTick","data":{"id":0,"current":490}}
{"type":"progressTick","data":{"id":0,"current":491}}
{"type":"progressTick","data":{"id":0,"current":492}}
{"type":"progressTick","data":{"id":0,"current":493}}
{"type":"progressTick","data":{"id":0,"current":494}}
{"type":"progressTick","data":{"id":0,"current":495}}
{"type":"progressTick","data":{"id":0,"current":496}}
{"type":"progressTick","data":{"id":0,"current":497}}
{"type":"progressTick","data":{"id":0,"current":498}}
{"type":"progressTick","data":{"id":0,"current":499}}
{"type":"progressTick","data":{"id":0,"current":500}}
{"type":"progressTick","data":{"id":0,"current":501}}
{"type":"progressTick","data":{"id":0,"current":502}}
{"type":"progressTick","data":{"id":0,"current":503}}
{"type":"progressTick","data":{"id":0,"current":504}}
{"type":"progressTick","data":{"id":0,"current":505}}
{"type":"progressTick","data":{"id":0,"current":506}}
{"type":"progressTick","data":{"id":0,"current":507}}
{"type":"progressTick","data":{"id":0,"current":508}}
{"type":"progressTick","data":{"id":0,"current":509}}
{"type":"progressTick","data":{"id":0,"current":510}}
{"type":"progressTick","data":{"id":0,"current":511}}
{"type":"progressTick","data":{"id":0,"current":512}}
{"type":"progressTick","data":{"id":0,"current":513}}
{"type":"progressTick","data":{"id":0,"current":514}}
{"type":"progressTick","data":{"id":0,"current":515}}
{"type":"progressTick","data":{"id":0,"current":516}}
{"type":"progressTick","data":{"id":0,"current":517}}
{"type":"progressTick","data":{"id":0,"current":518}}
{"type":"progressTick","data":{"id":0,"current":519}}
{"type":"progressTick","data":{"id":0,"current":520}}
{"type":"progressTick","data":{"id":0,"current":521}}
{"type":"progressTick","data":{"id":0,"current":522}}
{"type":"progressTick","data":{"id":0,"current":523}}
{"type":"progressTick","data":{"id":0,"current":524}}
{"type":"progressTick","data":{"id":0,"current":525}}
{"type":"progressTick","data":{"id":0,"current":526}}
{"type":"progressTick","data":{"id":0,"current":527}}
{"type":"progressTick","data":{"id":0,"current":528}}
{"type":"progressTick","data":{"id":0,"current":529}}
{"type":"progressTick","data":{"id":0,"current":530}}
{"type":"progressTick","data":{"id":0,"current":531}}
{"type":"progressTick","data":{"id":0,"current":532}}
{"type":"progressTick","data":{"id":0,"current":533}}
{"type":"progressTick","data":{"id":0,"current":534}}
{"type":"progressTick","data":{"id":0,"current":535}}
{"type":"progressTick","data":{"id":0,"current":536}}
{"type":"progressTick","data":{"id":0,"current":537}}
{"type":"progressTick","data":{"id":0,"current":538}}
{"type":"progressTick","data":{"id":0,"current":539}}
{"type":"progressTick","data":{"id":0,"current":540}}
{"type":"progressTick","data":{"id":0,"current":541}}
{"type":"progressTick","data":{"id":0,"current":542}}
{"type":"progressTick","data":{"id":0,"current":543}}
{"type":"progressTick","data":{"id":0,"current":544}}
{"type":"progressTick","data":{"id":0,"current":545}}
{"type":"progressTick","data":{"id":0,"current":546}}
{"type":"progressTick","data":{"id":0,"current":547}}
{"type":"progressTick","data":{"id":0,"current":548}}
{"type":"progressTick","data":{"id":0,"current":549}}
{"type":"progressTick","data":{"id":0,"current":550}}
{"type":"progressTick","data":{"id":0,"current":551}}
{"type":"progressTick","data":{"id":0,"current":552}}
{"type":"progressTick","data":{"id":0,"current":553}}
{"type":"progressTick","data":{"id":0,"current":554}}
{"type":"progressTick","data":{"id":0,"current":555}}
{"type":"progressTick","data":{"id":0,"current":556}}
{"type":"progressTick","data":{"id":0,"current":557}}
{"type":"progressTick","data":{"id":0,"current":558}}
{"type":"progressTick","data":{"id":0,"current":559}}
{"type":"progressTick","data":{"id":0,"current":560}}
{"type":"progressTick","data":{"id":0,"current":561}}
{"type":"progressTick","data":{"id":0,"current":562}}
{"type":"progressTick","data":{"id":0,"current":563}}
{"type":"progressTick","data":{"id":0,"current":564}}
{"type":"progressTick","data":{"id":0,"current":565}}
{"type":"progressTick","data":{"id":0,"current":566}}
{"type":"progressTick","data":{"id":0,"current":567}}
{"type":"progressTick","data":{"id":0,"current":568}}
{"type":"progressTick","data":{"id":0,"current":569}}
{"type":"progressTick","data":{"id":0,"current":570}}
{"type":"progressTick","data":{"id":0,"current":571}}
{"type":"progressTick","data":{"id":0,"current":572}}
{"type":"progressTick","data":{"id":0,"current":573}}
{"type":"progressTick","data":{"id":0,"current":574}}
{"type":"progressTick","data":{"id":0,"current":575}}
{"type":"progressTick","data":{"id":0,"current":576}}
{"type":"progressTick","data":{"id":0,"current":577}}
{"type":"progressTick","data":{"id":0,"current":578}}
{"type":"progressTick","data":{"id":0,"current":579}}
{"type":"progressTick","data":{"id":0,"current":580}}
{"type":"progressTick","data":{"id":0,"current":581}}
{"type":"progressTick","data":{"id":0,"current":582}}
{"type":"progressTick","data":{"id":0,"current":583}}
{"type":"progressTick","data":{"id":0,"current":584}}
{"type":"progressTick","data":{"id":0,"current":585}}
{"type":"progressTick","data":{"id":0,"current":586}}
{"type":"progressTick","data":{"id":0,"current":587}}
{"type":"progressTick","data":{"id":0,"current":588}}
{"type":"progressTick","data":{"id":0,"current":589}}
{"type":"progressTick","data":{"id":0,"current":590}}
{"type":"progressTick","data":{"id":0,"current":591}}
{"type":"progressTick","data":{"id":0,"current":592}}
{"type":"progressTick","data":{"id":0,"current":593}}
{"type":"progressTick","data":{"id":0,"current":594}}
{"type":"progressTick","data":{"id":0,"current":595}}
{"type":"progressTick","data":{"id":0,"current":596}}
{"type":"progressTick","data":{"id":0,"current":597}}
{"type":"progressTick","data":{"id":0,"current":598}}
{"type":"progressTick","data":{"id":0,"current":599}}
{"type":"progressTick","data":{"id":0,"current":600}}
{"type":"progressTick","data":{"id":0,"current":601}}
{"type":"progressTick","data":{"id":0,"current":602}}
{"type":"progressTick","data":{"id":0,"current":603}}
{"type":"progressTick","data":{"id":0,"current":604}}
{"type":"progressTick","data":{"id":0,"current":605}}
{"type":"progressTick","data":{"id":0,"current":606}}
{"type":"progressTick","data":{"id":0,"current":607}}
{"type":"progressTick","data":{"id":0,"current":608}}
{"type":"progressTick","data":{"id":0,"current":609}}
{"type":"progressTick","data":{"id":0,"current":610}}
{"type":"progressTick","data":{"id":0,"current":611}}
{"type":"progressTick","data":{"id":0,"current":612}}
{"type":"progressTick","data":{"id":0,"current":613}}
{"type":"progressTick","data":{"id":0,"current":614}}
{"type":"progressTick","data":{"id":0,"current":615}}
{"type":"progressTick","data":{"id":0,"current":616}}
{"type":"progressTick","data":{"id":0,"current":617}}
{"type":"progressTick","data":{"id":0,"current":618}}
{"type":"progressTick","data":{"id":0,"current":619}}
{"type":"progressTick","data":{"id":0,"current":620}}
{"type":"progressTick","data":{"id":0,"current":621}}
{"type":"progressTick","data":{"id":0,"current":622}}
{"type":"progressTick","data":{"id":0,"current":623}}
{"type":"progressTick","data":{"id":0,"current":624}}
{"type":"progressTick","data":{"id":0,"current":625}}
{"type":"progressTick","data":{"id":0,"current":626}}
{"type":"progressTick","data":{"id":0,"current":627}}
{"type":"progressTick","data":{"id":0,"current":628}}
{"type":"progressTick","data":{"id":0,"current":629}}
{"type":"progressTick","data":{"id":0,"current":630}}
{"type":"progressTick","data":{"id":0,"current":631}}
{"type":"progressTick","data":{"id":0,"current":632}}
{"type":"progressTick","data":{"id":0,"current":633}}
{"type":"progressTick","data":{"id":0,"current":634}}
{"type":"progressTick","data":{"id":0,"current":635}}
{"type":"progressTick","data":{"id":0,"current":636}}
{"type":"progressTick","data":{"id":0,"current":637}}
{"type":"progressTick","data":{"id":0,"current":638}}
{"type":"progressTick","data":{"id":0,"current":639}}
{"type":"progressTick","data":{"id":0,"current":640}}
{"type":"progressTick","data":{"id":0,"current":641}}
{"type":"progressTick","data":{"id":0,"current":642}}
{"type":"progressTick","data":{"id":0,"current":643}}
{"type":"progressTick","data":{"id":0,"current":644}}
{"type":"progressTick","data":{"id":0,"current":645}}
{"type":"progressTick","data":{"id":0,"current":646}}
{"type":"progressTick","data":{"id":0,"current":647}}
{"type":"progressTick","data":{"id":0,"current":648}}
{"type":"progressTick","data":{"id":0,"current":649}}
{"type":"progressTick","data":{"id":0,"current":650}}
{"type":"progressTick","data":{"id":0,"current":651}}
{"type":"progressTick","data":{"id":0,"current":652}}
{"type":"progressTick","data":{"id":0,"current":653}}
{"type":"progressTick","data":{"id":0,"current":654}}
{"type":"progressTick","data":{"id":0,"current":655}}
{"type":"progressTick","data":{"id":0,"current":656}}
{"type":"progressTick","data":{"id":0,"current":657}}
{"type":"progressTick","data":{"id":0,"current":658}}
{"type":"progressTick","data":{"id":0,"current":659}}
{"type":"progressTick","data":{"id":0,"current":660}}
{"type":"progressTick","data":{"id":0,"current":661}}
{"type":"progressTick","data":{"id":0,"current":662}}
{"type":"progressTick","data":{"id":0,"current":663}}
{"type":"progressTick","data":{"id":0,"current":664}}
{"type":"progressTick","data":{"id":0,"current":665}}
{"type":"progressTick","data":{"id":0,"current":666}}
{"type":"progressTick","data":{"id":0,"current":667}}
{"type":"progressTick","data":{"id":0,"current":668}}
{"type":"progressTick","data":{"id":0,"current":669}}
{"type":"progressTick","data":{"id":0,"current":670}}
{"type":"progressTick","data":{"id":0,"current":671}}
{"type":"progressTick","data":{"id":0,"current":672}}
{"type":"progressTick","data":{"id":0,"current":673}}
{"type":"progressTick","data":{"id":0,"current":674}}
{"type":"progressTick","data":{"id":0,"current":675}}
{"type":"progressTick","data":{"id":0,"current":676}}
{"type":"progressTick","data":{"id":0,"current":677}}
{"type":"progressTick","data":{"id":0,"current":678}}
{"type":"progressTick","data":{"id":0,"current":679}}
{"type":"progressTick","data":{"id":0,"current":680}}
{"type":"progressTick","data":{"id":0,"current":681}}
{"type":"progressTick","data":{"id":0,"current":682}}
{"type":"progressTick","data":{"id":0,"current":683}}
{"type":"progressTick","data":{"id":0,"current":684}}
{"type":"progressTick","data":{"id":0,"current":685}}
{"type":"progressTick","data":{"id":0,"current":686}}
{"type":"progressTick","data":{"id":0,"current":687}}
{"type":"progressTick","data":{"id":0,"current":688}}
{"type":"progressTick","data":{"id":0,"current":689}}
{"type":"progressTick","data":{"id":0,"current":690}}
{"type":"progressTick","data":{"id":0,"current":691}}
{"type":"progressTick","data":{"id":0,"current":692}}
{"type":"progressTick","data":{"id":0,"current":693}}
{"type":"progressTick","data":{"id":0,"current":694}}
{"type":"progressTick","data":{"id":0,"current":695}}
{"type":"progressTick","data":{"id":0,"current":696}}
{"type":"progressTick","data":{"id":0,"current":697}}
{"type":"progressTick","data":{"id":0,"current":698}}
{"type":"progressTick","data":{"id":0,"current":699}}
{"type":"progressTick","data":{"id":0,"current":700}}
{"type":"progressTick","data":{"id":0,"current":701}}
{"type":"progressTick","data":{"id":0,"current":702}}
{"type":"progressTick","data":{"id":0,"current":703}}
{"type":"progressTick","data":{"id":0,"current":704}}
{"type":"progressTick","data":{"id":0,"current":705}}
{"type":"progressTick","data":{"id":0,"current":706}}
{"type":"progressTick","data":{"id":0,"current":707}}
{"type":"progressTick","data":{"id":0,"current":708}}
{"type":"progressTick","data":{"id":0,"current":709}}
{"type":"progressTick","data":{"id":0,"current":710}}
{"type":"progressTick","data":{"id":0,"current":711}}
{"type":"progressTick","data":{"id":0,"current":712}}
{"type":"progressTick","data":{"id":0,"current":713}}
{"type":"progressTick","data":{"id":0,"current":714}}
{"type":"progressTick","data":{"id":0,"current":715}}
{"type":"progressTick","data":{"id":0,"current":716}}
{"type":"progressTick","data":{"id":0,"current":717}}
{"type":"progressTick","data":{"id":0,"current":718}}
{"type":"progressTick","data":{"id":0,"current":719}}
{"type":"progressTick","data":{"id":0,"current":720}}
{"type":"progressTick","data":{"id":0,"current":721}}
{"type":"progressTick","data":{"id":0,"current":722}}
{"type":"progressTick","data":{"id":0,"current":723}}
{"type":"progressTick","data":{"id":0,"current":724}}
{"type":"progressTick","data":{"id":0,"current":725}}
{"type":"progressTick","data":{"id":0,"current":726}}
{"type":"progressTick","data":{"id":0,"current":727}}
{"type":"progressTick","data":{"id":0,"current":728}}
{"type":"progressTick","data":{"id":0,"current":729}}
{"type":"progressTick","data":{"id":0,"current":730}}
{"type":"progressTick","data":{"id":0,"current":731}}
{"type":"progressTick","data":{"id":0,"current":732}}
{"type":"progressTick","data":{"id":0,"current":733}}
{"type":"progressTick","data":{"id":0,"current":734}}
{"type":"progressTick","data":{"id":0,"current":735}}
{"type":"progressTick","data":{"id":0,"current":736}}
{"type":"progressTick","data":{"id":0,"current":737}}
{"type":"progressTick","data":{"id":0,"current":738}}
{"type":"progressTick","data":{"id":0,"current":739}}
{"type":"progressTick","data":{"id":0,"current":740}}
{"type":"progressTick","data":{"id":0,"current":741}}
{"type":"progressTick","data":{"id":0,"current":742}}
{"type":"progressTick","data":{"id":0,"current":743}}
{"type":"progressTick","data":{"id":0,"current":744}}
{"type":"progressTick","data":{"id":0,"current":745}}
{"type":"progressTick","data":{"id":0,"current":746}}
{"type":"progressTick","data":{"id":0,"current":747}}
{"type":"progressTick","data":{"id":0,"current":748}}
{"type":"progressTick","data":{"id":0,"current":749}}
{"type":"progressTick","data":{"id":0,"current":750}}
{"type":"progressTick","data":{"id":0,"current":751}}
{"type":"progressTick","data":{"id":0,"current":752}}
{"type":"progressTick","data":{"id":0,"current":753}}
{"type":"progressTick","data":{"id":0,"current":754}}
{"type":"progressTick","data":{"id":0,"current":755}}
{"type":"progressTick","data":{"id":0,"current":756}}
{"type":"progressTick","data":{"id":0,"current":757}}
{"type":"progressTick","data":{"id":0,"current":758}}
{"type":"progressTick","data":{"id":0,"current":759}}
{"type":"progressTick","data":{"id":0,"current":760}}
{"type":"progressTick","data":{"id":0,"current":761}}
{"type":"progressTick","data":{"id":0,"current":762}}
{"type":"progressTick","data":{"id":0,"current":763}}
{"type":"progressTick","data":{"id":0,"current":764}}
{"type":"progressTick","data":{"id":0,"current":765}}
{"type":"progressTick","data":{"id":0,"current":766}}
{"type":"progressTick","data":{"id":0,"current":767}}
{"type":"progressTick","data":{"id":0,"current":768}}
{"type":"progressTick","data":{"id":0,"current":769}}
{"type":"progressTick","data":{"id":0,"current":770}}
{"type":"progressTick","data":{"id":0,"current":771}}
{"type":"progressTick","data":{"id":0,"current":772}}
{"type":"progressTick","data":{"id":0,"current":773}}
{"type":"progressTick","data":{"id":0,"current":774}}
{"type":"progressTick","data":{"id":0,"current":775}}
{"type":"progressTick","data":{"id":0,"current":776}}
{"type":"progressTick","data":{"id":0,"current":777}}
{"type":"progressTick","data":{"id":0,"current":778}}
{"type":"progressTick","data":{"id":0,"current":779}}
{"type":"progressTick","data":{"id":0,"current":780}}
{"type":"progressTick","data":{"id":0,"current":781}}
{"type":"progressTick","data":{"id":0,"current":782}}
{"type":"progressTick","data":{"id":0,"current":783}}
{"type":"progressTick","data":{"id":0,"current":784}}
{"type":"progressTick","data":{"id":0,"current":785}}
{"type":"progressTick","data":{"id":0,"current":786}}
{"type":"progressTick","data":{"id":0,"current":787}}
{"type":"progressTick","data":{"id":0,"current":788}}
{"type":"progressTick","data":{"id":0,"current":789}}
{"type":"progressTick","data":{"id":0,"current":790}}
{"type":"progressTick","data":{"id":0,"current":791}}
{"type":"progressTick","data":{"id":0,"current":792}}
{"type":"progressTick","data":{"id":0,"current":793}}
{"type":"progressTick","data":{"id":0,"current":794}}
{"type":"progressTick","data":{"id":0,"current":795}}
{"type":"progressTick","data":{"id":0,"current":796}}
{"type":"progressTick","data":{"id":0,"current":797}}
{"type":"progressTick","data":{"id":0,"current":798}}
{"type":"progressTick","data":{"id":0,"current":799}}
{"type":"progressTick","data":{"id":0,"current":800}}
{"type":"progressTick","data":{"id":0,"current":801}}
{"type":"progressTick","data":{"id":0,"current":802}}
{"type":"progressTick","data":{"id":0,"current":803}}
{"type":"progressTick","data":{"id":0,"current":804}}
{"type":"progressTick","data":{"id":0,"current":805}}
{"type":"progressTick","data":{"id":0,"current":806}}
{"type":"progressTick","data":{"id":0,"current":807}}
{"type":"progressTick","data":{"id":0,"current":808}}
{"type":"progressTick","data":{"id":0,"current":809}}
{"type":"progressTick","data":{"id":0,"current":810}}
{"type":"progressTick","data":{"id":0,"current":811}}
{"type":"progressTick","data":{"id":0,"current":812}}
{"type":"progressTick","data":{"id":0,"current":813}}
{"type":"progressTick","data":{"id":0,"current":814}}
{"type":"progressTick","data":{"id":0,"current":815}}
{"type":"progressTick","data":{"id":0,"current":816}}
{"type":"progressTick","data":{"id":0,"current":817}}
{"type":"progressTick","data":{"id":0,"current":818}}
{"type":"progressTick","data":{"id":0,"current":819}}
{"type":"progressTick","data":{"id":0,"current":820}}
{"type":"progressTick","data":{"id":0,"current":821}}
{"type":"progressTick","data":{"id":0,"current":822}}
{"type":"progressTick","data":{"id":0,"current":823}}
{"type":"progressTick","data":{"id":0,"current":824}}
{"type":"progressTick","data":{"id":0,"current":825}}
{"type":"progressTick","data":{"id":0,"current":826}}
{"type":"progressTick","data":{"id":0,"current":827}}
{"type":"progressTick","data":{"id":0,"current":828}}
{"type":"progressTick","data":{"id":0,"current":829}}
{"type":"progressTick","data":{"id":0,"current":830}}
{"type":"progressTick","data":{"id":0,"current":831}}
{"type":"progressTick","data":{"id":0,"current":832}}
{"type":"progressTick","data":{"id":0,"current":833}}
{"type":"progressTick","data":{"id":0,"current":834}}
{"type":"progressTick","data":{"id":0,"current":835}}
{"type":"progressTick","data":{"id":0,"current":836}}
{"type":"progressTick","data":{"id":0,"current":837}}
{"type":"progressTick","data":{"id":0,"current":838}}
{"type":"progressTick","data":{"id":0,"current":839}}
{"type":"progressTick","data":{"id":0,"current":840}}
{"type":"progressTick","data":{"id":0,"current":841}}
{"type":"progressTick","data":{"id":0,"current":842}}
{"type":"progressTick","data":{"id":0,"current":843}}
{"type":"progressTick","data":{"id":0,"current":844}}
{"type":"progressTick","data":{"id":0,"current":845}}
{"type":"progressTick","data":{"id":0,"current":846}}
{"type":"progressTick","data":{"id":0,"current":847}}
{"type":"progressTick","data":{"id":0,"current":848}}
{"type":"progressTick","data":{"id":0,"current":849}}
{"type":"progressTick","data":{"id":0,"current":850}}
{"type":"progressTick","data":{"id":0,"current":851}}
{"type":"progressTick","data":{"id":0,"current":852}}
{"type":"progressTick","data":{"id":0,"current":853}}
{"type":"progressTick","data":{"id":0,"current":854}}
{"type":"progressTick","data":{"id":0,"current":855}}
{"type":"progressTick","data":{"id":0,"current":856}}
{"type":"progressTick","data":{"id":0,"current":857}}
{"type":"progressTick","data":{"id":0,"current":858}}
{"type":"progressTick","data":{"id":0,"current":859}}
{"type":"progressTick","data":{"id":0,"current":860}}
{"type":"progressTick","data":{"id":0,"current":861}}
{"type":"progressTick","data":{"id":0,"current":862}}
{"type":"progressTick","data":{"id":0,"current":863}}
{"type":"progressTick","data":{"id":0,"current":864}}
{"type":"progressTick","data":{"id":0,"current":865}}
{"type":"progressTick","data":{"id":0,"current":866}}
{"type":"progressTick","data":{"id":0,"current":867}}
{"type":"progressTick","data":{"id":0,"current":868}}
{"type":"progressTick","data":{"id":0,"current":869}}
{"type":"progressTick","data":{"id":0,"current":870}}
{"type":"progressTick","data":{"id":0,"current":871}}
{"type":"progressTick","data":{"id":0,"current":872}}
{"type":"progressTick","data":{"id":0,"current":873}}
{"type":"progressTick","data":{"id":0,"current":874}}
{"type":"progressTick","data":{"id":0,"current":875}}
{"type":"progressTick","data":{"id":0,"current":876}}
{"type":"progressTick","data":{"id":0,"current":877}}
{"type":"progressTick","data":{"id":0,"current":878}}
{"type":"progressTick","data":{"id":0,"current":879}}
{"type":"progressTick","data":{"id":0,"current":880}}
{"type":"progressTick","data":{"id":0,"current":881}}
{"type":"progressTick","data":{"id":0,"current":882}}
{"type":"progressTick","data":{"id":0,"current":883}}
{"type":"progressTick","data":{"id":0,"current":884}}
{"type":"progressTick","data":{"id":0,"current":885}}
{"type":"progressTick","data":{"id":0,"current":886}}
{"type":"progressTick","data":{"id":0,"current":887}}
{"type":"progressTick","data":{"id":0,"current":888}}
{"type":"progressTick","data":{"id":0,"current":889}}
{"type":"progressTick","data":{"id":0,"current":890}}
{"type":"progressTick","data":{"id":0,"current":891}}
{"type":"progressTick","data":{"id":0,"current":892}}
{"type":"progressTick","data":{"id":0,"current":893}}
{"type":"progressTick","data":{"id":0,"current":894}}
{"type":"progressTick","data":{"id":0,"current":895}}
{"type":"progressTick","data":{"id":0,"current":896}}
{"type":"progressTick","data":{"id":0,"current":897}}
{"type":"progressTick","data":{"id":0,"current":898}}
{"type":"progressTick","data":{"id":0,"current":899}}
{"type":"progressTick","data":{"id":0,"current":900}}
{"type":"progressTick","data":{"id":0,"current":901}}
{"type":"progressTick","data":{"id":0,"current":902}}
{"type":"progressTick","data":{"id":0,"current":903}}
{"type":"progressTick","data":{"id":0,"current":904}}
{"type":"progressTick","data":{"id":0,"current":905}}
{"type":"progressTick","data":{"id":0,"current":906}}
{"type":"progressTick","data":{"id":0,"current":907}}
{"type":"progressTick","data":{"id":0,"current":908}}
{"type":"progressTick","data":{"id":0,"current":909}}
{"type":"progressTick","data":{"id":0,"current":910}}
{"type":"progressTick","data":{"id":0,"current":911}}
{"type":"progressTick","data":{"id":0,"current":912}}
{"type":"progressTick","data":{"id":0,"current":913}}
{"type":"progressTick","data":{"id":0,"current":914}}
{"type":"progressTick","data":{"id":0,"current":915}}
{"type":"progressTick","data":{"id":0,"current":916}}
{"type":"progressTick","data":{"id":0,"current":917}}
{"type":"progressTick","data":{"id":0,"current":918}}
{"type":"progressTick","data":{"id":0,"current":919}}
{"type":"progressTick","data":{"id":0,"current":920}}
{"type":"progressTick","data":{"id":0,"current":921}}
{"type":"progressTick","data":{"id":0,"current":922}}
{"type":"progressTick","data":{"id":0,"current":923}}
{"type":"progressTick","data":{"id":0,"current":924}}
{"type":"progressTick","data":{"id":0,"current":925}}
{"type":"progressTick","data":{"id":0,"current":926}}
{"type":"progressTick","data":{"id":0,"current":927}}
{"type":"progressTick","data":{"id":0,"current":928}}
{"type":"progressTick","data":{"id":0,"current":929}}
{"type":"progressTick","data":{"id":0,"current":930}}
{"type":"progressTick","data":{"id":0,"current":931}}
{"type":"progressTick","data":{"id":0,"current":932}}
{"type":"progressTick","data":{"id":0,"current":933}}
{"type":"progressTick","data":{"id":0,"current":934}}
{"type":"progressTick","data":{"id":0,"current":935}}
{"type":"progressTick","data":{"id":0,"current":936}}
{"type":"progressTick","data":{"id":0,"current":937}}
{"type":"progressTick","data":{"id":0,"current":938}}
{"type":"progressTick","data":{"id":0,"current":939}}
{"type":"progressTick","data":{"id":0,"current":940}}
{"type":"progressTick","data":{"id":0,"current":941}}
{"type":"progressTick","data":{"id":0,"current":942}}
{"type":"progressTick","data":{"id":0,"current":943}}
{"type":"progressTick","data":{"id":0,"current":944}}
{"type":"progressTick","data":{"id":0,"current":945}}
{"type":"progressTick","data":{"id":0,"current":946}}
{"type":"progressTick","data":{"id":0,"current":947}}
{"type":"progressTick","data":{"id":0,"current":948}}
{"type":"progressTick","data":{"id":0,"current":949}}
{"type":"progressTick","data":{"id":0,"current":950}}
{"type":"progressTick","data":{"id":0,"current":951}}
{"type":"progressTick","data":{"id":0,"current":952}}
{"type":"progressTick","data":{"id":0,"current":953}}
{"type":"progressTick","data":{"id":0,"current":954}}
{"type":"progressTick","data":{"id":0,"current":955}}
{"type":"progressTick","data":{"id":0,"current":956}}
{"type":"progressTick","data":{"id":0,"current":957}}
{"type":"progressTick","data":{"id":0,"current":958}}
{"type":"progressTick","data":{"id":0,"current":959}}
{"type":"progressTick","data":{"id":0,"current":960}}
{"type":"progressTick","data":{"id":0,"current":961}}
{"type":"progressTick","data":{"id":0,"current":962}}
{"type":"progressTick","data":{"id":0,"current":963}}
{"type":"progressTick","data":{"id":0,"current":964}}
{"type":"progressTick","data":{"id":0,"current":965}}
{"type":"progressTick","data":{"id":0,"current":966}}
{"type":"progressTick","data":{"id":0,"current":967}}
{"type":"progressTick","data":{"id":0,"current":968}}
{"type":"progressTick","data":{"id":0,"current":969}}
{"type":"progressTick","data":{"id":0,"current":970}}
{"type":"progressTick","data":{"id":0,"current":971}}
{"type":"progressTick","data":{"id":0,"current":972}}
{"type":"progressTick","data":{"id":0,"current":973}}
{"type":"progressTick","data":{"id":0,"current":974}}
{"type":"progressTick","data":{"id":0,"current":975}}
{"type":"progressTick","data":{"id":0,"current":976}}
{"type":"progressTick","data":{"id":0,"current":977}}
{"type":"progressTick","data":{"id":0,"current":978}}
{"type":"progressTick","data":{"id":0,"current":979}}
{"type":"progressTick","data":{"id":0,"current":980}}
{"type":"progressTick","data":{"id":0,"current":981}}
{"type":"progressTick","data":{"id":0,"current":982}}
{"type":"progressTick","data":{"id":0,"current":983}}
{"type":"progressTick","data":{"id":0,"current":984}}
{"type":"progressTick","data":{"id":0,"current":985}}
{"type":"progressTick","data":{"id":0,"current":986}}
{"type":"progressTick","data":{"id":0,"current":987}}
{"type":"progressTick","data":{"id":0,"current":988}}
{"type":"progressTick","data":{"id":0,"current":989}}
{"type":"progressTick","data":{"id":0,"current":990}}
{"type":"progressTick","data":{"id":0,"current":991}}
{"type":"progressTick","data":{"id":0,"current":992}}
{"type":"progressTick","data":{"id":0,"current":993}}
{"type":"progressTick","data":{"id":0,"current":994}}
{"type":"progressTick","data":{"id":0,"current":995}}
{"type":"progressTick","data":{"id":0,"current":996}}
{"type":"progressTick","data":{"id":0,"current":997}}
{"type":"progressTick","data":{"id":0,"current":998}}
{"type":"progressTick","data":{"id":0,"current":999}}
{"type":"progressTick","data":{"id":0,"current":1000}}
{"type":"progressTick","data":{"id":0,"current":1001}}
{"type":"progressTick","data":{"id":0,"current":1002}}
{"type":"progressTick","data":{"id":0,"current":1003}}
{"type":"progressTick","data":{"id":0,"current":1004}}
{"type":"progressTick","data":{"id":0,"current":1005}}
{"type":"progressTick","data":{"id":0,"current":1006}}
{"type":"progressTick","data":{"id":0,"current":1007}}
{"type":"progressTick","data":{"id":0,"current":1008}}
{"type":"progressTick","data":{"id":0,"current":1009}}
{"type":"progressTick","data":{"id":0,"current":1010}}
{"type":"progressTick","data":{"id":0,"current":1011}}
{"type":"progressTick","data":{"id":0,"current":1012}}
{"type":"progressTick","data":{"id":0,"current":1013}}
{"type":"progressTick","data":{"id":0,"current":1014}}
{"type":"progressTick","data":{"id":0,"current":1015}}
{"type":"progressTick","data":{"id":0,"current":1016}}
{"type":"progressTick","data":{"id":0,"current":1017}}
{"type":"progressTick","data":{"id":0,"current":1018}}
{"type":"progressTick","data":{"id":0,"current":1019}}
{"type":"progressTick","data":{"id":0,"current":1020}}
{"type":"progressTick","data":{"id":0,"current":1021}}
{"type":"progressTick","data":{"id":0,"current":1022}}
{"type":"progressTick","data":{"id":0,"current":1023}}
{"type":"progressTick","data":{"id":0,"current":1024}}
{"type":"progressTick","data":{"id":0,"current":1025}}
{"type":"progressTick","data":{"id":0,"current":1026}}
{"type":"progressTick","data":{"id":0,"current":1027}}
{"type":"progressTick","data":{"id":0,"current":1028}}
{"type":"progressTick","data":{"id":0,"current":1029}}
{"type":"progressTick","data":{"id":0,"current":1030}}
{"type":"progressTick","data":{"id":0,"current":1031}}
{"type":"progressTick","data":{"id":0,"current":1032}}
{"type":"progressTick","data":{"id":0,"current":1033}}
{"type":"progressTick","data":{"id":0,"current":1034}}
{"type":"progressTick","data":{"id":0,"current":1035}}
{"type":"progressTick","data":{"id":0,"current":1036}}
{"type":"progressTick","data":{"id":0,"current":1037}}
{"type":"progressTick","data":{"id":0,"current":1038}}
{"type":"progressTick","data":{"id":0,"current":1039}}
{"type":"progressTick","data":{"id":0,"current":1040}}
{"type":"progressTick","data":{"id":0,"current":1041}}
{"type":"progressTick","data":{"id":0,"current":1042}}
{"type":"progressTick","data":{"id":0,"current":1043}}
{"type":"progressTick","data":{"id":0,"current":1044}}
{"type":"progressTick","data":{"id":0,"current":1045}}
{"type":"progressTick","data":{"id":0,"current":1046}}
{"type":"progressTick","data":{"id":0,"current":1047}}
{"type":"progressTick","data":{"id":0,"current":1048}}
{"type":"progressTick","data":{"id":0,"current":1049}}
{"type":"progressTick","data":{"id":0,"current":1050}}
{"type":"progressTick","data":{"id":0,"current":1051}}
{"type":"progressTick","data":{"id":0,"current":1052}}
{"type":"progressTick","data":{"id":0,"current":1053}}
{"type":"progressTick","data":{"id":0,"current":1054}}
{"type":"progressTick","data":{"id":0,"current":1055}}
{"type":"progressTick","data":{"id":0,"current":1056}}
{"type":"progressTick","data":{"id":0,"current":1057}}
{"type":"progressTick","data":{"id":0,"current":1058}}
{"type":"progressTick","data":{"id":0,"current":1059}}
{"type":"progressTick","data":{"id":0,"current":1060}}
{"type":"progressTick","data":{"id":0,"current":1061}}
{"type":"progressTick","data":{"id":0,"current":1062}}
{"type":"progressTick","data":{"id":0,"current":1063}}
{"type":"progressTick","data":{"id":0,"current":1064}}
{"type":"progressTick","data":{"id":0,"current":1065}}
{"type":"progressTick","data":{"id":0,"current":1066}}
{"type":"progressTick","data":{"id":0,"current":1067}}
{"type":"progressTick","data":{"id":0,"current":1068}}
{"type":"progressTick","data":{"id":0,"current":1069}}
{"type":"progressTick","data":{"id":0,"current":1070}}
{"type":"progressTick","data":{"id":0,"current":1071}}
{"type":"progressTick","data":{"id":0,"current":1072}}
{"type":"progressTick","data":{"id":0,"current":1073}}
{"type":"progressTick","data":{"id":0,"current":1074}}
{"type":"progressTick","data":{"id":0,"current":1075}}
{"type":"progressTick","data":{"id":0,"current":1076}}
{"type":"progressTick","data":{"id":0,"current":1077}}
{"type":"progressTick","data":{"id":0,"current":1078}}
{"type":"progressTick","data":{"id":0,"current":1079}}
{"type":"progressTick","data":{"id":0,"current":1080}}
{"type":"progressTick","data":{"id":0,"current":1081}}
{"type":"progressTick","data":{"id":0,"current":1082}}
{"type":"progressTick","data":{"id":0,"current":1083}}
{"type":"progressTick","data":{"id":0,"current":1084}}
{"type":"progressTick","data":{"id":0,"current":1085}}
{"type":"progressTick","data":{"id":0,"current":1086}}
{"type":"progressTick","data":{"id":0,"current":1087}}
{"type":"progressTick","data":{"id":0,"current":1088}}
{"type":"progressTick","data":{"id":0,"current":1089}}
{"type":"progressTick","data":{"id":0,"current":1090}}
{"type":"progressTick","data":{"id":0,"current":1091}}
{"type":"progressTick","data":{"id":0,"current":1092}}
{"type":"progressTick","data":{"id":0,"current":1093}}
{"type":"progressTick","data":{"id":0,"current":1094}}
{"type":"progressTick","data":{"id":0,"current":1095}}
{"type":"progressTick","data":{"id":0,"current":1096}}
{"type":"progressTick","data":{"id":0,"current":1097}}
{"type":"progressTick","data":{"id":0,"current":1098}}
{"type":"progressTick","data":{"id":0,"current":1099}}
{"type":"progressTick","data":{"id":0,"current":1100}}
{"type":"progressTick","data":{"id":0,"current":1101}}
{"type":"progressTick","data":{"id":0,"current":1102}}
{"type":"progressTick","data":{"id":0,"current":1103}}
{"type":"progressTick","data":{"id":0,"current":1104}}
{"type":"progressTick","data":{"id":0,"current":1105}}
{"type":"progressTick","data":{"id":0,"current":1106}}
{"type":"progressTick","data":{"id":0,"current":1107}}
{"type":"progressTick","data":{"id":0,"current":1108}}
{"type":"progressTick","data":{"id":0,"current":1109}}
{"type":"progressTick","data":{"id":0,"current":1110}}
{"type":"progressTick","data":{"id":0,"current":1111}}
{"type":"progressTick","data":{"id":0,"current":1112}}
{"type":"progressTick","data":{"id":0,"current":1113}}
{"type":"progressTick","data":{"id":0,"current":1114}}
{"type":"progressTick","data":{"id":0,"current":1115}}
{"type":"progressTick","data":{"id":0,"current":1116}}
{"type":"progressTick","data":{"id":0,"current":1117}}
{"type":"progressTick","data":{"id":0,"current":1118}}
{"type":"progressTick","data":{"id":0,"current":1119}}
{"type":"progressTick","data":{"id":0,"current":1120}}
{"type":"progressTick","data":{"id":0,"current":1121}}
{"type":"progressTick","data":{"id":0,"current":1122}}
{"type":"progressTick","data":{"id":0,"current":1123}}
{"type":"progressTick","data":{"id":0,"current":1124}}
{"type":"progressTick","data":{"id":0,"current":1125}}
{"type":"progressTick","data":{"id":0,"current":1126}}
{"type":"progressTick","data":{"id":0,"current":1127}}
{"type":"progressTick","data":{"id":0,"current":1128}}
{"type":"progressTick","data":{"id":0,"current":1129}}
{"type":"progressTick","data":{"id":0,"current":1130}}
{"type":"progressTick","data":{"id":0,"current":1131}}
{"type":"progressTick","data":{"id":0,"current":1132}}
{"type":"progressTick","data":{"id":0,"current":1133}}
{"type":"progressTick","data":{"id":0,"current":1134}}
{"type":"progressTick","data":{"id":0,"current":1135}}
{"type":"progressTick","data":{"id":0,"current":1136}}
{"type":"progressTick","data":{"id":0,"current":1137}}
{"type":"progressTick","data":{"id":0,"current":1138}}
{"type":"progressTick","data":{"id":0,"current":1139}}
{"type":"progressTick","data":{"id":0,"current":1140}}
{"type":"progressTick","data":{"id":0,"current":1141}}
{"type":"progressTick","data":{"id":0,"current":1142}}
{"type":"progressTick","data":{"id":0,"current":1143}}
{"type":"progressTick","data":{"id":0,"current":1144}}
{"type":"progressTick","data":{"id":0,"current":1145}}
{"type":"progressTick","data":{"id":0,"current":1146}}
{"type":"progressTick","data":{"id":0,"current":1147}}
{"type":"progressTick","data":{"id":0,"current":1148}}
{"type":"progressTick","data":{"id":0,"current":1149}}
{"type":"progressTick","data":{"id":0,"current":1150}}
{"type":"progressTick","data":{"id":0,"current":1151}}
{"type":"progressTick","data":{"id":0,"current":1152}}
{"type":"progressTick","data":{"id":0,"current":1153}}
{"type":"progressTick","data":{"id":0,"current":1154}}
{"type":"progressTick","data":{"id":0,"current":1155}}
{"type":"progressTick","data":{"id":0,"current":1156}}
{"type":"progressTick","data":{"id":0,"current":1157}}
{"type":"progressTick","data":{"id":0,"current":1158}}
{"type":"progressTick","data":{"id":0,"current":1159}}
{"type":"progressTick","data":{"id":0,"current":1160}}
{"type":"progressTick","data":{"id":0,"current":1161}}
{"type":"progressTick","data":{"id":0,"current":1162}}
{"type":"progressTick","data":{"id":0,"current":1163}}
{"type":"progressTick","data":{"id":0,"current":1164}}
{"type":"progressTick","data":{"id":0,"current":1165}}
{"type":"progressTick","data":{"id":0,"current":1166}}
{"type":"progressTick","data":{"id":0,"current":1167}}
{"type":"progressTick","data":{"id":0,"current":1168}}
{"type":"progressTick","data":{"id":0,"current":1169}}
{"type":"progressTick","data":{"id":0,"current":1170}}
{"type":"progressTick","data":{"id":0,"current":1171}}
{"type":"progressTick","data":{"id":0,"current":1172}}
{"type":"progressTick","data":{"id":0,"current":1173}}
{"type":"progressTick","data":{"id":0,"current":1174}}
{"type":"progressTick","data":{"id":0,"current":1175}}
{"type":"progressTick","data":{"id":0,"current":1176}}
{"type":"progressTick","data":{"id":0,"current":1177}}
{"type":"progressTick","data":{"id":0,"current":1178}}
{"type":"progressTick","data":{"id":0,"current":1179}}
{"type":"progressTick","data":{"id":0,"current":1180}}
{"type":"progressTick","data":{"id":0,"current":1181}}
{"type":"progressTick","data":{"id":0,"current":1182}}
{"type":"progressTick","data":{"id":0,"current":1183}}
{"type":"progressTick","data":{"id":0,"current":1184}}
{"type":"progressTick","data":{"id":0,"current":1185}}
{"type":"progressTick","data":{"id":0,"current":1186}}
{"type":"progressTick","data":{"id":0,"current":1187}}
{"type":"progressTick","data":{"id":0,"current":1188}}
{"type":"progressTick","data":{"id":0,"current":1189}}
{"type":"progressTick","data":{"id":0,"current":1190}}
{"type":"progressTick","data":{"id":0,"current":1191}}
{"type":"progressTick","data":{"id":0,"current":1192}}
{"type":"progressTick","data":{"id":0,"current":1193}}
{"type":"progressTick","data":{"id":0,"current":1194}}
{"type":"progressTick","data":{"id":0,"current":1195}}
{"type":"progressTick","data":{"id":0,"current":1196}}
{"type":"progressTick","data":{"id":0,"current":1197}}
{"type":"progressTick","data":{"id":0,"current":1198}}
{"type":"progressTick","data":{"id":0,"current":1199}}
{"type":"progressTick","data":{"id":0,"current":1200}}
{"type":"progressTick","data":{"id":0,"current":1201}}
{"type":"progressTick","data":{"id":0,"current":1202}}
{"type":"progressTick","data":{"id":0,"current":1203}}
{"type":"progressTick","data":{"id":0,"current":1204}}
{"type":"progressTick","data":{"id":0,"current":1205}}
{"type":"progressTick","data":{"id":0,"current":1206}}
{"type":"progressTick","data":{"id":0,"current":1207}}
{"type":"progressTick","data":{"id":0,"current":1208}}
{"type":"progressTick","data":{"id":0,"current":1209}}
{"type":"progressTick","data":{"id":0,"current":1210}}
{"type":"progressTick","data":{"id":0,"current":1211}}
{"type":"progressTick","data":{"id":0,"current":1212}}
{"type":"progressTick","data":{"id":0,"current":1213}}
{"type":"progressTick","data":{"id":0,"current":1214}}
{"type":"progressTick","data":{"id":0,"current":1215}}
{"type":"progressTick","data":{"id":0,"current":1216}}
{"type":"progressTick","data":{"id":0,"current":1217}}
{"type":"progressTick","data":{"id":0,"current":1218}}
{"type":"progressTick","data":{"id":0,"current":1219}}
{"type":"progressTick","data":{"id":0,"current":1220}}
{"type":"progressTick","data":{"id":0,"current":1221}}
{"type":"progressTick","data":{"id":0,"current":1222}}
{"type":"progressTick","data":{"id":0,"current":1223}}
{"type":"progressTick","data":{"id":0,"current":1224}}
{"type":"progressTick","data":{"id":0,"current":1225}}
{"type":"progressTick","data":{"id":0,"current":1226}}
{"type":"progressTick","data":{"id":0,"current":1227}}
{"type":"progressTick","data":{"id":0,"current":1228}}
{"type":"progressTick","data":{"id":0,"current":1229}}
{"type":"progressTick","data":{"id":0,"current":1230}}
{"type":"progressTick","data":{"id":0,"current":1231}}
{"type":"progressTick","data":{"id":0,"current":1232}}
{"type":"progressTick","data":{"id":0,"current":1233}}
{"type":"progressTick","data":{"id":0,"current":1234}}
{"type":"progressTick","data":{"id":0,"current":1235}}
{"type":"progressTick","data":{"id":0,"current":1236}}
{"type":"progressTick","data":{"id":0,"current":1237}}
{"type":"progressTick","data":{"id":0,"current":1238}}
{"type":"progressTick","data":{"id":0,"current":1239}}
{"type":"progressTick","data":{"id":0,"current":1240}}
{"type":"progressTick","data":{"id":0,"current":1241}}
{"type":"progressTick","data":{"id":0,"current":1242}}
{"type":"progressTick","data":{"id":0,"current":1243}}
{"type":"progressTick","data":{"id":0,"current":1244}}
{"type":"progressTick","data":{"id":0,"current":1245}}
{"type":"progressTick","data":{"id":0,"current":1246}}
{"type":"progressTick","data":{"id":0,"current":1247}}
{"type":"progressTick","data":{"id":0,"current":1248}}
{"type":"progressTick","data":{"id":0,"current":1249}}
{"type":"progressTick","data":{"id":0,"current":1250}}
{"type":"progressTick","data":{"id":0,"current":1251}}
{"type":"progressTick","data":{"id":0,"current":1252}}
{"type":"progressTick","data":{"id":0,"current":1253}}
{"type":"progressTick","data":{"id":0,"current":1254}}
{"type":"progressTick","data":{"id":0,"current":1255}}
{"type":"progressTick","data":{"id":0,"current":1256}}
{"type":"progressTick","data":{"id":0,"current":1257}}
{"type":"progressTick","data":{"id":0,"current":1258}}
{"type":"progressTick","data":{"id":0,"current":1259}}
{"type":"progressTick","data":{"id":0,"current":1260}}
{"type":"progressTick","data":{"id":0,"current":1261}}
{"type":"progressTick","data":{"id":0,"current":1262}}
{"type":"progressTick","data":{"id":0,"current":1263}}
{"type":"progressTick","data":{"id":0,"current":1264}}
{"type":"progressTick","data":{"id":0,"current":1265}}
{"type":"progressTick","data":{"id":0,"current":1266}}
{"type":"progressTick","data":{"id":0,"current":1267}}
{"type":"progressTick","data":{"id":0,"current":1268}}
{"type":"progressTick","data":{"id":0,"current":1269}}
{"type":"progressTick","data":{"id":0,"current":1270}}
{"type":"progressTick","data":{"id":0,"current":1271}}
{"type":"progressTick","data":{"id":0,"current":1272}}
{"type":"progressTick","data":{"id":0,"current":1273}}
{"type":"progressTick","data":{"id":0,"current":1274}}
{"type":"progressTick","data":{"id":0,"current":1275}}
{"type":"progressTick","data":{"id":0,"current":1276}}
{"type":"progressTick","data":{"id":0,"current":1277}}
{"type":"progressTick","data":{"id":0,"current":1278}}
{"type":"progressTick","data":{"id":0,"current":1279}}
{"type":"progressTick","data":{"id":0,"current":1280}}
{"type":"progressTick","data":{"id":0,"current":1281}}
{"type":"progressTick","data":{"id":0,"current":1282}}
{"type":"progressTick","data":{"id":0,"current":1283}}
{"type":"progressTick","data":{"id":0,"current":1284}}
{"type":"progressTick","data":{"id":0,"current":1285}}
{"type":"progressTick","data":{"id":0,"current":1286}}
{"type":"progressTick","data":{"id":0,"current":1287}}
{"type":"progressTick","data":{"id":0,"current":1288}}
{"type":"progressTick","data":{"id":0,"current":1289}}
{"type":"progressTick","data":{"id":0,"current":1290}}
{"type":"progressTick","data":{"id":0,"current":1291}}
{"type":"progressTick","data":{"id":0,"current":1292}}
{"type":"progressTick","data":{"id":0,"current":1293}}
{"type":"progressTick","data":{"id":0,"current":1294}}
{"type":"progressTick","data":{"id":0,"current":1295}}
{"type":"progressTick","data":{"id":0,"current":1296}}
{"type":"progressTick","data":{"id":0,"current":1297}}
{"type":"progressTick","data":{"id":0,"current":1298}}
{"type":"progressTick","data":{"id":0,"current":1299}}
{"type":"progressTick","data":{"id":0,"current":1300}}
{"type":"progressTick","data":{"id":0,"current":1301}}
{"type":"progressTick","data":{"id":0,"current":1302}}
{"type":"progressTick","data":{"id":0,"current":1303}}
{"type":"progressTick","data":{"id":0,"current":1304}}
{"type":"progressTick","data":{"id":0,"current":1305}}
{"type":"progressTick","data":{"id":0,"current":1306}}
{"type":"progressTick","data":{"id":0,"current":1307}}
{"type":"progressTick","data":{"id":0,"current":1308}}
{"type":"progressTick","data":{"id":0,"current":1309}}
{"type":"progressTick","data":{"id":0,"current":1310}}
{"type":"progressTick","data":{"id":0,"current":1311}}
{"type":"progressTick","data":{"id":0,"current":1312}}
{"type":"progressTick","data":{"id":0,"current":1313}}
{"type":"progressTick","data":{"id":0,"current":1314}}
{"type":"progressTick","data":{"id":0,"current":1315}}
{"type":"progressTick","data":{"id":0,"current":1316}}
{"type":"progressTick","data":{"id":0,"current":1317}}
{"type":"progressTick","data":{"id":0,"current":1318}}
{"type":"progressTick","data":{"id":0,"current":1319}}
{"type":"progressTick","data":{"id":0,"current":1320}}
{"type":"progressTick","data":{"id":0,"current":1321}}
{"type":"progressTick","data":{"id":0,"current":1322}}
{"type":"progressTick","data":{"id":0,"current":1323}}
{"type":"progressTick","data":{"id":0,"current":1324}}
{"type":"progressTick","data":{"id":0,"current":1325}}
{"type":"progressTick","data":{"id":0,"current":1326}}
{"type":"progressTick","data":{"id":0,"current":1327}}
{"type":"progressTick","data":{"id":0,"current":1328}}
{"type":"progressTick","data":{"id":0,"current":1329}}
{"type":"progressTick","data":{"id":0,"current":1330}}
{"type":"progressTick","data":{"id":0,"current":1331}}
{"type":"progressTick","data":{"id":0,"current":1332}}
{"type":"progressTick","data":{"id":0,"current":1333}}
{"type":"progressTick","data":{"id":0,"current":1334}}
{"type":"progressTick","data":{"id":0,"current":1335}}
{"type":"progressTick","data":{"id":0,"current":1336}}
{"type":"progressTick","data":{"id":0,"current":1337}}
{"type":"progressTick","data":{"id":0,"current":1338}}
{"type":"progressTick","data":{"id":0,"current":1339}}
{"type":"progressTick","data":{"id":0,"current":1340}}
{"type":"progressTick","data":{"id":0,"current":1341}}
{"type":"progressTick","data":{"id":0,"current":1342}}
{"type":"progressTick","data":{"id":0,"current":1343}}
{"type":"progressTick","data":{"id":0,"current":1344}}
{"type":"progressTick","data":{"id":0,"current":1345}}
{"type":"progressTick","data":{"id":0,"current":1346}}
{"type":"progressTick","data":{"id":0,"current":1347}}
{"type":"progressTick","data":{"id":0,"current":1348}}
{"type":"progressTick","data":{"id":0,"current":1349}}
{"type":"progressTick","data":{"id":0,"current":1350}}
{"type":"progressTick","data":{"id":0,"current":1351}}
{"type":"progressTick","data":{"id":0,"current":1352}}
{"type":"progressTick","data":{"id":0,"current":1353}}
{"type":"progressTick","data":{"id":0,"current":1354}}
{"type":"progressTick","data":{"id":0,"current":1355}}
{"type":"progressTick","data":{"id":0,"current":1356}}
{"type":"progressTick","data":{"id":0,"current":1357}}
{"type":"progressTick","data":{"id":0,"current":1358}}
{"type":"progressTick","data":{"id":0,"current":1359}}
{"type":"progressTick","data":{"id":0,"current":1360}}
{"type":"progressTick","data":{"id":0,"current":1361}}
{"type":"progressTick","data":{"id":0,"current":1362}}
{"type":"progressTick","data":{"id":0,"current":1363}}
{"type":"progressTick","data":{"id":0,"current":1364}}
{"type":"progressTick","data":{"id":0,"current":1365}}
{"type":"progressTick","data":{"id":0,"current":1366}}
{"type":"progressTick","data":{"id":0,"current":1367}}
{"type":"progressTick","data":{"id":0,"current":1368}}
{"type":"progressTick","data":{"id":0,"current":1369}}
{"type":"progressTick","data":{"id":0,"current":1370}}
{"type":"progressTick","data":{"id":0,"current":1371}}
{"type":"progressTick","data":{"id":0,"current":1372}}
{"type":"progressTick","data":{"id":0,"current":1373}}
{"type":"progressTick","data":{"id":0,"current":1374}}
{"type":"progressTick","data":{"id":0,"current":1375}}
{"type":"progressTick","data":{"id":0,"current":1376}}
{"type":"progressTick","data":{"id":0,"current":1377}}
{"type":"progressTick","data":{"id":0,"current":1378}}
{"type":"progressTick","data":{"id":0,"current":1379}}
{"type":"progressTick","data":{"id":0,"current":1380}}
{"type":"progressTick","data":{"id":0,"current":1381}}
{"type":"progressTick","data":{"id":0,"current":1382}}
{"type":"progressTick","data":{"id":0,"current":1383}}
{"type":"progressTick","data":{"id":0,"current":1384}}
{"type":"progressTick","data":{"id":0,"current":1385}}
{"type":"progressTick","data":{"id":0,"current":1386}}
{"type":"progressTick","data":{"id":0,"current":1387}}
{"type":"progressTick","data":{"id":0,"current":1388}}
{"type":"progressTick","data":{"id":0,"current":1389}}
{"type":"progressTick","data":{"id":0,"current":1390}}
{"type":"progressTick","data":{"id":0,"current":1391}}
{"type":"progressTick","data":{"id":0,"current":1392}}
{"type":"progressTick","data":{"id":0,"current":1393}}
{"type":"progressTick","data":{"id":0,"current":1394}}
{"type":"progressTick","data":{"id":0,"current":1395}}
{"type":"progressTick","data":{"id":0,"current":1396}}
{"type":"progressTick","data":{"id":0,"current":1397}}
{"type":"progressTick","data":{"id":0,"current":1398}}
{"type":"progressTick","data":{"id":0,"current":1399}}
{"type":"progressTick","data":{"id":0,"current":1400}}
{"type":"progressTick","data":{"id":0,"current":1401}}
{"type":"progressTick","data":{"id":0,"current":1402}}
{"type":"progressTick","data":{"id":0,"current":1403}}
{"type":"progressTick","data":{"id":0,"current":1404}}
{"type":"progressTick","data":{"id":0,"current":1405}}
{"type":"progressTick","data":{"id":0,"current":1406}}
{"type":"progressTick","data":{"id":0,"current":1407}}
{"type":"progressTick","data":{"id":0,"current":1408}}
{"type":"progressTick","data":{"id":0,"current":1409}}
{"type":"progressTick","data":{"id":0,"current":1410}}
{"type":"progressTick","data":{"id":0,"current":1411}}
{"type":"progressTick","data":{"id":0,"current":1412}}
{"type":"progressTick","data":{"id":0,"current":1413}}
{"type":"progressTick","data":{"id":0,"current":1414}}
{"type":"progressTick","data":{"id":0,"current":1415}}
{"type":"progressTick","data":{"id":0,"current":1416}}
{"type":"progressTick","data":{"id":0,"current":1417}}
{"type":"progressTick","data":{"id":0,"current":1418}}
{"type":"progressTick","data":{"id":0,"current":1419}}
{"type":"progressTick","data":{"id":0,"current":1420}}
{"type":"progressTick","data":{"id":0,"current":1421}}
{"type":"progressTick","data":{"id":0,"current":1422}}
{"type":"progressTick","data":{"id":0,"current":1423}}
{"type":"progressTick","data":{"id":0,"current":1424}}
{"type":"progressTick","data":{"id":0,"current":1425}}
{"type":"progressTick","data":{"id":0,"current":1426}}
{"type":"progressTick","data":{"id":0,"current":1427}}
{"type":"progressTick","data":{"id":0,"current":1428}}
{"type":"progressTick","data":{"id":0,"current":1429}}
{"type":"progressTick","data":{"id":0,"current":1430}}
{"type":"progressTick","data":{"id":0,"current":1431}}
{"type":"progressTick","data":{"id":0,"current":1432}}
{"type":"progressTick","data":{"id":0,"current":1433}}
{"type":"progressTick","data":{"id":0,"current":1434}}
{"type":"progressTick","data":{"id":0,"current":1435}}
{"type":"progressTick","data":{"id":0,"current":1436}}
{"type":"progressTick","data":{"id":0,"current":1437}}
{"type":"progressTick","data":{"id":0,"current":1438}}
{"type":"progressTick","data":{"id":0,"current":1439}}
{"type":"progressTick","data":{"id":0,"current":1440}}
{"type":"progressTick","data":{"id":0,"current":1441}}
{"type":"progressTick","data":{"id":0,"current":1442}}
{"type":"progressTick","data":{"id":0,"current":1443}}
{"type":"progressTick","data":{"id":0,"current":1444}}
{"type":"progressTick","data":{"id":0,"current":1445}}
{"type":"progressTick","data":{"id":0,"current":1446}}
{"type":"progressTick","data":{"id":0,"current":1447}}
{"type":"progressTick","data":{"id":0,"current":1448}}
{"type":"progressTick","data":{"id":0,"current":1449}}
{"type":"progressTick","data":{"id":0,"current":1450}}
{"type":"progressTick","data":{"id":0,"current":1451}}
{"type":"progressTick","data":{"id":0,"current":1452}}
{"type":"progressTick","data":{"id":0,"current":1453}}
{"type":"progressTick","data":{"id":0,"current":1454}}
{"type":"progressTick","data":{"id":0,"current":1455}}
{"type":"progressTick","data":{"id":0,"current":1456}}
{"type":"progressTick","data":{"id":0,"current":1457}}
{"type":"progressTick","data":{"id":0,"current":1458}}
{"type":"progressTick","data":{"id":0,"current":1459}}
{"type":"progressTick","data":{"id":0,"current":1460}}
{"type":"progressTick","data":{"id":0,"current":1461}}
{"type":"progressTick","data":{"id":0,"current":1462}}
{"type":"progressTick","data":{"id":0,"current":1463}}
{"type":"progressTick","data":{"id":0,"current":1464}}
{"type":"progressTick","data":{"id":0,"current":1465}}
{"type":"progressTick","data":{"id":0,"current":1466}}
{"type":"progressTick","data":{"id":0,"current":1467}}
{"type":"progressTick","data":{"id":0,"current":1468}}
{"type":"progressTick","data":{"id":0,"current":1469}}
{"type":"progressTick","data":{"id":0,"current":1470}}
{"type":"progressTick","data":{"id":0,"current":1471}}
{"type":"progressTick","data":{"id":0,"current":1472}}
{"type":"progressTick","data":{"id":0,"current":1473}}
{"type":"progressTick","data":{"id":0,"current":1474}}
{"type":"progressTick","data":{"id":0,"current":1475}}
{"type":"progressTick","data":{"id":0,"current":1476}}
{"type":"progressTick","data":{"id":0,"current":1477}}
{"type":"progressTick","data":{"id":0,"current":1478}}
{"type":"progressTick","data":{"id":0,"current":1479}}
{"type":"progressTick","data":{"id":0,"current":1480}}
{"type":"progressTick","data":{"id":0,"current":1481}}
{"type":"progressTick","data":{"id":0,"current":1482}}
{"type":"progressTick","data":{"id":0,"current":1483}}
{"type":"progressTick","data":{"id":0,"current":1484}}
{"type":"progressTick","data":{"id":0,"current":1485}}
{"type":"progressTick","data":{"id":0,"current":1486}}
{"type":"progressTick","data":{"id":0,"current":1487}}
{"type":"progressTick","data":{"id":0,"current":1488}}
{"type":"progressTick","data":{"id":0,"current":1489}}
{"type":"progressTick","data":{"id":0,"current":1490}}
{"type":"progressTick","data":{"id":0,"current":1491}}
{"type":"progressTick","data":{"id":0,"current":1492}}
{"type":"progressTick","data":{"id":0,"current":1493}}
{"type":"progressTick","data":{"id":0,"current":1494}}
{"type":"progressTick","data":{"id":0,"current":1495}}
{"type":"progressTick","data":{"id":0,"current":1496}}
{"type":"progressTick","data":{"id":0,"current":1497}}
{"type":"progressTick","data":{"id":0,"current":1498}}
{"type":"progressTick","data":{"id":0,"current":1499}}
{"type":"progressTick","data":{"id":0,"current":1500}}
{"type":"progressTick","data":{"id":0,"current":1501}}
{"type":"progressTick","data":{"id":0,"current":1502}}
{"type":"progressTick","data":{"id":0,"current":1503}}
{"type":"progressTick","data":{"id":0,"current":1504}}
{"type":"progressTick","data":{"id":0,"current":1505}}
{"type":"progressTick","data":{"id":0,"current":1506}}
{"type":"progressTick","data":{"id":0,"current":1507}}
{"type":"progressTick","data":{"id":0,"current":1508}}
{"type":"progressTick","data":{"id":0,"current":1509}}
{"type":"progressTick","data":{"id":0,"current":1510}}
{"type":"progressTick","data":{"id":0,"current":1511}}
{"type":"progressTick","data":{"id":0,"current":1512}}
{"type":"progressTick","data":{"id":0,"current":1513}}
{"type":"progressTick","data":{"id":0,"current":1514}}
{"type":"progressTick","data":{"id":0,"current":1515}}
{"type":"progressTick","data":{"id":0,"current":1516}}
{"type":"progressTick","data":{"id":0,"current":1517}}
{"type":"progressTick","data":{"id":0,"current":1518}}
{"type":"progressTick","data":{"id":0,"current":1519}}
{"type":"progressTick","data":{"id":0,"current":1520}}
{"type":"progressTick","data":{"id":0,"current":1521}}
{"type":"progressTick","data":{"id":0,"current":1522}}
{"type":"progressTick","data":{"id":0,"current":1523}}
{"type":"progressTick","data":{"id":0,"current":1524}}
{"type":"progressTick","data":{"id":0,"current":1525}}
{"type":"progressTick","data":{"id":0,"current":1526}}
{"type":"progressTick","data":{"id":0,"current":1527}}
{"type":"progressTick","data":{"id":0,"current":1528}}
{"type":"progressTick","data":{"id":0,"current":1529}}
{"type":"progressTick","data":{"id":0,"current":1530}}
{"type":"progressTick","data":{"id":0,"current":1531}}
{"type":"progressTick","data":{"id":0,"current":1532}}
{"type":"progressTick","data":{"id":0,"current":1533}}
{"type":"progressTick","data":{"id":0,"current":1534}}
{"type":"progressTick","data":{"id":0,"current":1535}}
{"type":"progressTick","data":{"id":0,"current":1536}}
{"type":"progressTick","data":{"id":0,"current":1537}}
{"type":"progressTick","data":{"id":0,"current":1538}}
{"type":"progressTick","data":{"id":0,"current":1539}}
{"type":"progressTick","data":{"id":0,"current":1540}}
{"type":"progressTick","data":{"id":0,"current":1541}}
{"type":"progressTick","data":{"id":0,"current":1542}}
{"type":"progressTick","data":{"id":0,"current":1543}}
{"type":"progressTick","data":{"id":0,"current":1544}}
{"type":"progressTick","data":{"id":0,"current":1545}}
{"type":"progressTick","data":{"id":0,"current":1546}}
{"type":"progressTick","data":{"id":0,"current":1547}}
{"type":"progressTick","data":{"id":0,"current":1548}}
{"type":"progressTick","data":{"id":0,"current":1549}}
{"type":"progressTick","data":{"id":0,"current":1550}}
{"type":"progressTick","data":{"id":0,"current":1551}}
{"type":"progressTick","data":{"id":0,"current":1552}}
{"type":"progressTick","data":{"id":0,"current":1553}}
{"type":"progressTick","data":{"id":0,"current":1554}}
{"type":"progressTick","data":{"id":0,"current":1555}}
{"type":"progressTick","data":{"id":0,"current":1556}}
{"type":"progressTick","data":{"id":0,"current":1557}}
{"type":"progressTick","data":{"id":0,"current":1558}}
{"type":"progressTick","data":{"id":0,"current":1559}}
{"type":"progressTick","data":{"id":0,"current":1560}}
{"type":"progressTick","data":{"id":0,"current":1561}}
{"type":"progressTick","data":{"id":0,"current":1562}}
{"type":"progressTick","data":{"id":0,"current":1563}}
{"type":"progressTick","data":{"id":0,"current":1564}}
{"type":"progressTick","data":{"id":0,"current":1565}}
{"type":"progressTick","data":{"id":0,"current":1566}}
{"type":"progressTick","data":{"id":0,"current":1567}}
{"type":"progressTick","data":{"id":0,"current":1568}}
{"type":"progressTick","data":{"id":0,"current":1569}}
{"type":"progressTick","data":{"id":0,"current":1570}}
{"type":"progressTick","data":{"id":0,"current":1571}}
{"type":"progressTick","data":{"id":0,"current":1572}}
{"type":"progressTick","data":{"id":0,"current":1573}}
{"type":"progressTick","data":{"id":0,"current":1574}}
{"type":"progressTick","data":{"id":0,"current":1575}}
{"type":"progressTick","data":{"id":0,"current":1576}}
{"type":"progressTick","data":{"id":0,"current":1577}}
{"type":"progressTick","data":{"id":0,"current":1578}}
{"type":"progressTick","data":{"id":0,"current":1579}}
{"type":"progressTick","data":{"id":0,"current":1580}}
{"type":"progressTick","data":{"id":0,"current":1581}}
{"type":"progressTick","data":{"id":0,"current":1582}}
{"type":"progressTick","data":{"id":0,"current":1583}}
{"type":"progressTick","data":{"id":0,"current":1584}}
{"type":"progressTick","data":{"id":0,"current":1585}}
{"type":"progressTick","data":{"id":0,"current":1586}}
{"type":"progressTick","data":{"id":0,"current":1587}}
{"type":"progressTick","data":{"id":0,"current":1588}}
{"type":"progressTick","data":{"id":0,"current":1589}}
{"type":"progressTick","data":{"id":0,"current":1590}}
{"type":"progressTick","data":{"id":0,"current":1591}}
{"type":"progressTick","data":{"id":0,"current":1592}}
{"type":"progressTick","data":{"id":0,"current":1593}}
{"type":"progressTick","data":{"id":0,"current":1594}}
{"type":"progressTick","data":{"id":0,"current":1595}}
{"type":"progressTick","data":{"id":0,"current":1596}}
{"type":"progressTick","data":{"id":0,"current":1597}}
{"type":"progressTick","data":{"id":0,"current":1598}}
{"type":"progressTick","data":{"id":0,"current":1599}}
{"type":"progressTick","data":{"id":0,"current":1600}}
{"type":"progressTick","data":{"id":0,"current":1601}}
{"type":"progressTick","data":{"id":0,"current":1602}}
{"type":"progressTick","data":{"id":0,"current":1603}}
{"type":"progressTick","data":{"id":0,"current":1604}}
{"type":"progressTick","data":{"id":0,"current":1605}}
{"type":"progressTick","data":{"id":0,"current":1606}}
{"type":"progressTick","data":{"id":0,"current":1607}}
{"type":"progressTick","data":{"id":0,"current":1608}}
{"type":"progressTick","data":{"id":0,"current":1609}}
{"type":"progressTick","data":{"id":0,"current":1610}}
{"type":"progressTick","data":{"id":0,"current":1611}}
{"type":"progressTick","data":{"id":0,"current":1612}}
{"type":"progressTick","data":{"id":0,"current":1613}}
{"type":"progressTick","data":{"id":0,"current":1614}}
{"type":"progressTick","data":{"id":0,"current":1615}}
{"type":"progressTick","data":{"id":0,"current":1616}}
{"type":"progressTick","data":{"id":0,"current":1617}}
{"type":"progressTick","data":{"id":0,"current":1618}}
{"type":"progressTick","data":{"id":0,"current":1619}}
{"type":"progressTick","data":{"id":0,"current":1620}}
{"type":"progressTick","data":{"id":0,"current":1621}}
{"type":"progressTick","data":{"id":0,"current":1622}}
{"type":"progressTick","data":{"id":0,"current":1623}}
{"type":"progressTick","data":{"id":0,"current":1624}}
{"type":"progressTick","data":{"id":0,"current":1625}}
{"type":"progressTick","data":{"id":0,"current":1626}}
{"type":"progressTick","data":{"id":0,"current":1627}}
{"type":"progressTick","data":{"id":0,"current":1628}}
{"type":"progressTick","data":{"id":0,"current":1629}}
{"type":"progressTick","data":{"id":0,"current":1630}}
{"type":"progressTick","data":{"id":0,"current":1631}}
{"type":"progressTick","data":{"id":0,"current":1632}}
{"type":"progressTick","data":{"id":0,"current":1633}}
{"type":"progressTick","data":{"id":0,"current":1634}}
{"type":"progressTick","data":{"id":0,"current":1635}}
{"type":"progressTick","data":{"id":0,"current":1636}}
{"type":"progressTick","data":{"id":0,"current":1637}}
{"type":"progressTick","data":{"id":0,"current":1638}}
{"type":"progressTick","data":{"id":0,"current":1639}}
{"type":"progressTick","data":{"id":0,"current":1640}}
{"type":"progressTick","data":{"id":0,"current":1641}}
{"type":"progressTick","data":{"id":0,"current":1642}}
{"type":"progressTick","data":{"id":0,"current":1643}}
{"type":"progressTick","data":{"id":0,"current":1644}}
{"type":"progressTick","data":{"id":0,"current":1645}}
{"type":"progressTick","data":{"id":0,"current":1646}}
{"type":"progressTick","data":{"id":0,"current":1647}}
{"type":"progressTick","data":{"id":0,"current":1648}}
{"type":"progressTick","data":{"id":0,"current":1649}}
{"type":"progressTick","data":{"id":0,"current":1650}}
{"type":"progressTick","data":{"id":0,"current":1651}}
{"type":"progressTick","data":{"id":0,"current":1652}}
{"type":"progressTick","data":{"id":0,"current":1653}}
{"type":"progressTick","data":{"id":0,"current":1654}}
{"type":"progressTick","data":{"id":0,"current":1655}}
{"type":"progressTick","data":{"id":0,"current":1656}}
{"type":"progressTick","data":{"id":0,"current":1657}}
{"type":"progressTick","data":{"id":0,"current":1658}}
{"type":"progressTick","data":{"id":0,"current":1659}}
{"type":"progressTick","data":{"id":0,"current":1660}}
{"type":"progressTick","data":{"id":0,"current":1661}}
{"type":"progressTick","data":{"id":0,"current":1662}}
{"type":"progressTick","data":{"id":0,"current":1663}}
{"type":"progressTick","data":{"id":0,"current":1664}}
{"type":"progressTick","data":{"id":0,"current":1665}}
{"type":"progressTick","data":{"id":0,"current":1666}}
{"type":"progressTick","data":{"id":0,"current":1667}}
{"type":"progressTick","data":{"id":0,"current":1668}}
{"type":"progressTick","data":{"id":0,"current":1669}}
{"type":"progressTick","data":{"id":0,"current":1670}}
{"type":"progressTick","data":{"id":0,"current":1671}}
{"type":"progressTick","data":{"id":0,"current":1672}}
{"type":"progressTick","data":{"id":0,"current":1673}}
{"type":"progressTick","data":{"id":0,"current":1674}}
{"type":"progressTick","data":{"id":0,"current":1675}}
{"type":"progressTick","data":{"id":0,"current":1676}}
{"type":"progressTick","data":{"id":0,"current":1677}}
{"type":"progressTick","data":{"id":0,"current":1678}}
{"type":"progressTick","data":{"id":0,"current":1679}}
{"type":"progressTick","data":{"id":0,"current":1680}}
{"type":"progressTick","data":{"id":0,"current":1681}}
{"type":"progressTick","data":{"id":0,"current":1682}}
{"type":"progressTick","data":{"id":0,"current":1683}}
{"type":"progressTick","data":{"id":0,"current":1684}}
{"type":"progressTick","data":{"id":0,"current":1685}}
{"type":"progressTick","data":{"id":0,"current":1686}}
{"type":"progressTick","data":{"id":0,"current":1687}}
{"type":"progressTick","data":{"id":0,"current":1688}}
{"type":"progressTick","data":{"id":0,"current":1689}}
{"type":"progressTick","data":{"id":0,"current":1690}}
{"type":"progressTick","data":{"id":0,"current":1691}}
{"type":"progressTick","data":{"id":0,"current":1692}}
{"type":"progressTick","data":{"id":0,"current":1693}}
{"type":"progressTick","data":{"id":0,"current":1694}}
{"type":"progressTick","data":{"id":0,"current":1695}}
{"type":"progressTick","data":{"id":0,"current":1696}}
{"type":"progressTick","data":{"id":0,"current":1697}}
{"type":"progressTick","data":{"id":0,"current":1698}}
{"type":"progressTick","data":{"id":0,"current":1699}}
{"type":"progressTick","data":{"id":0,"current":1700}}
{"type":"progressTick","data":{"id":0,"current":1701}}
{"type":"progressTick","data":{"id":0,"current":1702}}
{"type":"progressTick","data":{"id":0,"current":1703}}
{"type":"progressTick","data":{"id":0,"current":1704}}
{"type":"progressTick","data":{"id":0,"current":1705}}
{"type":"progressTick","data":{"id":0,"current":1706}}
{"type":"progressTick","data":{"id":0,"current":1707}}
{"type":"progressTick","data":{"id":0,"current":1708}}
{"type":"progressTick","data":{"id":0,"current":1709}}
{"type":"progressTick","data":{"id":0,"current":1710}}
{"type":"progressTick","data":{"id":0,"current":1711}}
{"type":"progressTick","data":{"id":0,"current":1712}}
{"type":"progressTick","data":{"id":0,"current":1713}}
{"type":"progressTick","data":{"id":0,"current":1714}}
{"type":"progressTick","data":{"id":0,"current":1715}}
{"type":"progressTick","data":{"id":0,"current":1716}}
{"type":"progressTick","data":{"id":0,"current":1717}}
{"type":"progressTick","data":{"id":0,"current":1718}}
{"type":"progressTick","data":{"id":0,"current":1719}}
{"type":"progressTick","data":{"id":0,"current":1720}}
{"type":"progressTick","data":{"id":0,"current":1721}}
{"type":"progressTick","data":{"id":0,"current":1722}}
{"type":"progressTick","data":{"id":0,"current":1723}}
{"type":"progressTick","data":{"id":0,"current":1724}}
{"type":"progressTick","data":{"id":0,"current":1725}}
{"type":"progressTick","data":{"id":0,"current":1726}}
{"type":"progressTick","data":{"id":0,"current":1727}}
{"type":"progressTick","data":{"id":0,"current":1728}}
{"type":"progressTick","data":{"id":0,"current":1729}}
{"type":"progressTick","data":{"id":0,"current":1730}}
{"type":"progressTick","data":{"id":0,"current":1731}}
{"type":"progressTick","data":{"id":0,"current":1732}}
{"type":"progressTick","data":{"id":0,"current":1733}}
{"type":"progressTick","data":{"id":0,"current":1734}}
{"type":"progressTick","data":{"id":0,"current":1735}}
{"type":"progressTick","data":{"id":0,"current":1736}}
{"type":"progressTick","data":{"id":0,"current":1737}}
{"type":"progressTick","data":{"id":0,"current":1738}}
{"type":"progressTick","data":{"id":0,"current":1739}}
{"type":"progressTick","data":{"id":0,"current":1740}}
{"type":"progressTick","data":{"id":0,"current":1741}}
{"type":"progressTick","data":{"id":0,"current":1742}}
{"type":"progressTick","data":{"id":0,"current":1743}}
{"type":"progressTick","data":{"id":0,"current":1744}}
{"type":"progressTick","data":{"id":0,"current":1745}}
{"type":"progressTick","data":{"id":0,"current":1746}}
{"type":"progressTick","data":{"id":0,"current":1747}}
{"type":"progressTick","data":{"id":0,"current":1748}}
{"type":"progressTick","data":{"id":0,"current":1749}}
{"type":"progressTick","data":{"id":0,"current":1750}}
{"type":"progressTick","data":{"id":0,"current":1751}}
{"type":"progressTick","data":{"id":0,"current":1752}}
{"type":"progressTick","data":{"id":0,"current":1753}}
{"type":"progressTick","data":{"id":0,"current":1754}}
{"type":"progressTick","data":{"id":0,"current":1755}}
{"type":"progressTick","data":{"id":0,"current":1756}}
{"type":"progressTick","data":{"id":0,"current":1757}}
{"type":"progressTick","data":{"id":0,"current":1758}}
{"type":"progressTick","data":{"id":0,"current":1759}}
{"type":"progressTick","data":{"id":0,"current":1760}}
{"type":"progressTick","data":{"id":0,"current":1761}}
{"type":"progressTick","data":{"id":0,"current":1762}}
{"type":"progressTick","data":{"id":0,"current":1763}}
{"type":"progressTick","data":{"id":0,"current":1764}}
{"type":"progressTick","data":{"id":0,"current":1765}}
{"type":"progressTick","data":{"id":0,"current":1766}}
{"type":"progressTick","data":{"id":0,"current":1767}}
{"type":"progressTick","data":{"id":0,"current":1768}}
{"type":"progressTick","data":{"id":0,"current":1769}}
{"type":"progressTick","data":{"id":0,"current":1770}}
{"type":"progressTick","data":{"id":0,"current":1771}}
{"type":"progressTick","data":{"id":0,"current":1772}}
{"type":"progressTick","data":{"id":0,"current":1773}}
{"type":"progressTick","data":{"id":0,"current":1774}}
{"type":"progressTick","data":{"id":0,"current":1775}}
{"type":"progressTick","data":{"id":0,"current":1776}}
{"type":"progressTick","data":{"id":0,"current":1777}}
{"type":"progressTick","data":{"id":0,"current":1778}}
{"type":"progressTick","data":{"id":0,"current":1779}}
{"type":"progressTick","data":{"id":0,"current":1780}}
{"type":"progressTick","data":{"id":0,"current":1781}}
{"type":"progressTick","data":{"id":0,"current":1782}}
{"type":"progressTick","data":{"id":0,"current":1783}}
{"type":"progressTick","data":{"id":0,"current":1784}}
{"type":"progressTick","data":{"id":0,"current":1785}}
{"type":"progressTick","data":{"id":0,"current":1786}}
{"type":"progressTick","data":{"id":0,"current":1787}}
{"type":"progressTick","data":{"id":0,"current":1788}}
{"type":"progressTick","data":{"id":0,"current":1789}}
{"type":"progressTick","data":{"id":0,"current":1790}}
{"type":"progressTick","data":{"id":0,"current":1791}}
{"type":"progressTick","data":{"id":0,"current":1792}}
{"type":"progressTick","data":{"id":0,"current":1793}}
{"type":"progressTick","data":{"id":0,"current":1794}}
{"type":"progressTick","data":{"id":0,"current":1795}}
{"type":"progressTick","data":{"id":0,"current":1796}}
{"type":"progressTick","data":{"id":0,"current":1797}}
{"type":"progressTick","data":{"id":0,"current":1798}}
{"type":"progressTick","data":{"id":0,"current":1799}}
{"type":"progressTick","data":{"id":0,"current":1800}}
{"type":"progressTick","data":{"id":0,"current":1801}}
{"type":"progressTick","data":{"id":0,"current":1802}}
{"type":"progressTick","data":{"id":0,"current":1803}}
{"type":"progressTick","data":{"id":0,"current":1804}}
{"type":"progressTick","data":{"id":0,"current":1805}}
{"type":"progressTick","data":{"id":0,"current":1806}}
{"type":"progressTick","data":{"id":0,"current":1807}}
{"type":"progressTick","data":{"id":0,"current":1808}}
{"type":"progressTick","data":{"id":0,"current":1809}}
{"type":"progressTick","data":{"id":0,"current":1810}}
{"type":"progressTick","data":{"id":0,"current":1811}}
{"type":"progressTick","data":{"id":0,"current":1812}}
{"type":"progressTick","data":{"id":0,"current":1813}}
{"type":"progressTick","data":{"id":0,"current":1814}}
{"type":"progressTick","data":{"id":0,"current":1815}}
{"type":"progressTick","data":{"id":0,"current":1816}}
{"type":"progressTick","data":{"id":0,"current":1817}}
{"type":"progressTick","data":{"id":0,"current":1818}}
{"type":"progressTick","data":{"id":0,"current":1819}}
{"type":"progressTick","data":{"id":0,"current":1820}}
{"type":"progressTick","data":{"id":0,"current":1821}}
{"type":"progressTick","data":{"id":0,"current":1822}}
{"type":"progressTick","data":{"id":0,"current":1823}}
{"type":"progressTick","data":{"id":0,"current":1824}}
{"type":"progressTick","data":{"id":0,"current":1825}}
{"type":"progressTick","data":{"id":0,"current":1826}}
{"type":"progressTick","data":{"id":0,"current":1827}}
{"type":"progressTick","data":{"id":0,"current":1828}}
{"type":"progressTick","data":{"id":0,"current":1829}}
{"type":"progressTick","data":{"id":0,"current":1830}}
{"type":"progressTick","data":{"id":0,"current":1831}}
{"type":"progressTick","data":{"id":0,"current":1832}}
{"type":"progressTick","data":{"id":0,"current":1833}}
{"type":"progressTick","data":{"id":0,"current":1834}}
{"type":"progressTick","data":{"id":0,"current":1835}}
{"type":"progressTick","data":{"id":0,"current":1836}}
{"type":"progressTick","data":{"id":0,"current":1837}}
{"type":"progressTick","data":{"id":0,"current":1838}}
{"type":"progressTick","data":{"id":0,"current":1839}}
{"type":"progressTick","data":{"id":0,"current":1840}}
{"type":"progressTick","data":{"id":0,"current":1841}}
{"type":"progressTick","data":{"id":0,"current":1842}}
{"type":"progressTick","data":{"id":0,"current":1843}}
{"type":"progressTick","data":{"id":0,"current":1844}}
{"type":"progressTick","data":{"id":0,"current":1845}}
{"type":"progressTick","data":{"id":0,"current":1846}}
{"type":"progressTick","data":{"id":0,"current":1847}}
{"type":"progressTick","data":{"id":0,"current":1848}}
{"type":"progressTick","data":{"id":0,"current":1849}}
{"type":"progressTick","data":{"id":0,"current":1850}}
{"type":"progressTick","data":{"id":0,"current":1851}}
{"type":"progressTick","data":{"id":0,"current":1852}}
{"type":"progressTick","data":{"id":0,"current":1853}}
{"type":"progressTick","data":{"id":0,"current":1854}}
{"type":"progressTick","data":{"id":0,"current":1855}}
{"type":"progressTick","data":{"id":0,"current":1856}}
{"type":"progressTick","data":{"id":0,"current":1857}}
{"type":"progressTick","data":{"id":0,"current":1858}}
{"type":"progressTick","data":{"id":0,"current":1859}}
{"type":"progressTick","data":{"id":0,"current":1860}}
{"type":"progressTick","data":{"id":0,"current":1861}}
{"type":"progressTick","data":{"id":0,"current":1862}}
{"type":"progressTick","data":{"id":0,"current":1863}}
{"type":"progressTick","data":{"id":0,"current":1864}}
{"type":"progressTick","data":{"id":0,"current":1865}}
{"type":"progressTick","data":{"id":0,"current":1866}}
{"type":"progressTick","data":{"id":0,"current":1867}}
{"type":"progressTick","data":{"id":0,"current":1868}}
{"type":"progressTick","data":{"id":0,"current":1869}}
{"type":"progressTick","data":{"id":0,"current":1870}}
{"type":"progressTick","data":{"id":0,"current":1871}}
{"type":"progressTick","data":{"id":0,"current":1872}}
{"type":"progressTick","data":{"id":0,"current":1873}}
{"type":"progressTick","data":{"id":0,"current":1874}}
{"type":"progressTick","data":{"id":0,"current":1875}}
{"type":"progressTick","data":{"id":0,"current":1876}}
{"type":"progressTick","data":{"id":0,"current":1877}}
{"type":"progressTick","data":{"id":0,"current":1878}}
{"type":"progressTick","data":{"id":0,"current":1879}}
{"type":"progressTick","data":{"id":0,"current":1880}}
{"type":"progressTick","data":{"id":0,"current":1881}}
{"type":"progressTick","data":{"id":0,"current":1882}}
{"type":"progressTick","data":{"id":0,"current":1883}}
{"type":"progressTick","data":{"id":0,"current":1884}}
{"type":"progressTick","data":{"id":0,"current":1885}}
{"type":"progressTick","data":{"id":0,"current":1886}}
{"type":"progressTick","data":{"id":0,"current":1887}}
{"type":"progressTick","data":{"id":0,"current":1888}}
{"type":"progressTick","data":{"id":0,"current":1889}}
{"type":"progressTick","data":{"id":0,"current":1890}}
{"type":"progressTick","data":{"id":0,"current":1891}}
{"type":"progressTick","data":{"id":0,"current":1892}}
{"type":"progressTick","data":{"id":0,"current":1893}}
{"type":"progressTick","data":{"id":0,"current":1894}}
{"type":"progressTick","data":{"id":0,"current":1895}}
{"type":"progressTick","data":{"id":0,"current":1896}}
{"type":"progressTick","data":{"id":0,"current":1897}}
{"type":"progressTick","data":{"id":0,"current":1898}}
{"type":"progressTick","data":{"id":0,"current":1899}}
{"type":"progressTick","data":{"id":0,"current":1900}}
{"type":"progressTick","data":{"id":0,"current":1901}}
{"type":"progressTick","data":{"id":0,"current":1902}}
{"type":"progressTick","data":{"id":0,"current":1903}}
{"type":"progressTick","data":{"id":0,"current":1904}}
{"type":"progressTick","data":{"id":0,"current":1905}}
{"type":"progressTick","data":{"id":0,"current":1906}}
{"type":"progressTick","data":{"id":0,"current":1907}}
{"type":"progressTick","data":{"id":0,"current":1908}}
{"type":"progressTick","data":{"id":0,"current":1909}}
{"type":"progressTick","data":{"id":0,"current":1910}}
{"type":"progressTick","data":{"id":0,"current":1911}}
{"type":"progressTick","data":{"id":0,"current":1912}}
{"type":"progressTick","data":{"id":0,"current":1913}}
{"type":"progressTick","data":{"id":0,"current":1914}}
{"type":"progressTick","data":{"id":0,"current":1915}}
{"type":"progressTick","data":{"id":0,"current":1916}}
{"type":"progressTick","data":{"id":0,"current":1917}}
{"type":"progressTick","data":{"id":0,"current":1918}}
{"type":"progressTick","data":{"id":0,"current":1919}}
{"type":"progressTick","data":{"id":0,"current":1920}}
{"type":"progressTick","data":{"id":0,"current":1921}}
{"type":"progressTick","data":{"id":0,"current":1922}}
{"type":"progressTick","data":{"id":0,"current":1923}}
{"type":"progressTick","data":{"id":0,"current":1924}}
{"type":"progressTick","data":{"id":0,"current":1925}}
{"type":"progressTick","data":{"id":0,"current":1926}}
{"type":"progressTick","data":{"id":0,"current":1927}}
{"type":"progressTick","data":{"id":0,"current":1928}}
{"type":"progressTick","data":{"id":0,"current":1929}}
{"type":"progressTick","data":{"id":0,"current":1930}}
{"type":"progressTick","data":{"id":0,"current":1931}}
{"type":"progressTick","data":{"id":0,"current":1932}}
{"type":"progressTick","data":{"id":0,"current":1933}}
{"type":"progressTick","data":{"id":0,"current":1934}}
{"type":"progressTick","data":{"id":0,"current":1935}}
{"type":"progressTick","data":{"id":0,"current":1936}}
{"type":"progressTick","data":{"id":0,"current":1937}}
{"type":"progressTick","data":{"id":0,"current":1938}}
{"type":"progressTick","data":{"id":0,"current":1939}}
{"type":"progressTick","data":{"id":0,"current":1940}}
{"type":"progressTick","data":{"id":0,"current":1941}}
{"type":"progressTick","data":{"id":0,"current":1942}}
{"type":"progressTick","data":{"id":0,"current":1943}}
{"type":"progressTick","data":{"id":0,"current":1944}}
{"type":"progressTick","data":{"id":0,"current":1945}}
{"type":"progressTick","data":{"id":0,"current":1946}}
{"type":"progressTick","data":{"id":0,"current":1947}}
{"type":"progressTick","data":{"id":0,"current":1948}}
{"type":"progressTick","data":{"id":0,"current":1949}}
{"type":"progressTick","data":{"id":0,"current":1950}}
{"type":"progressTick","data":{"id":0,"current":1951}}
{"type":"progressTick","data":{"id":0,"current":1952}}
{"type":"progressTick","data":{"id":0,"current":1953}}
{"type":"progressTick","data":{"id":0,"current":1954}}
{"type":"progressTick","data":{"id":0,"current":1955}}
{"type":"progressTick","data":{"id":0,"current":1956}}
{"type":"progressTick","data":{"id":0,"current":1957}}
{"type":"progressTick","data":{"id":0,"current":1958}}
{"type":"progressTick","data":{"id":0,"current":1959}}
{"type":"progressTick","data":{"id":0,"current":1960}}
{"type":"progressTick","data":{"id":0,"current":1961}}
{"type":"progressTick","data":{"id":0,"current":1962}}
{"type":"progressTick","data":{"id":0,"current":1963}}
{"type":"progressTick","data":{"id":0,"current":1964}}
{"type":"progressTick","data":{"id":0,"current":1965}}
{"type":"progressFinish","data":{"id":0}}
{"type":"info","data":"fsevents@2.1.3: The platform \"win32\" is incompatible with this module."}
{"type":"info","data":"\"fsevents@2.1.3\" is an optional dependency and failed compatibility check. Excluding it from installation."}
{"type":"info","data":"fsevents@1.2.13: The platform \"win32\" is incompatible with this module."}
{"type":"info","data":"\"fsevents@1.2.13\" is an optional dependency and failed compatibility check. Excluding it from installation."}
{"type":"table","data":{"head":["Name","Version","License","URL","VendorUrl","VendorName"],"body":[["@angular-builders/custom-webpack","8.2.0","MIT","https://github.com/meltedspark/angular-builders/tree/master/packages/custom-webpack","Unknown","JeB Barabanov"],["@angular-devkit/architect","0.803.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@angular-devkit/build-angular","0.803.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@angular-devkit/build-optimizer","0.803.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@angular-devkit/build-webpack","0.803.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@angular-devkit/core","8.3.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@angular-devkit/schematics","8.3.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@angular/animations","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/cdk","8.2.1","MIT","https://github.com/angular/components.git","https://github.com/angular/components#readme","Unknown"],["@angular/cli","8.3.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@angular/common","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/compiler","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/compiler","8.2.14","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/compiler-cli","8.2.8","MIT","https://github.com/angular/angular.git","https://github.com/angular/angular/tree/master/packages/compiler-cli","Unknown"],["@angular/core","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/elements","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/forms","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/language-service","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/platform-browser","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/platform-browser-dynamic","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/platform-server","8.2.8","MIT","https://github.com/angular/angular.git","Unknown","angular"],["@angular/router","8.2.8","MIT","git+https://github.com/angular/angular.git","https://github.com/angular/angular/tree/master/packages/router","angular"],["@babel/code-frame","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/compat-data","7.11.0","MIT","https://github.com/babel/babel.git","https://babeljs.io/team","The Babel Team"],["@babel/core","7.5.5","MIT","https://github.com/babel/babel/tree/master/packages/babel-core","https://babeljs.io/","Sebastian McKenzie"],["@babel/core","7.11.6","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/generator","7.11.6","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/helper-annotate-as-pure","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-builder-binary-assignment-operator-visitor","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-compilation-targets","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/team","The Babel Team"],["@babel/helper-create-class-features-plugin","7.10.5","MIT","https://github.com/babel/babel.git","https://babeljs.io/team","The Babel Team"],["@babel/helper-create-regexp-features-plugin","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/team","The Babel Team"],["@babel/helper-define-map","7.10.5","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-explode-assignable-expression","7.11.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-function-name","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-get-function-arity","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-hoist-variables","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-member-expression-to-functions","7.11.0","MIT","https://github.com/babel/babel.git","Unknown","Justin Ridgewell"],["@babel/helper-module-imports","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Logan Smyth"],["@babel/helper-module-transforms","7.11.0","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Logan Smyth"],["@babel/helper-optimise-call-expression","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-plugin-utils","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Logan Smyth"],["@babel/helper-regex","7.10.5","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-remap-async-to-generator","7.11.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-replace-supers","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-simple-access","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Logan Smyth"],["@babel/helper-skip-transparent-expression-wrappers","7.11.0","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-split-export-declaration","7.11.0","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-validator-identifier","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helper-wrap-function","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/helpers","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/highlight","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","suchipi"],["@babel/parser","7.10.5","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/parser","7.11.5","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/plugin-proposal-async-generator-functions","7.10.5","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-class-properties","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-dynamic-import","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-export-namespace-from","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-json-strings","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-logical-assignment-operators","7.11.0","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-nullish-coalescing-operator","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-numeric-separator","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-object-rest-spread","7.11.0","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-optional-catch-binding","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-optional-chaining","7.11.0","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-private-methods","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-proposal-unicode-property-regex","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Unknown"],["@babel/plugin-syntax-async-generators","7.8.4","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators","Unknown","Unknown"],["@babel/plugin-syntax-class-properties","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-syntax-dynamic-import","7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-dynamic-import","Unknown","Unknown"],["@babel/plugin-syntax-export-namespace-from","7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-export-namespace-from","Unknown","Unknown"],["@babel/plugin-syntax-json-strings","7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings","Unknown","Unknown"],["@babel/plugin-syntax-logical-assignment-operators","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-syntax-nullish-coalescing-operator","7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator","Unknown","Unknown"],["@babel/plugin-syntax-numeric-separator","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-syntax-object-rest-spread","7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread","Unknown","Unknown"],["@babel/plugin-syntax-optional-catch-binding","7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding","Unknown","Unknown"],["@babel/plugin-syntax-optional-chaining","7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining","Unknown","Unknown"],["@babel/plugin-syntax-top-level-await","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-arrow-functions","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-async-to-generator","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-block-scoped-functions","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-block-scoping","7.11.1","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-classes","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-computed-properties","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-destructuring","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-dotall-regex","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Unknown"],["@babel/plugin-transform-duplicate-keys","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-exponentiation-operator","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-for-of","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-function-name","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-literals","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-member-expression-literals","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-modules-amd","7.10.5","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-modules-commonjs","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-modules-systemjs","7.10.5","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-modules-umd","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-named-capturing-groups-regex","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Unknown"],["@babel/plugin-transform-new-target","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-object-super","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-parameters","7.10.5","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-property-literals","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-regenerator","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Ben Newman"],["@babel/plugin-transform-reserved-words","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-shorthand-properties","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-spread","7.11.0","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-sticky-regex","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-template-literals","7.10.5","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-typeof-symbol","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-unicode-escapes","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/plugin-transform-unicode-regex","7.10.4","MIT","https://github.com/babel/babel.git","Unknown","Unknown"],["@babel/preset-env","7.5.5","MIT","https://github.com/babel/babel/tree/master/packages/babel-preset-env","https://babeljs.io/","Henry Zhu"],["@babel/preset-env","7.11.5","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Henry Zhu"],["@babel/preset-modules","0.1.4","MIT","Unknown","Unknown","Unknown"],["@babel/runtime","7.10.5","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/runtime","7.11.2","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/runtime-corejs3","7.11.2","MIT","https://github.com/babel/babel.git","Unknown","Denis Pushkarev"],["@babel/template","7.10.4","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/traverse","7.11.5","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@babel/types","7.11.5","MIT","https://github.com/babel/babel.git","https://babeljs.io/","Sebastian McKenzie"],["@biesbjerg/ngx-translate-extract","3.0.5","MIT","https://github.com/biesbjerg/ngx-translate-extract.git","https://github.com/biesbjerg/ngx-translate-extract","Kim Biesbjerg"],["@cypress/listr-verbose-renderer","0.4.1","MIT","https://github.com/SamVerschueren/listr-verbose-renderer.git","github.com/SamVerschueren","Sam Verschueren"],["@cypress/webpack-preprocessor","4.1.3","MIT","https://github.com/cypress-io/cypress-webpack-preprocessor.git","https://github.com/cypress-io/cypress-webpack-preprocessor#readme","Chris Breiding"],["@cypress/xvfb","1.2.4","MIT","https://github.com/cypress-io/xvfb.git","https://robwu.nl","Rob Wu"],["@egjs/hammerjs","2.0.16","MIT","git://github.com/naver/hammer.js.git","http://naver.github.io/egjs","Unknown"],["@istanbuljs/schema","0.1.2","MIT","git+https://github.com/istanbuljs/schema.git","https://github.com/istanbuljs/schema#readme","Corey Farrell"],["@jest/types","24.9.0","MIT","https://github.com/facebook/jest.git","Unknown","Unknown"],["@nestjs/common","6.8.3","MIT","https://github.com/nestjs/nest","Unknown","Kamil Mysliwiec"],["@nestjs/core","6.8.3","MIT","https://github.com/nestjs/nest","Unknown","Kamil Mysliwiec"],["@nestjs/cqrs","6.1.0","MIT","Unknown","Unknown","Unknown"],["@nestjs/microservices","6.8.3","MIT","https://github.com/nestjs/nest","Unknown","Kamil Mysliwiec"],["@nestjs/mongoose","6.1.2","MIT","https://github.com/nestjs/mongoose.git","Unknown","Kamil Mysliwiec"],["@nestjs/passport","6.1.0","MIT","Unknown","Unknown","Kamil Mysliwiec"],["@nestjs/platform-express","6.8.3","MIT","https://github.com/nestjs/nest","Unknown","Kamil Mysliwiec"],["@nestjs/swagger","3.1.0","MIT","https://github.com/nestjs/swagger","Unknown","Kamil Mysliwiec"],["@nestjs/testing","6.11.6","MIT","https://github.com/nestjs/nest","https://nestjs.com/","Kamil Mysliwiec"],["@nestjs/testing","6.8.3","MIT","https://github.com/nestjs/nest","Unknown","Kamil Mysliwiec"],["@ng-bootstrap/ng-bootstrap","5.3.0","MIT","git+https://github.com/ng-bootstrap/ng-bootstrap.git","https://github.com/ng-bootstrap/ng-bootstrap#readme","https://github.com/ng-bootstrap/ng-bootstrap/graphs/contributors"],["@ngneat/spectator","5.13.0","MIT","https://github.com/ngneat/spectator","https://github.com/ngneat/spectator#readme","Netanel Basal"],["@ngrx/effects","8.6.0","MIT","https://github.com/ngrx/platform.git","https://github.com/ngrx/platform#readme","NgRx"],["@ngrx/entity","8.6.0","MIT","https://github.com/ngrx/platform.git","https://github.com/ngrx/platform#readme","NgRx"],["@ngrx/router-store","8.6.0","MIT","git+https://github.com/ngrx/platform.git","https://github.com/ngrx/platform#readme","NgRx"],["@ngrx/store","8.6.0","MIT","git+https://github.com/ngrx/platform.git","https://github.com/ngrx/platform#readme","NgRx"],["@ngrx/store-devtools","8.6.0","MIT","git+https://github.com/ngrx/platform.git","https://github.com/ngrx/platform#readme","NgRx"],["@ngtools/webpack","8.3.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@nguniversal/express-engine","8.1.1","MIT","https://github.com/angular/universal","https://github.com/angular/universal","Unknown"],["@ngx-translate/core","11.0.1","MIT","git+https://github.com/ngx-translate/core.git","https://github.com/ngx-translate/core","Olivier Combe"],["@ngx-translate/http-loader","4.0.0","MIT","git+https://github.com/ngx-translate/http-loader.git","https://github.com/ngx-translate/http-loader","Olivier Combe"],["@nodelib/fs.scandir","2.1.3","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir","Unknown","Unknown"],["@nodelib/fs.stat","2.0.3","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat","Unknown","Unknown"],["@nodelib/fs.walk","1.2.4","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk","Unknown","Unknown"],["@nuxtjs/opencollective","0.2.2","MIT","git+https://github.com/nuxt-community/opencollective","Unknown","Unknown"],["@phenomnomnominal/tsquery","3.0.0","MIT","https://github.com/phenomnomnominal/tsquery","Unknown","Craig Spence"],["@samverschueren/stream-to-observable","0.3.1","MIT","https://github.com/SamVerschueren/stream-to-observable.git","Unknown","Unknown"],["@schematics/angular","8.3.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@schematics/update","0.803.6","MIT","https://github.com/angular/angular-cli.git","https://github.com/angular/angular-cli","Angular Authors"],["@sheerun/mutationobserver-shim","0.3.3","MIT","github.com/megawac/MutationObserver.js","Unknown","Unknown"],["@stomp/rx-stomp","0.3.4","MIT","git+https://github.com/stomp-js/stompjs.git","https://github.com/stomp-js/rx-stomp#readme","<EMAIL>"],["@stomp/stompjs","5.4.4","MIT","git+https://github.com/stomp-js/stompjs.git","https://github.com/stomp-js/stompjs#readme","<EMAIL>"],["@testing-library/dom","6.1.0","MIT","https://github.com/testing-library/dom-testing-library.git","https://github.com/testing-library/dom-testing-library#readme","Kent C. Dodds"],["@types/amqplib","0.5.13","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/bcryptjs","2.4.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/bluebird","3.5.32","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/body-parser","1.19.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/bson","4.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/caseless","0.12.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/cassandra-driver","4.1.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/color-name","1.1.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/connect","3.4.33","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/cookie-parser","1.4.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/cookiejar","2.1.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/cross-spawn","6.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3","5.7.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3","3.5.43","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-array","1.2.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-array","2.0.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-axis","1.0.12","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-brush","1.1.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-chord","1.0.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-collection","1.0.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-color","1.2.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-contour","1.3.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-dispatch","1.0.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-drag","1.2.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-dsv","1.0.36","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-ease","1.0.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-fetch","1.1.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-force","1.2.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-format","1.3.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-geo","1.11.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-hierarchy","1.1.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-interpolate","1.3.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-path","1.0.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-polygon","1.0.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-quadtree","1.0.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-random","1.1.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-scale","2.2.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-scale-chromatic","1.5.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-selection","1.4.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-shape","1.3.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-time","1.0.10","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-time-format","2.1.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-timer","1.0.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-transition","1.1.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-voronoi","1.1.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/d3-zoom","1.7.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/debug","4.1.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/decompress","4.2.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/dedent","0.7.0","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/del","3.0.1","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/errorhandler","0.0.32","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/express","4.17.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/express","4.17.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/express-serve-static-core","4.17.12","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/find-cache-dir","2.0.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/find-package-json","1.1.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/fuzzy-search","2.1.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/geojson","7946.0.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/get-port","4.2.0","MIT","https://github.com/sindresorhus/get-port","Unknown","Unknown"],["@types/glob","7.1.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/hammerjs","2.0.36","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/istanbul-lib-coverage","2.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/istanbul-lib-report","3.0.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/istanbul-reports","1.1.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/jasmine","3.4.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/jasmine","3.5.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/jasmine","3.5.12","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/js-yaml","3.12.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/json-schema","7.0.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/json5","0.0.29","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Jason Swearingen"],["@types/jsonwebtoken","8.3.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/jsonwebtoken","8.3.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/jsonwebtoken","8.5.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/libxmljs","0.18.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/lockfile","1.0.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/long","4.0.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/marked","0.6.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/md5-file","4.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/mime","2.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/minimatch","3.0.3","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/mkdirp","0.5.2","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/mongodb","3.3.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/mongodb","3.5.27","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/mongoose","5.5.20","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/mongoose","5.7.21","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/ms","0.7.31","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/multer","1.3.10","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/nock","9.3.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/node","10.12.17","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/node","13.7.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/node","10.17.13","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/node","14.6.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/node-fetch","2.5.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/passport","1.0.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/passport-jwt","3.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/passport-strategy","0.2.35","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/password-hash","1.2.20","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/plotly.js","1.44.33","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/qs","6.9.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/quill","1.3.6","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/range-parser","1.2.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/request","2.48.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/request-promise","4.1.44","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/request-promise-native","1.0.17","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/rimraf","2.0.2","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/serve-static","1.13.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/sinonjs__fake-timers","6.0.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/sizzle","2.3.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/source-list-map","0.1.2","MIT","https://www.github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/superagent","4.1.10","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/supertest","2.0.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/testing-library__dom","6.14.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/tmp","0.1.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/tough-cookie","4.0.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/ua-parser-js","0.7.33","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/uuid","3.4.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/uuid","3.4.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/validator","13.1.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/validator","13.0.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/validator","10.11.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/webpack-sources","0.1.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/xml2js","0.4.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/yargs","15.0.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/yargs","13.0.10","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@types/yargs-parser","15.0.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped.git","Unknown","Unknown"],["@webassemblyjs/ast","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/floating-point-hex-parser","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Mauro Bringolf"],["@webassemblyjs/helper-api-error","1.8.5","MIT","Unknown","Unknown","Sven Sauleau"],["@webassemblyjs/helper-buffer","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/helper-code-frame","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/helper-module-context","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/helper-wasm-bytecode","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/helper-wasm-section","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/ieee754","1.8.5","MIT","Unknown","Unknown","Unknown"],["@webassemblyjs/leb128","1.8.5","MIT","Unknown","Unknown","Unknown"],["@webassemblyjs/utf8","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/wasm-edit","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/wasm-gen","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/wasm-opt","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/wasm-parser","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/wast-parser","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@webassemblyjs/wast-printer","1.8.5","MIT","https://github.com/xtuc/webassemblyjs.git","Unknown","Sven Sauleau"],["@zxing/library","0.15.2","MIT","https://github.com/zxing-js/library","https://zxing-js.github.io/library/","Unknown"],["accepts","1.3.7","MIT","https://github.com/jshttp/accepts.git","Unknown","Unknown"],["acorn","6.4.1","MIT","https://github.com/acornjs/acorn.git","https://github.com/acornjs/acorn","Unknown"],["acorn","7.4.0","MIT","https://github.com/acornjs/acorn.git","https://github.com/acornjs/acorn","Unknown"],["acorn-walk","7.2.0","MIT","https://github.com/acornjs/acorn.git","https://github.com/acornjs/acorn","Unknown"],["after","0.8.2","MIT","git://github.com/Raynos/after.git","Unknown","Raynos"],["agent-base","4.3.0","MIT","git://github.com/TooTallNate/node-agent-base.git","http://n8.io/","Nathan Rajlich"],["agent-base","5.1.1","MIT","git://github.com/TooTallNate/node-agent-base.git","http://n8.io/","Nathan Rajlich"],["agent-base","4.2.1","MIT","git://github.com/TooTallNate/node-agent-base.git","http://n8.io/","Nathan Rajlich"],["agentkeepalive","3.5.2","MIT","git://github.com/node-modules/agentkeepalive.git","https://fengmk2.com","fengmk2"],["aggregate-error","3.1.0","MIT","https://github.com/sindresorhus/aggregate-error.git","sindresorhus.com","Sindre Sorhus"],["ajv","6.12.4","MIT","https://github.com/ajv-validator/ajv.git","https://github.com/ajv-validator/ajv","Evgeny Poberezkin"],["ajv","6.10.2","MIT","https://github.com/epoberezkin/ajv.git","https://github.com/epoberezkin/ajv","Evgeny Poberezkin"],["ajv","5.5.2","MIT","https://github.com/epoberezkin/ajv.git","https://github.com/epoberezkin/ajv","Evgeny Poberezkin"],["ajv-errors","1.0.1","MIT","git+https://github.com/epoberezkin/ajv-errors.git","https://github.com/epoberezkin/ajv-errors#readme","Unknown"],["ajv-keywords","3.5.2","MIT","git+https://github.com/epoberezkin/ajv-keywords.git","https://github.com/epoberezkin/ajv-keywords#readme","Evgeny Poberezkin"],["amqplib","0.5.5","MIT","https://github.com/squaremo/amqp.node.git","http://squaremo.github.io/amqp.node/","Michael Bridgen"],["angular2-uuid","1.1.1","MIT","git+https://github.com/wulfsolter/angular2-uuid.git","https://github.com/wulfsolter/angular2-uuid#readme","Wulf Solter"],["ansi-colors","4.1.1","MIT","https://github.com/doowb/ansi-colors.git","https://github.com/doowb/ansi-colors","Brian Woodward"],["ansi-colors","3.2.3","MIT","https://github.com/doowb/ansi-colors.git","https://github.com/doowb/ansi-colors","Brian Woodward"],["ansi-colors","3.2.4","MIT","https://github.com/doowb/ansi-colors.git","https://github.com/doowb/ansi-colors","Brian Woodward"],["ansi-escapes","1.4.0","MIT","https://github.com/sindresorhus/ansi-escapes.git","sindresorhus.com","Sindre Sorhus"],["ansi-escapes","4.3.1","MIT","https://github.com/sindresorhus/ansi-escapes.git","https://sindresorhus.com","Sindre Sorhus"],["ansi-escapes","3.2.0","MIT","https://github.com/sindresorhus/ansi-escapes.git","sindresorhus.com","Sindre Sorhus"],["ansi-regex","2.1.1","MIT","https://github.com/chalk/ansi-regex.git","sindresorhus.com","Sindre Sorhus"],["ansi-regex","3.0.0","MIT","https://github.com/chalk/ansi-regex.git","sindresorhus.com","Sindre Sorhus"],["ansi-regex","4.1.0","MIT","https://github.com/chalk/ansi-regex.git","sindresorhus.com","Sindre Sorhus"],["ansi-regex","5.0.0","MIT","https://github.com/chalk/ansi-regex.git","sindresorhus.com","Sindre Sorhus"],["ansi-styles","4.2.1","MIT","https://github.com/chalk/ansi-styles.git","sindresorhus.com","Sindre Sorhus"],["ansi-styles","3.2.1","MIT","https://github.com/chalk/ansi-styles.git","sindresorhus.com","Sindre Sorhus"],["ansi-styles","2.2.1","MIT","https://github.com/chalk/ansi-styles.git","sindresorhus.com","Sindre Sorhus"],["any-observable","0.3.0","MIT","https://github.com/sindresorhus/any-observable.git","sindresorhus.com","Sindre Sorhus"],["app-root-path","2.2.1","MIT","https://github.com/inxilpro/node-app-root-path.git","https://github.com/inxilpro/node-app-root-path","Chris Morrell"],["append-field","1.0.0","MIT","http://github.com/LinusU/node-append-field.git","Unknown","Linus Unnebäck"],["append-transform","2.0.0","MIT","https://github.com/istanbuljs/append-transform.git","github.com/jamestalmage","James Talmage"],["append-transform","1.0.0","MIT","https://github.com/istanbuljs/append-transform.git","github.com/jamestalmage","James Talmage"],["arch","2.1.2","MIT","git://github.com/feross/arch.git","https://github.com/feross/arch","Feross Aboukhadijeh"],["archy","1.0.0","MIT","http://github.com/substack/node-archy.git","http://substack.net","James Halliday"],["arg","4.1.3","MIT","https://github.com/zeit/arg.git","Unknown","Josh Junon"],["argparse","1.0.10","MIT","https://github.com/nodeca/argparse.git","Unknown","Unknown"],["arr-diff","4.0.0","MIT","https://github.com/jonschlinkert/arr-diff.git","https://github.com/jonschlinkert/arr-diff","Jon Schlinkert"],["arr-flatten","1.1.0","MIT","https://github.com/jonschlinkert/arr-flatten.git","https://github.com/jonschlinkert/arr-flatten","Jon Schlinkert"],["arr-union","3.1.0","MIT","https://github.com/jonschlinkert/arr-union.git","https://github.com/jonschlinkert/arr-union","Jon Schlinkert"],["array-find-index","1.0.2","MIT","https://github.com/sindresorhus/array-find-index.git","sindresorhus.com","Sindre Sorhus"],["array-flatten","1.1.1","MIT","git://github.com/blakeembrey/array-flatten.git","https://github.com/blakeembrey/array-flatten","Blake Embrey"],["array-flatten","2.1.2","MIT","git://github.com/blakeembrey/array-flatten.git","https://github.com/blakeembrey/array-flatten","Blake Embrey"],["array-union","1.0.2","MIT","https://github.com/sindresorhus/array-union.git","sindresorhus.com","Sindre Sorhus"],["array-union","2.1.0","MIT","https://github.com/sindresorhus/array-union.git","sindresorhus.com","Sindre Sorhus"],["array-uniq","1.0.3","MIT","https://github.com/sindresorhus/array-uniq.git","sindresorhus.com","Sindre Sorhus"],["array-unique","0.3.2","MIT","https://github.com/jonschlinkert/array-unique.git","https://github.com/jonschlinkert/array-unique","Jon Schlinkert"],["arraybuffer.slice","0.0.7","MIT","**************:rase-/arraybuffer.slice.git","https://github.com/rase-/arraybuffer.slice","Unknown"],["asap","2.0.6","MIT","https://github.com/kriskowal/asap.git","Unknown","Unknown"],["asn1","0.2.4","MIT","git://github.com/joyent/node-asn1.git","joyent.com","Joyent"],["asn1.js","5.4.1","MIT","**************:indutny/asn1.js","https://github.com/indutny/asn1.js","Fedor Indutny"],["assert","1.5.0","MIT","git://github.com/browserify/commonjs-assert.git","https://github.com/browserify/commonjs-assert","Unknown"],["assert-plus","1.0.0","MIT","https://github.com/mcavage/node-assert-plus.git","Unknown","Mark Cavage"],["assertion-error","1.1.0","MIT","**************:chaijs/assertion-error.git","http://qualiancy.com","Jake Luer"],["assign-symbols","1.0.0","MIT","https://github.com/jonschlinkert/assign-symbols.git","https://github.com/jonschlinkert/assign-symbols","Jon Schlinkert"],["async","2.6.3","MIT","https://github.com/caolan/async.git","https://caolan.github.io/async/","Caolan McMahon"],["async","1.5.2","MIT","https://github.com/caolan/async.git","Unknown","Caolan McMahon"],["async","3.2.0","MIT","https://github.com/caolan/async.git","https://caolan.github.io/async/","Caolan McMahon"],["async-each","1.0.3","MIT","git://github.com/paulmillr/async-each.git","https://github.com/paulmillr/async-each/","Paul Miller"],["async-limiter","1.0.1","MIT","https://github.com/strml/async-limiter.git","Unknown","Samuel Reed"],["asynckit","0.4.0","MIT","git+https://github.com/alexindigo/asynckit.git","https://github.com/alexindigo/asynckit#readme","Alex Indigo"],["autoprefixer","9.6.1","MIT","https://github.com/postcss/autoprefixer.git","Unknown","Andrey Sitnik"],["aws4","1.10.1","MIT","https://github.com/mhart/aws4.git","https://github.com/mhart","Michael Hart"],["axios","0.19.0","MIT","https://github.com/axios/axios.git","https://github.com/axios/axios","Matt Zabriskie"],["babel-code-frame","6.26.0","MIT","https://github.com/babel/babel/tree/master/packages/babel-code-frame","https://babeljs.io/","Sebastian McKenzie"],["babel-generator","6.26.1","MIT","https://github.com/babel/babel/tree/master/packages/babel-generator","https://babeljs.io/","Sebastian McKenzie"],["babel-loader","8.1.0","MIT","https://github.com/babel/babel-loader.git","https://github.com/babel/babel-loader","Luis Couto"],["babel-messages","6.23.0","MIT","https://github.com/babel/babel/tree/master/packages/babel-messages","https://babeljs.io/","Sebastian McKenzie"],["babel-plugin-dynamic-import-node","2.3.3","MIT","git+https://github.com/airbnb/babel-plugin-dynamic-import-node.git","https://github.com/airbnb/babel-plugin-dynamic-import-node#readme","Jordan Gensler"],["babel-runtime","6.26.0","MIT","https://github.com/babel/babel/tree/master/packages/babel-runtime","Unknown","Sebastian McKenzie"],["babel-template","6.26.0","MIT","https://github.com/babel/babel/tree/master/packages/babel-template","https://babeljs.io/","Sebastian McKenzie"],["babel-traverse","6.26.0","MIT","https://github.com/babel/babel/tree/master/packages/babel-traverse","https://babeljs.io/","Sebastian McKenzie"],["babel-types","6.26.0","MIT","https://github.com/babel/babel/tree/master/packages/babel-types","https://babeljs.io/","Sebastian McKenzie"],["babylon","6.18.0","MIT","https://github.com/babel/babylon","https://babeljs.io/","Sebastian McKenzie"],["backo2","1.0.2","MIT","https://github.com/mokesmokes/backo.git","Unknown","Unknown"],["balanced-match","1.0.0","MIT","git://github.com/juliangruber/balanced-match.git","https://github.com/juliangruber/balanced-match","Julian Gruber"],["base","0.11.2","MIT","https://github.com/node-base/base.git","https://github.com/node-base/base","Jon Schlinkert"],["base64-arraybuffer","0.1.5","MIT","https://github.com/niklasvh/base64-arraybuffer","https://github.com/niklasvh/base64-arraybuffer","Niklas von Hertzen"],["base64-js","1.3.1","MIT","git://github.com/beatgammit/base64-js.git","https://github.com/beatgammit/base64-js","T. Jameson Little"],["base64id","1.0.0","MIT","https://github.com/faeldt/base64id.git","Unknown","Kristian Faeldt"],["batch","0.6.1","MIT","https://github.com/visionmedia/batch.git","Unknown","TJ Holowaychuk"],["bcryptjs","2.4.3","MIT","https://github.com/dcodeIO/bcrypt.js.git","Unknown","Daniel Wirtz"],["better-assert","1.0.2","MIT","https://github.com/visionmedia/better-assert.git","Unknown","TJ Holowaychuk"],["bfj","6.1.2","MIT","https://gitlab.com/philbooth/bfj.git","https://gitlab.com/philbooth/bfj","Phil Booth"],["big.js","5.2.2","MIT","https://github.com/MikeMcl/big.js.git","Unknown","Michael Mclaughlin"],["binary-extensions","2.1.0","MIT","https://github.com/sindresorhus/binary-extensions.git","sindresorhus.com","Sindre Sorhus"],["binary-extensions","1.13.1","MIT","https://github.com/sindresorhus/binary-extensions.git","sindresorhus.com","Sindre Sorhus"],["bindings","1.3.1","MIT","git://github.com/TooTallNate/node-bindings.git","https://github.com/TooTallNate/node-bindings","Nathan Rajlich"],["bindings","1.5.0","MIT","git://github.com/TooTallNate/node-bindings.git","https://github.com/TooTallNate/node-bindings","Nathan Rajlich"],["bitsyntax","0.1.0","MIT","git://github.com/squaremo/bitsyntax-js.git","Unknown","Michael Bridgen"],["bl","2.2.1","MIT","https://github.com/rvagg/bl.git","https://github.com/rvagg/bl","Unknown"],["bl","4.0.3","MIT","https://github.com/rvagg/bl.git","https://github.com/rvagg/bl","Unknown"],["bl","1.2.3","MIT","https://github.com/rvagg/bl.git","https://github.com/rvagg/bl","Unknown"],["blob","0.0.5","MIT","git://github.com/webmodules/blob.git","https://github.com/webmodules/blob","Unknown"],["bluebird","3.7.2","MIT","git://github.com/petkaantonov/bluebird.git","https://github.com/petkaantonov/bluebird","Petka Antonov"],["bluebird","3.7.1","MIT","git://github.com/petkaantonov/bluebird.git","https://github.com/petkaantonov/bluebird","Petka Antonov"],["bluebird","3.5.1","MIT","git://github.com/petkaantonov/bluebird.git","https://github.com/petkaantonov/bluebird","Petka Antonov"],["bn.js","5.1.3","MIT","**************:indutny/bn.js","https://github.com/indutny/bn.js","Fedor Indutny"],["bn.js","4.11.9","MIT","**************:indutny/bn.js","https://github.com/indutny/bn.js","Fedor Indutny"],["body-parser","1.19.0","MIT","https://github.com/expressjs/body-parser.git","Unknown","Unknown"],["bonjour","3.5.0","MIT","https://github.com/watson/bonjour.git","https://github.com/watson/bonjour","Thomas Watson Steen"],["bootstrap","4.3.1","MIT","git+https://github.com/twbs/bootstrap.git","https://getbootstrap.com/","The Bootstrap Authors"],["boxen","4.2.0","MIT","https://github.com/sindresorhus/boxen.git","sindresorhus.com","Sindre Sorhus"],["boxen","1.3.0","MIT","https://github.com/sindresorhus/boxen.git","sindresorhus.com","Sindre Sorhus"],["brace-expansion","1.1.11","MIT","git://github.com/juliangruber/brace-expansion.git","https://github.com/juliangruber/brace-expansion","Julian Gruber"],["braces","3.0.2","MIT","https://github.com/micromatch/braces.git","https://github.com/micromatch/braces","Jon Schlinkert"],["braces","2.3.2","MIT","https://github.com/micromatch/braces.git","https://github.com/micromatch/braces","Jon Schlinkert"],["brorand","1.1.0","MIT","**************:indutny/brorand","https://github.com/indutny/brorand","Fedor Indutny"],["browser-detect","0.2.28","MIT","https://github.com/KennethanCeyer/browser-detect.git","https://github.com/KennethanCeyer/browser-detect","PIGNOSE"],["browserify-aes","1.2.0","MIT","git://github.com/crypto-browserify/browserify-aes.git","https://github.com/crypto-browserify/browserify-aes","Unknown"],["browserify-cipher","1.0.1","MIT","**************:crypto-browserify/browserify-cipher.git","Unknown","Calvin Metcalf"],["browserify-des","1.0.2","MIT","git+https://github.com/crypto-browserify/browserify-des.git","https://github.com/crypto-browserify/browserify-des#readme","Calvin Metcalf"],["browserify-rsa","4.0.1","MIT","**************:crypto-browserify/browserify-rsa.git","Unknown","Unknown"],["browserify-zlib","0.2.0","MIT","git+https://github.com/devongovett/browserify-zlib.git","https://github.com/devongovett/browserify-zlib","Devon Govett"],["browserslist","4.6.6","MIT","https://github.com/browserslist/browserslist.git","Unknown","Andrey Sitnik"],["browserslist","4.14.1","MIT","https://github.com/browserslist/browserslist.git","Unknown","Andrey Sitnik"],["buffer","4.9.2","MIT","git://github.com/feross/buffer.git","https://github.com/feross/buffer","Feross Aboukhadijeh"],["buffer","5.6.0","MIT","git://github.com/feross/buffer.git","https://github.com/feross/buffer","Feross Aboukhadijeh"],["buffer-alloc","1.2.0","MIT","https://github.com/LinusU/buffer-alloc.git","Unknown","Unknown"],["buffer-alloc-unsafe","1.1.0","MIT","https://github.com/LinusU/buffer-alloc-unsafe.git","Unknown","Unknown"],["buffer-crc32","0.2.13","MIT","git://github.com/brianloveswords/buffer-crc32.git","https://github.com/brianloveswords/buffer-crc32","Brian J. Brennan"],["buffer-fill","1.0.0","MIT","https://github.com/LinusU/buffer-fill.git","Unknown","Unknown"],["buffer-from","1.1.1","MIT","https://github.com/LinusU/buffer-from.git","Unknown","Unknown"],["buffer-indexof","1.1.1","MIT","git://github.com/soldair/node-buffer-indexof.git","Unknown","Ryan Day"],["buffer-more-ints","1.0.0","MIT","https://github.com/dpw/node-buffer-more-ints.git","https://github.com/dpw/node-buffer-more-ints","David Wragg"],["buffer-xor","1.0.3","MIT","https://github.com/crypto-browserify/buffer-xor.git","https://github.com/crypto-browserify/buffer-xor","Daniel Cousens"],["builtin-modules","1.1.1","MIT","https://github.com/sindresorhus/builtin-modules.git","sindresorhus.com","Sindre Sorhus"],["builtin-status-codes","3.0.0","MIT","https://github.com/bendrucker/builtin-status-codes.git","bendrucker.me","Ben Drucker"],["builtins","1.0.3","MIT","https://github.com/juliangruber/builtins.git","Unknown","Unknown"],["busboy","0.2.14","MIT","http://github.com/mscdex/busboy.git","Unknown","Brian White"],["bwip-js","2.0.9","MIT","https://github.com/metafloor/bwip-js.git","https://github.com/metafloor/bwip-js","Mark Warren"],["byline","5.0.0","MIT","https://github.com/jahewson/node-byline","https://github.com/jahewson/node-byline","John Hewson"],["bytes","3.1.0","MIT","https://github.com/visionmedia/bytes.js.git","http://tjholowaychuk.com","TJ Holowaychuk"],["bytes","3.0.0","MIT","https://github.com/visionmedia/bytes.js.git","http://tjholowaychuk.com","TJ Holowaychuk"],["cache-base","1.0.1","MIT","https://github.com/jonschlinkert/cache-base.git","https://github.com/jonschlinkert/cache-base","Jon Schlinkert"],["cachedir","2.3.0","MIT","https://github.com/LinusU/node-cachedir.git","Unknown","Linus Unnebäck"],["caching-transform","4.0.0","MIT","https://github.com/istanbuljs/caching-transform.git","Unknown","Unknown"],["callback-stream","1.1.0","MIT","https://github.com/mcollina/callback-stream.git","Unknown","Matteo Collina"],["caller-callsite","2.0.0","MIT","https://github.com/sindresorhus/caller-callsite.git","sindresorhus.com","Sindre Sorhus"],["caller-path","2.0.0","MIT","https://github.com/sindresorhus/caller-path.git","sindresorhus.com","Sindre Sorhus"],["callsites","2.0.0","MIT","https://github.com/sindresorhus/callsites.git","sindresorhus.com","Sindre Sorhus"],["camelcase","5.3.1","MIT","https://github.com/sindresorhus/camelcase.git","sindresorhus.com","Sindre Sorhus"],["camelcase","4.1.0","MIT","https://github.com/sindresorhus/camelcase.git","sindresorhus.com","Sindre Sorhus"],["camelcase","2.1.1","MIT","https://github.com/sindresorhus/camelcase.git","http://sindresorhus.com","Sindre Sorhus"],["camelcase","3.0.0","MIT","https://github.com/sindresorhus/camelcase.git","http://sindresorhus.com","Sindre Sorhus"],["camelcase-keys","2.1.0","MIT","https://github.com/sindresorhus/camelcase-keys.git","http://sindresorhus.com","Sindre Sorhus"],["canonical-path","1.0.0","MIT","https://github.com/petebacondarwin/node-canonical-path","Unknown","Pete Bacon Darwin"],["capture-stack-trace","1.0.1","MIT","https://github.com/floatdrop/capture-stack-trace.git","github.com/floatdrop","Vsevolod Strukchinsky"],["chai","4.2.0","MIT","https://github.com/chaijs/chai","http://chaijs.com/","Jake Luer"],["chalk","4.0.0","MIT","https://github.com/chalk/chalk.git","Unknown","Unknown"],["chalk","2.4.2","MIT","https://github.com/chalk/chalk.git","Unknown","Unknown"],["chalk","3.0.0","MIT","https://github.com/chalk/chalk.git","Unknown","Unknown"],["chalk","1.1.3","MIT","https://github.com/chalk/chalk.git","Unknown","Unknown"],["chardet","0.7.0","MIT","**************:runk/node-chardet.git","https://github.com/runk/node-chardet","Dmitry Shirokov"],["check-error","1.0.2","MIT","git+ssh://**************/chaijs/check-error.git","http://alogicalparadox.com","Jake Luer"],["check-more-types","2.24.0","MIT","https://github.com/kensho/check-more-types.git","https://github.com/kensho/check-more-types","Gleb Bahmutov"],["check-types","8.0.3","MIT","https://gitlab.com/philbooth/check-types.js.git","https://gitlab.com/philbooth/check-types.js","Phil Booth"],["chokidar","3.4.2","MIT","git+https://github.com/paulmillr/chokidar.git","https://github.com/paulmillr/chokidar","Paul Miller"],["chokidar","2.1.8","MIT","https://github.com/paulmillr/chokidar.git","https://github.com/paulmillr/chokidar","Paul Miller"],["chrome-trace-event","1.0.2","MIT","github.com:samccone/chrome-trace-event","Unknown","Trent Mick, Sam Saccone"],["ci-info","2.0.0","MIT","https://github.com/watson/ci-info.git","https://github.com/watson/ci-info","Thomas Watson Steen"],["ci-info","1.6.0","MIT","https://github.com/watson/ci-info.git","https://github.com/watson/ci-info","Thomas Watson Steen"],["cipher-base","1.0.4","MIT","git+https://github.com/crypto-browserify/cipher-base.git","https://github.com/crypto-browserify/cipher-base#readme","Calvin Metcalf"],["class-transformer","0.2.3","MIT","https://github.com/pleerock/class-transformer.git","Unknown","Umed Khudoiberdiev"],["class-utils","0.3.6","MIT","https://github.com/jonschlinkert/class-utils.git","https://github.com/jonschlinkert/class-utils","Jon Schlinkert"],["class-validator","0.12.2","MIT","https://github.com/typestack/class-validator.git","Unknown","Umed Khudoiberdiev"],["class-validator","0.10.2","MIT","https://github.com/typestack/class-validator.git","Unknown","Umed Khudoiberdiev"],["clean-css","4.2.1","MIT","https://github.com/jakubpawlowicz/clean-css.git","https://github.com/jakubpawlowicz/clean-css","Jakub Pawlowicz"],["clean-stack","2.2.0","MIT","https://github.com/sindresorhus/clean-stack.git","sindresorhus.com","Sindre Sorhus"],["cli-boxes","2.2.1","MIT","https://github.com/sindresorhus/cli-boxes.git","https://sindresorhus.com","Sindre Sorhus"],["cli-boxes","1.0.0","MIT","https://github.com/sindresorhus/cli-boxes.git","sindresorhus.com","Sindre Sorhus"],["cli-cursor","1.0.2","MIT","https://github.com/sindresorhus/cli-cursor.git","sindresorhus.com","Sindre Sorhus"],["cli-cursor","3.1.0","MIT","https://github.com/sindresorhus/cli-cursor.git","sindresorhus.com","Sindre Sorhus"],["cli-cursor","2.1.0","MIT","https://github.com/sindresorhus/cli-cursor.git","sindresorhus.com","Sindre Sorhus"],["cli-table3","0.5.1","MIT","https://github.com/cli-table/cli-table3.git","https://github.com/cli-table/cli-table3","James Talmage"],["cli-truncate","0.2.1","MIT","https://github.com/sindresorhus/cli-truncate.git","sindresorhus.com","Sindre Sorhus"],["clipboard","2.0.6","MIT","https://github.com/zenorocha/clipboard.js.git","Unknown","Unknown"],["clone","2.1.2","MIT","git://github.com/pvorb/node-clone.git","http://paul.vorba.ch/","Paul Vorbach"],["clone-deep","4.0.1","MIT","https://github.com/jonschlinkert/clone-deep.git","https://github.com/jonschlinkert/clone-deep","Jon Schlinkert"],["co","4.6.0","MIT","https://github.com/tj/co.git","Unknown","Unknown"],["code-point-at","1.1.0","MIT","https://github.com/sindresorhus/code-point-at.git","sindresorhus.com","Sindre Sorhus"],["codelyzer","5.1.1","MIT","git+https://github.com/mgechev/codelyzer.git","https://github.com/mgechev/codelyzer#readme","Minko Gechev"],["collection-visit","1.0.0","MIT","https://github.com/jonschlinkert/collection-visit.git","https://github.com/jonschlinkert/collection-visit","Jon Schlinkert"],["color","3.0.0","MIT","https://github.com/Qix-/color.git","Unknown","Unknown"],["color-convert","2.0.1","MIT","https://github.com/Qix-/color-convert.git","Unknown","Heather Arthur"],["color-convert","1.9.3","MIT","https://github.com/Qix-/color-convert.git","Unknown","Heather Arthur"],["color-name","1.1.4","MIT","**************:colorjs/color-name.git","https://github.com/colorjs/color-name","DY"],["color-name","1.1.3","MIT","**************:dfcreative/color-name.git","https://github.com/dfcreative/color-name","DY"],["color-string","1.5.3","MIT","https://github.com/Qix-/color-string.git","Unknown","Heather Arthur"],["colorette","1.2.1","MIT","https://github.com/jorgebucaran/colorette.git","https://github.com/jorgebucaran/colorette","Jorge Bucaran"],["colornames","1.1.1","MIT","git://github.com/timoxley/colornames.git","https://github.com/timoxley/colornames#readme","Tim Oxley"],["colors","1.4.0","MIT","http://github.com/Marak/colors.js.git","https://github.com/Marak/colors.js","Marak Squires"],["colors","1.1.2","MIT","http://github.com/Marak/colors.js.git","https://github.com/Marak/colors.js","Marak Squires"],["colorspace","1.1.2","MIT","https://github.com/3rd-Eden/colorspace","https://github.com/3rd-Eden/colorspace","Arnout Kazemier"],["combined-stream","1.0.8","MIT","git://github.com/felixge/node-combined-stream.git","https://github.com/felixge/node-combined-stream","Felix Geisendörfer"],["commander","2.20.3","MIT","https://github.com/tj/commander.js.git","Unknown","TJ Holowaychuk"],["commander","2.19.0","MIT","https://github.com/tj/commander.js.git","Unknown","TJ Holowaychuk"],["commander","5.0.0","MIT","https://github.com/tj/commander.js.git","Unknown","TJ Holowaychuk"],["commander","4.1.1","MIT","https://github.com/tj/commander.js.git","Unknown","TJ Holowaychuk"],["commander","2.17.1","MIT","https://github.com/tj/commander.js.git","Unknown","TJ Holowaychuk"],["commander","2.14.1","MIT","https://github.com/tj/commander.js.git","Unknown","TJ Holowaychuk"],["commist","1.1.0","MIT","https://github.com/mcollina/commist.git","https://github.com/mcollina/commist","Matteo Collina"],["common-tags","1.8.0","MIT","https://github.com/declandewet/common-tags","https://github.com/declandewet/common-tags","Declan de Wet"],["commondir","1.0.1","MIT","http://github.com/substack/node-commondir.git","http://substack.net","James Halliday"],["compare-versions","3.6.0","MIT","git+https://github.com/omichelsen/compare-versions.git","https://github.com/omichelsen/compare-versions#readme","Ole Michelsen"],["component-emitter","1.3.0","MIT","https://github.com/component/emitter.git","Unknown","Unknown"],["component-emitter","1.2.1","MIT","https://github.com/component/emitter.git","Unknown","Unknown"],["compressible","2.0.18","MIT","https://github.com/jshttp/compressible.git","Unknown","Unknown"],["compression","1.7.4","MIT","https://github.com/expressjs/compression.git","Unknown","Unknown"],["concat-map","0.0.1","MIT","git://github.com/substack/node-concat-map.git","http://substack.net","James Halliday"],["concat-stream","1.6.2","MIT","http://github.com/maxogden/concat-stream.git","Unknown","Max Ogden"],["concurrently","5.0.2","MIT","https://github.com/kimmobrunfeldt/concurrently.git","Unknown","Kimmo Brunfeldt"],["connect","3.7.0","MIT","https://github.com/senchalabs/connect.git","http://tjholowaychuk.com","TJ Holowaychuk"],["connect-history-api-fallback","1.6.0","MIT","http://github.com/bripkens/connect-history-api-fallback.git","http://bripkens.de","Ben Ripkens"],["consola","2.15.0","MIT","https://github.com/nuxt/consola.git","Unknown","Unknown"],["console-browserify","1.2.0","MIT","git://github.com/browserify/console-browserify.git","https://github.com/browserify/console-browserify","Raynos"],["constants-browserify","1.0.0","MIT","git://github.com/juliangruber/constants-browserify.git","https://github.com/juliangruber/constants-browserify","Julian Gruber"],["content-disposition","0.5.3","MIT","https://github.com/jshttp/content-disposition.git","Unknown","Douglas Christopher Wilson"],["content-type","1.0.4","MIT","https://github.com/jshttp/content-type.git","Unknown","Douglas Christopher Wilson"],["convert-source-map","1.7.0","MIT","git://github.com/thlorenz/convert-source-map.git","https://github.com/thlorenz/convert-source-map","Thorsten Lorenz"],["cookie","0.3.1","MIT","https://github.com/jshttp/cookie.git","Unknown","Roman Shtylman"],["cookie","0.4.0","MIT","https://github.com/jshttp/cookie.git","Unknown","Roman Shtylman"],["cookie-parser","1.4.4","MIT","https://github.com/expressjs/cookie-parser.git","http://tjholowaychuk.com","TJ Holowaychuk"],["cookie-signature","1.0.6","MIT","https://github.com/visionmedia/node-cookie-signature.git","Unknown","TJ Holowaychuk"],["cookiejar","2.1.2","MIT","https://github.com/bmeck/node-cookiejar.git","Unknown","bradleymeck"],["copy-descriptor","0.1.1","MIT","https://github.com/jonschlinkert/copy-descriptor.git","https://github.com/jonschlinkert/copy-descriptor","Jon Schlinkert"],["copy-webpack-plugin","5.0.4","MIT","https://github.com/webpack-contrib/copy-webpack-plugin.git","https://github.com/webpack-contrib/copy-webpack-plugin","Len Boyette"],["core-js","2.6.9","MIT","https://github.com/zloirock/core-js.git","Unknown","Unknown"],["core-js","2.6.11","MIT","https://github.com/zloirock/core-js.git","Unknown","Unknown"],["core-js","3.2.1","MIT","https://github.com/zloirock/core-js.git","Unknown","Unknown"],["core-js","3.6.5","MIT","https://github.com/zloirock/core-js.git","Unknown","Unknown"],["core-js-compat","3.6.5","MIT","https://github.com/zloirock/core-js.git","Unknown","Unknown"],["core-js-pure","3.6.5","MIT","https://github.com/zloirock/core-js.git","Unknown","Unknown"],["core-util-is","1.0.2","MIT","git://github.com/isaacs/core-util-is","http://blog.izs.me/","Isaac Z. Schlueter"],["cors","2.8.5","MIT","https://github.com/expressjs/cors.git","https://github.com/troygoode/","Troy Goode"],["cosmiconfig","5.2.1","MIT","git+https://github.com/davidtheclark/cosmiconfig.git","https://github.com/davidtheclark/cosmiconfig#readme","David Clark"],["create-ecdh","4.0.4","MIT","https://github.com/crypto-browserify/createECDH.git","https://github.com/crypto-browserify/createECDH","Calvin Metcalf"],["create-error-class","3.0.2","MIT","https://github.com/floatdrop/create-error-class.git","github.com/floatdrop","Vsevolod Strukchinsky"],["create-hash","1.2.0","MIT","**************:crypto-browserify/createHash.git","https://github.com/crypto-browserify/createHash","Unknown"],["create-hmac","1.1.7","MIT","https://github.com/crypto-browserify/createHmac.git","https://github.com/crypto-browserify/createHmac","Unknown"],["cross-spawn","3.0.1","MIT","git://github.com/IndigoUnited/node-cross-spawn.git","http://indigounited.com","IndigoUnited"],["cross-spawn","7.0.3","MIT","**************:moxystudio/node-cross-spawn.git","https://github.com/moxystudio/node-cross-spawn","André Cruz"],["cross-spawn","6.0.5","MIT","**************:moxystudio/node-cross-spawn.git","https://github.com/moxystudio/node-cross-spawn","André Cruz"],["cross-spawn","5.1.0","MIT","git://github.com/IndigoUnited/node-cross-spawn.git","http://indigounited.com","IndigoUnited"],["crypto-browserify","3.12.0","MIT","git://github.com/crypto-browserify/crypto-browserify.git","https://github.com/crypto-browserify/crypto-browserify","Dominic Tarr"],["crypto-random-string","1.0.0","MIT","https://github.com/sindresorhus/crypto-random-string.git","sindresorhus.com","Sindre Sorhus"],["css-parse","1.7.0","MIT","https://github.com/visionmedia/css-parse.git","Unknown","TJ Holowaychuk"],["css-selector-tokenizer","0.7.3","MIT","https://github.com/css-modules/css-selector-tokenizer.git","https://github.com/css-modules/css-selector-tokenizer","Tobias Koppers @sokra"],["cssauron","1.4.0","MIT","git://github.com/chrisdickinson/cssauron.git","Unknown","Chris Dickinson"],["cssesc","3.0.0","MIT","https://github.com/mathiasbynens/cssesc.git","https://mths.be/cssesc","Mathias Bynens"],["currently-unhandled","0.4.1","MIT","https://github.com/jamestalmage/currently-unhandled.git","github.com/jamestalmage","James Talmage"],["custom-event","1.0.1","MIT","git://github.com/webmodules/custom-event.git","https://github.com/webmodules/custom-event","Nathan Rajlich"],["cyclist","1.0.1","MIT","git://github.com/mafintosh/cyclist","https://github.com/mafintosh/cyclist","Mathias Buus Madsen"],["cypress","4.10.0","MIT","https://github.com/cypress-io/cypress.git","https://github.com/cypress-io/cypress","Brian Mann"],["cypress-file-upload","4.0.4","MIT","git+https://github.com/abramenal/cypress-file-upload.git","https://github.com/abramenal/cypress-file-upload#readme","abramenal"],["cypress-multi-reporters","1.2.4","MIT","https://github.com/you54f/cypress-multi-reporters","Unknown","Stanley Ng"],["cypress-plugin-retries","1.5.2","MIT","git://github.com/Bkucera/cypress-plugin-retries.git","Unknown","Ben Kucera"],["dashdash","1.14.1","MIT","git://github.com/trentm/node-dashdash.git","http://trentm.com","Trent Mick"],["date-fns","2.16.1","MIT","https://github.com/date-fns/date-fns","Unknown","Unknown"],["date-fns","1.30.1","MIT","https://github.com/date-fns/date-fns","Unknown","Sasha Koss"],["date-format","2.1.0","MIT","https://github.com/nomiddlename/date-format.git","Unknown","Gareth Jones"],["dateformat","3.0.3","MIT","https://github.com/felixge/node-dateformat.git","https://github.com/felixge/node-dateformat","Steven Levithan"],["debug","4.1.1","MIT","git://github.com/visionmedia/debug.git","Unknown","TJ Holowaychuk"],["debug","3.2.6","MIT","git://github.com/visionmedia/debug.git","Unknown","TJ Holowaychuk"],["debug","2.6.9","MIT","git://github.com/visionmedia/debug.git","Unknown","TJ Holowaychuk"],["debug","3.1.0","MIT","git://github.com/visionmedia/debug.git","Unknown","TJ Holowaychuk"],["debuglog","1.0.1","MIT","https://github.com/sam-github/node-debuglog.git","Unknown","Sam Roberts"],["decamelize","1.2.0","MIT","https://github.com/sindresorhus/decamelize.git","sindresorhus.com","Sindre Sorhus"],["decamelize","3.2.0","MIT","https://github.com/sindresorhus/decamelize.git","sindresorhus.com","Sindre Sorhus"],["decode-uri-component","0.2.0","MIT","https://github.com/SamVerschueren/decode-uri-component.git","github.com/SamVerschueren","Sam Verschueren"],["decompress","4.2.1","MIT","https://github.com/kevva/decompress.git","github.com/kevva","Kevin Mårtensson"],["decompress-tar","4.1.1","MIT","https://github.com/kevva/decompress-tar.git","https://github.com/kevva","Kevin Mårtensson"],["decompress-tarbz2","4.1.1","MIT","https://github.com/kevva/decompress-tarbz2.git","github.com/kevva","Kevin Mårtensson"],["decompress-targz","4.1.1","MIT","https://github.com/kevva/decompress-targz.git","https://github.com/kevva","Kevin Mårtensson"],["decompress-unzip","4.0.1","MIT","https://github.com/kevva/decompress-unzip.git","https://github.com/kevva","Kevin Mårtensson"],["dedent","0.7.0","MIT","git://github.com/dmnd/dedent.git","https://github.com/dmnd/dedent","Desmond Brand"],["deep-eql","3.0.1","MIT","**************:chaijs/deep-eql.git","Unknown","Jake Luer"],["deep-equal","1.1.1","MIT","http://github.com/substack/node-deep-equal.git","http://substack.net","James Halliday"],["deep-extend","0.6.0","MIT","git://github.com/unclechu/node-deep-extend.git","https://github.com/unclechu/node-deep-extend","Viacheslav Lotsmanov"],["deep-is","0.1.3","MIT","http://github.com/thlorenz/deep-is.git","http://thlorenz.com","Thorsten Lorenz"],["deepmerge","3.3.0","MIT","git://github.com/TehShrike/deepmerge.git","https://github.com/TehShrike/deepmerge","Unknown"],["default-require-extensions","3.0.0","MIT","https://github.com/avajs/default-require-extensions.git","Unknown","Unknown"],["default-require-extensions","2.0.0","MIT","https://github.com/avajs/default-require-extensions.git","github.com/jamestalmage","James Talmage"],["define-properties","1.1.3","MIT","git://github.com/ljharb/define-properties.git","Unknown","Jordan Harband"],["define-property","2.0.2","MIT","https://github.com/jonschlinkert/define-property.git","https://github.com/jonschlinkert/define-property","Jon Schlinkert"],["define-property","1.0.0","MIT","https://github.com/jonschlinkert/define-property.git","https://github.com/jonschlinkert/define-property","Jon Schlinkert"],["define-property","0.2.5","MIT","https://github.com/jonschlinkert/define-property.git","https://github.com/jonschlinkert/define-property","Jon Schlinkert"],["del","3.0.0","MIT","https://github.com/sindresorhus/del.git","sindresorhus.com","Sindre Sorhus"],["del","4.1.1","MIT","https://github.com/sindresorhus/del.git","sindresorhus.com","Sindre Sorhus"],["delayed-stream","1.0.0","MIT","git://github.com/felixge/node-delayed-stream.git","https://github.com/felixge/node-delayed-stream","Felix Geisendörfer"],["delegate","3.2.0","MIT","https://github.com/zenorocha/delegate.git","Unknown","Unknown"],["delegates","1.0.0","MIT","https://github.com/visionmedia/node-delegates.git","Unknown","Unknown"],["depd","1.1.2","MIT","https://github.com/dougwilson/nodejs-depd.git","Unknown","Douglas Christopher Wilson"],["dependency-graph","0.7.2","MIT","git://github.com/jriecken/dependency-graph.git","Unknown","Jim Riecken"],["des.js","1.0.1","MIT","git+ssh://**************/indutny/des.js.git","https://github.com/indutny/des.js#readme","Fedor Indutny"],["destroy","1.0.4","MIT","https://github.com/stream-utils/destroy.git","http://jongleberry.com","Jonathan Ong"],["detect-indent","4.0.0","MIT","https://github.com/sindresorhus/detect-indent.git","sindresorhus.com","Sindre Sorhus"],["di","0.0.1","MIT","git://github.com/vojtajina/node-di.git","Unknown","Vojta Jina"],["diagnostics","1.1.1","MIT","git://github.com/bigpipe/diagnostics.git","https://github.com/bigpipe/diagnostics","Arnout Kazemier"],["dicer","0.2.5","MIT","http://github.com/mscdex/dicer.git","Unknown","Brian White"],["diffie-hellman","5.0.3","MIT","https://github.com/crypto-browserify/diffie-hellman.git","https://github.com/crypto-browserify/diffie-hellman","Calvin Metcalf"],["dir-glob","3.0.1","MIT","https://github.com/kevva/dir-glob.git","github.com/kevva","Kevin Mårtensson"],["dir-glob","2.2.2","MIT","https://github.com/kevva/dir-glob.git","github.com/kevva","Kevin Mårtensson"],["dns-equal","1.0.0","MIT","git+https://github.com/watson/dns-equal.git","https://github.com/watson/dns-equal#readme","Thomas Watson Steen"],["dns-packet","1.3.1","MIT","https://github.com/mafintosh/dns-packet","https://github.com/mafintosh/dns-packet","Mathias Buus"],["dns-txt","2.0.2","MIT","https://github.com/watson/dns-txt.git","https://github.com/watson/dns-txt","Thomas Watson Steen"],["dom-serialize","2.2.1","MIT","git://github.com/webmodules/dom-serialize.git","https://github.com/webmodules/dom-serialize","Nathan Rajlich"],["domain-browser","1.2.0","MIT","https://github.com/bevry/domain-browser.git","https://github.com/bevry/domain-browser","2013+ Bevry Pty Ltd"],["dot-prop","4.2.1","MIT","https://github.com/sindresorhus/dot-prop.git","sindresorhus.com","Sindre Sorhus"],["duplexer","0.1.2","MIT","git://github.com/Raynos/duplexer.git","https://github.com/Raynos/duplexer","Raynos"],["duplexify","3.7.1","MIT","git://github.com/mafintosh/duplexify","https://github.com/mafintosh/duplexify","Mathias Buus"],["ecc-jsbn","0.1.2","MIT","https://github.com/quartzjer/ecc-jsbn.git","https://github.com/quartzjer/ecc-jsbn","Jeremie Miller"],["ee-first","1.1.1","MIT","https://github.com/jonathanong/ee-first.git","http://jongleberry.com","Jonathan Ong"],["elegant-spinner","1.0.1","MIT","https://github.com/sindresorhus/elegant-spinner.git","sindresorhus.com","Sindre Sorhus"],["elliptic","6.5.3","MIT","**************:indutny/elliptic","https://github.com/indutny/elliptic","Fedor Indutny"],["emoji-regex","8.0.0","MIT","https://github.com/mathiasbynens/emoji-regex.git","https://mths.be/emoji-regex","Mathias Bynens"],["emoji-regex","7.0.3","MIT","https://github.com/mathiasbynens/emoji-regex.git","https://mths.be/emoji-regex","Mathias Bynens"],["emojis-list","3.0.0","MIT","git+https://github.com/kikobeats/emojis-list.git","https://nidecoc.io/Kikobeats/emojis-list","Kiko Beats"],["emojis-list","2.1.0","MIT","git+https://github.com/kikobeats/emojis-list.git","https://github.com/Kikobeats/emojis-list","Kiko Beats"],["enabled","1.0.2","MIT","git://github.com/bigpipe/enabled.git","Unknown","Arnout Kazemier"],["encodeurl","1.0.2","MIT","https://github.com/pillarjs/encodeurl.git","Unknown","Unknown"],["encoding","0.1.13","MIT","https://github.com/andris9/encoding.git","Unknown","Andris Reinman"],["end-of-stream","1.4.4","MIT","git://github.com/mafintosh/end-of-stream.git","https://github.com/mafintosh/end-of-stream","Mathias Buus"],["engine.io","3.2.1","MIT","**************:socketio/engine.io.git","https://github.com/socketio/engine.io","Guillermo Rauch"],["engine.io-client","3.2.1","MIT","https://github.com/socketio/engine.io-client.git","https://github.com/socketio/engine.io-client","Unknown"],["engine.io-parser","2.1.3","MIT","**************:socketio/engine.io-parser.git","https://github.com/socketio/engine.io-parser","Unknown"],["enhanced-resolve","4.3.0","MIT","git://github.com/webpack/enhanced-resolve.git","http://github.com/webpack/enhanced-resolve","Tobias Koppers @sokra"],["enhanced-resolve","4.1.0","MIT","git://github.com/webpack/enhanced-resolve.git","http://github.com/webpack/enhanced-resolve","Tobias Koppers @sokra"],["ent","2.2.0","MIT","https://github.com/substack/node-ent.git","http://substack.net","James Halliday"],["env-variable","0.0.6","MIT","https://github.com/3rd-Eden/env-variable","https://github.com/3rd-Eden/env-variable","Arnout Kazemier"],["err-code","1.1.2","MIT","git://github.com/IndigoUnited/js-err-code.git","http://indigounited.com","IndigoUnited"],["errno","0.1.7","MIT","https://github.com/rvagg/node-errno.git","Unknown","Unknown"],["error-ex","1.3.2","MIT","https://github.com/qix-/node-error-ex.git","Unknown","Unknown"],["error-stack-parser","2.0.4","MIT","git://github.com/stacktracejs/error-stack-parser.git","https://www.stacktracejs.com/","Unknown"],["error-stack-parser","2.0.6","MIT","git://github.com/stacktracejs/error-stack-parser.git","https://www.stacktracejs.com/","Unknown"],["errorhandler","1.5.1","MIT","https://github.com/expressjs/errorhandler.git","Unknown","Unknown"],["es-abstract","1.17.6","MIT","git://github.com/ljharb/es-abstract.git","http://ljharb.codes","Jordan Harband"],["es-to-primitive","1.2.1","MIT","git://github.com/ljharb/es-to-primitive.git","Unknown","Jordan Harband"],["es6-error","4.1.1","MIT","https://github.com/bjyoungblood/es6-error.git","https://github.com/bjyoungblood/es6-error","Ben Youngblood"],["es6-iterator","2.0.3","MIT","git://github.com/medikoo/es6-iterator.git","http://www.medikoo.com/","Mariusz Nowak"],["es6-map","0.1.5","MIT","git://github.com/medikoo/es6-map.git","http://www.medikoo.com/","Mariusz Nowak"],["es6-promise","4.2.8","MIT","git://github.com/stefanpenner/es6-promise.git","https://github.com/stefanpenner/es6-promise","Yehuda Katz, Tom Dale, Stefan Penner and contributors"],["es6-promisify","5.0.0","MIT","https://github.com/digitaldesignlabs/es6-promisify.git","Unknown","Mike Hall"],["es6-set","0.1.5","MIT","git://github.com/medikoo/es6-set.git","http://www.medikoo.com/","Mariusz Nowak"],["es6-symbol","3.1.1","MIT","git://github.com/medikoo/es6-symbol.git","http://www.medikoo.com/","Mariusz Nowak"],["escalade","3.0.2","MIT","https://github.com/lukeed/escalade.git","https://lukeed.com","Luke Edwards"],["escape-html","1.0.3","MIT","https://github.com/component/escape-html.git","Unknown","Unknown"],["escape-string-regexp","1.0.5","MIT","https://github.com/sindresorhus/escape-string-regexp.git","sindresorhus.com","Sindre Sorhus"],["etag","1.8.1","MIT","https://github.com/jshttp/etag.git","Unknown","Unknown"],["event-emitter","0.3.5","MIT","git://github.com/medikoo/event-emitter.git","http://www.medikoo.com/","Mariusz Nowak"],["event-stream","3.3.4","MIT","git://github.com/dominictarr/event-stream.git","http://github.com/dominictarr/event-stream","Dominic Tarr"],["eventemitter2","6.4.2","MIT","git://github.com/hij1nx/EventEmitter2.git","Unknown","hij1nx"],["eventemitter3","2.0.3","MIT","git://github.com/primus/eventemitter3.git","Unknown","Arnout Kazemier"],["eventemitter3","4.0.7","MIT","git://github.com/primus/eventemitter3.git","Unknown","Arnout Kazemier"],["events","3.2.0","MIT","git://github.com/Gozala/events.git","http://jeditoolkit.com","Irakli Gozalishvili"],["eventsource","1.0.7","MIT","git://github.com/EventSource/eventsource.git","http://github.com/EventSource/eventsource","Aslak Hellesøy"],["evp_bytestokey","1.0.3","MIT","https://github.com/crypto-browserify/EVP_BytesToKey.git","https://github.com/crypto-browserify/EVP_BytesToKey","Calvin Metcalf"],["exec-sh","0.2.2","MIT","**************:tsertkov/exec-sh.git","Unknown","Aleksandr Tsertkov"],["execa","1.0.0","MIT","https://github.com/sindresorhus/execa.git","sindresorhus.com","Sindre Sorhus"],["execa","3.4.0","MIT","https://github.com/sindresorhus/execa.git","sindresorhus.com","Sindre Sorhus"],["execa","0.7.0","MIT","https://github.com/sindresorhus/execa.git","sindresorhus.com","Sindre Sorhus"],["executable","4.1.1","MIT","https://github.com/kevva/executable.git","https://github.com/kevva","Kevin Mårtensson"],["exit-hook","1.1.1","MIT","https://github.com/sindresorhus/exit-hook.git","http://sindresorhus.com","Sindre Sorhus"],["expand-brackets","2.1.4","MIT","https://github.com/jonschlinkert/expand-brackets.git","https://github.com/jonschlinkert/expand-brackets","Jon Schlinkert"],["express","4.17.1","MIT","https://github.com/expressjs/express.git","http://expressjs.com/","TJ Holowaychuk"],["extend","3.0.2","MIT","https://github.com/justmoon/node-extend.git","http://www.justmoon.net","Stefan Thomas"],["extend-shallow","3.0.2","MIT","https://github.com/jonschlinkert/extend-shallow.git","https://github.com/jonschlinkert/extend-shallow","Jon Schlinkert"],["extend-shallow","2.0.1","MIT","https://github.com/jonschlinkert/extend-shallow.git","https://github.com/jonschlinkert/extend-shallow","Jon Schlinkert"],["external-editor","3.1.0","MIT","git+https://github.com/mrkmg/node-external-editor.git","https://github.com/mrkmg/node-external-editor#readme","Kevin Gravier"],["extglob","2.0.4","MIT","https://github.com/micromatch/extglob.git","https://github.com/micromatch/extglob","Jon Schlinkert"],["extsprintf","1.3.0","MIT","git://github.com/davepacheco/node-extsprintf.git","Unknown","Unknown"],["extsprintf","1.4.0","MIT","git://github.com/davepacheco/node-extsprintf.git","Unknown","Unknown"],["fast-deep-equal","3.1.3","MIT","git+https://github.com/epoberezkin/fast-deep-equal.git","https://github.com/epoberezkin/fast-deep-equal#readme","Evgeny Poberezkin"],["fast-deep-equal","2.0.1","MIT","git+https://github.com/epoberezkin/fast-deep-equal.git","https://github.com/epoberezkin/fast-deep-equal#readme","Evgeny Poberezkin"],["fast-deep-equal","1.1.0","MIT","git+https://github.com/epoberezkin/fast-deep-equal.git","https://github.com/epoberezkin/fast-deep-equal#readme","Evgeny Poberezkin"],["fast-glob","3.2.4","MIT","https://github.com/mrmlnc/fast-glob.git","https://mrmlnc.com","Denis Malinochkin"],["fast-json-stable-stringify","2.1.0","MIT","git://github.com/epoberezkin/fast-json-stable-stringify.git","https://github.com/epoberezkin/fast-json-stable-stringify","James Halliday"],["fast-json-stable-stringify","2.0.0","MIT","git://github.com/epoberezkin/fast-json-stable-stringify.git","https://github.com/epoberezkin/fast-json-stable-stringify","James Halliday"],["fast-levenshtein","2.0.6","MIT","https://github.com/hiddentao/fast-levenshtein.git","http://www.hiddentao.com/","Ramesh Nair"],["fast-safe-stringify","2.0.7","MIT","git+https://github.com/davidmarkclements/fast-safe-stringify.git","https://github.com/davidmarkclements/fast-safe-stringify#readme","David Mark Clements"],["fastparse","1.1.2","MIT","https://github.com/webpack/fastparse.git","https://github.com/webpack/fastparse","Tobias Koppers @sokra"],["faye-websocket","0.10.0","MIT","git://github.com/faye/faye-websocket-node.git","http://github.com/faye/faye-websocket-node","James Coglan"],["fd-slicer","1.1.0","MIT","git://github.com/andrewrk/node-fd-slicer.git","Unknown","Andrew Kelley"],["fecha","4.2.0","MIT","https://<EMAIL>/taylorhakes/fecha.git","https://github.com/taylorhakes/fecha","Taylor Hakes"],["figures","1.7.0","MIT","https://github.com/sindresorhus/figures.git","sindresorhus.com","Sindre Sorhus"],["figures","3.2.0","MIT","https://github.com/sindresorhus/figures.git","https://sindresorhus.com","Sindre Sorhus"],["figures","2.0.0","MIT","https://github.com/sindresorhus/figures.git","sindresorhus.com","Sindre Sorhus"],["file-loader","4.2.0","MIT","https://github.com/webpack-contrib/file-loader.git","https://github.com/webpack-contrib/file-loader","Tobias Koppers @sokra"],["file-type","5.2.0","MIT","https://github.com/sindresorhus/file-type.git","sindresorhus.com","Sindre Sorhus"],["file-type","6.2.0","MIT","https://github.com/sindresorhus/file-type.git","sindresorhus.com","Sindre Sorhus"],["file-type","3.9.0","MIT","https://github.com/sindresorhus/file-type.git","sindresorhus.com","Sindre Sorhus"],["file-uri-to-path","1.0.0","MIT","git://github.com/TooTallNate/file-uri-to-path.git","https://github.com/TooTallNate/file-uri-to-path","Nathan Rajlich"],["fileset","2.0.3","MIT","git://github.com/mklabs/node-fileset.git","https://github.com/mklabs/node-fileset","mklabs"],["fill-range","7.0.1","MIT","https://github.com/jonschlinkert/fill-range.git","https://github.com/jonschlinkert/fill-range","Jon Schlinkert"],["fill-range","4.0.0","MIT","https://github.com/jonschlinkert/fill-range.git","https://github.com/jonschlinkert/fill-range","Jon Schlinkert"],["finalhandler","1.1.2","MIT","https://github.com/pillarjs/finalhandler.git","Unknown","Douglas Christopher Wilson"],["find-cache-dir","2.1.0","MIT","https://github.com/avajs/find-cache-dir.git","Unknown","Unknown"],["find-cache-dir","3.3.1","MIT","https://github.com/avajs/find-cache-dir.git","Unknown","Unknown"],["find-cache-dir","3.0.0","MIT","https://github.com/avajs/find-cache-dir.git","Unknown","Unknown"],["find-cache-dir","3.2.0","MIT","https://github.com/avajs/find-cache-dir.git","Unknown","Unknown"],["find-package-json","1.2.0","MIT","git+https://github.com/3rd-Eden/find-package-json.git","https://github.com/3rd-Eden/find-package-json#readme","Arnout Kazemier"],["find-process","1.4.3","MIT","git+https://github.com/yibn2008/find-process.git","https://github.com/yibn2008/find-process#readme","zoujie"],["find-up","3.0.0","MIT","https://github.com/sindresorhus/find-up.git","sindresorhus.com","Sindre Sorhus"],["find-up","4.1.0","MIT","https://github.com/sindresorhus/find-up.git","sindresorhus.com","Sindre Sorhus"],["find-up","1.1.2","MIT","https://github.com/sindresorhus/find-up.git","sindresorhus.com","Sindre Sorhus"],["flush-write-stream","1.1.1","MIT","https://github.com/mafintosh/flush-write-stream.git","https://github.com/mafintosh/flush-write-stream","Mathias Buus"],["follow-redirects","1.5.10","MIT","**************:follow-redirects/follow-redirects.git","https://github.com/follow-redirects/follow-redirects","Ruben Verborgh"],["follow-redirects","1.13.0","MIT","**************:follow-redirects/follow-redirects.git","https://github.com/follow-redirects/follow-redirects","Ruben Verborgh"],["for-in","1.0.2","MIT","https://github.com/jonschlinkert/for-in.git","https://github.com/jonschlinkert/for-in","Jon Schlinkert"],["form-data","2.3.3","MIT","git://github.com/form-data/form-data.git","http://debuggable.com/","Felix Geisendörfer"],["form-data","3.0.0","MIT","git://github.com/form-data/form-data.git","http://debuggable.com/","Felix Geisendörfer"],["form-data","2.5.1","MIT","git://github.com/form-data/form-data.git","http://debuggable.com/","Felix Geisendörfer"],["formidable","1.2.2","MIT","https://github.com/node-formidable/formidable.git","https://github.com/node-formidable/formidable","Unknown"],["forwarded","0.1.2","MIT","https://github.com/jshttp/forwarded.git","Unknown","Unknown"],["fragment-cache","0.2.1","MIT","https://github.com/jonschlinkert/fragment-cache.git","https://github.com/jonschlinkert/fragment-cache","Jon Schlinkert"],["fresh","0.5.2","MIT","https://github.com/jshttp/fresh.git","http://tjholowaychuk.com","TJ Holowaychuk"],["from","0.1.7","MIT","git://github.com/dominictarr/from.git","dominictarr.com","Dominic Tarr"],["from2","2.3.0","MIT","git://github.com/hughsk/from2","https://github.com/hughsk/from2","Hugh Kennedy"],["fromentries","1.2.1","MIT","git://github.com/feross/fromentries.git","https://github.com/feross/fromentries","Feross Aboukhadijeh"],["fs-constants","1.0.0","MIT","https://github.com/mafintosh/fs-constants.git","https://github.com/mafintosh/fs-constants","Mathias Buus"],["fs-extra","8.1.0","MIT","https://github.com/jprichardson/node-fs-extra","https://github.com/jprichardson/node-fs-extra","JP Richardson"],["fs-extra","7.0.1","MIT","https://github.com/jprichardson/node-fs-extra","https://github.com/jprichardson/node-fs-extra","JP Richardson"],["fsu","1.1.1","MIT","git+ssh://**************/velocityzen/fsu.git","https://github.com/velocityzen/fsu#readme","Alexey Novikov"],["function-bind","1.1.1","MIT","git://github.com/Raynos/function-bind.git","https://github.com/Raynos/function-bind","Raynos"],["gaze","1.1.3","MIT","https://github.com/shama/gaze.git","https://github.com/shama/gaze","Kyle Robinson Young"],["genfun","5.0.0","MIT","git://github.com/zkat/genfun","http://github.com/zkat/genfun","Kat Marchán"],["gensync","1.0.0-beta.1","MIT","Unknown","Unknown","Logan Smyth"],["get-func-name","2.0.0","MIT","git+ssh://**************/chaijs/get-func-name.git","http://alogicalparadox.com","Jake Luer"],["get-package-type","0.1.0","MIT","git+https://github.com/cfware/get-package-type.git","https://github.com/cfware/get-package-type#readme","Corey Farrell"],["get-port","5.0.0","MIT","https://github.com/sindresorhus/get-port.git","sindresorhus.com","Sindre Sorhus"],["get-port","5.1.1","MIT","https://github.com/sindresorhus/get-port.git","sindresorhus.com","Sindre Sorhus"],["get-stdin","4.0.1","MIT","https://github.com/sindresorhus/get-stdin.git","http://sindresorhus.com","Sindre Sorhus"],["get-stream","4.1.0","MIT","https://github.com/sindresorhus/get-stream.git","sindresorhus.com","Sindre Sorhus"],["get-stream","5.2.0","MIT","https://github.com/sindresorhus/get-stream.git","https://sindresorhus.com","Sindre Sorhus"],["get-stream","2.3.1","MIT","https://github.com/sindresorhus/get-stream.git","sindresorhus.com","Sindre Sorhus"],["get-stream","3.0.0","MIT","https://github.com/sindresorhus/get-stream.git","sindresorhus.com","Sindre Sorhus"],["get-value","2.0.6","MIT","https://github.com/jonschlinkert/get-value.git","https://github.com/jonschlinkert/get-value","Jon Schlinkert"],["getos","3.2.1","MIT","https://github.com/retrohacker/getos.git","https://github.com/retrohacker/getos","<EMAIL>"],["getpass","0.1.7","MIT","https://github.com/arekinath/node-getpass.git","Unknown","Alex Wilson"],["gettext-parser","4.0.4","MIT","http://github.com/smhg/gettext-parser.git","http://github.com/smhg/gettext-parser","Andris Reinman"],["glob-stream","6.1.0","MIT","https://github.com/gulpjs/glob-stream.git","http://gulpjs.com/","Gulp Team"],["global-dirs","2.0.1","MIT","https://github.com/sindresorhus/global-dirs.git","sindresorhus.com","Sindre Sorhus"],["global-dirs","0.1.1","MIT","https://github.com/sindresorhus/global-dirs.git","sindresorhus.com","Sindre Sorhus"],["globals","11.12.0","MIT","https://github.com/sindresorhus/globals.git","sindresorhus.com","Sindre Sorhus"],["globals","9.18.0","MIT","https://github.com/sindresorhus/globals.git","http://sindresorhus.com","Sindre Sorhus"],["globby","6.1.0","MIT","https://github.com/sindresorhus/globby.git","sindresorhus.com","Sindre Sorhus"],["globby","11.0.1","MIT","https://github.com/sindresorhus/globby.git","sindresorhus.com","Sindre Sorhus"],["globby","7.1.1","MIT","https://github.com/sindresorhus/globby.git","sindresorhus.com","Sindre Sorhus"],["globule","1.3.2","MIT","git://github.com/cowboy/node-globule.git","https://github.com/cowboy/node-globule","\"Cowboy\" Ben Alman"],["good-listener","1.2.2","MIT","https://github.com/zenorocha/good-listener.git","Unknown","Unknown"],["got","6.7.1","MIT","https://github.com/sindresorhus/got.git","Unknown","Unknown"],["growl","1.10.5","MIT","git://github.com/tj/node-growl.git","Unknown","TJ Holowaychuk"],["gzip-size","5.1.1","MIT","https://github.com/sindresorhus/gzip-size.git","sindresorhus.com","Sindre Sorhus"],["hammerjs","2.0.8","MIT","git://github.com/hammerjs/hammer.js.git","http://hammerjs.github.io/","Jorik Tangelder"],["handle-thing","2.0.1","MIT","git+ssh://**************/indutny/handle-thing.git","https://github.com/spdy-http2/handle-thing#readme","Fedor Indutny"],["handlebars","4.7.6","MIT","https://github.com/wycats/handlebars.js.git","http://www.handlebarsjs.com/","Yehuda Katz"],["har-validator","5.1.5","MIT","https://github.com/ahmadnassri/node-har-validator.git","https://github.com/ahmadnassri/node-har-validator","Ahmad Nassri"],["has","1.0.3","MIT","git://github.com/tarruda/has.git","https://github.com/tarruda/has","Thiago de Arruda"],["has-ansi","2.0.0","MIT","https://github.com/sindresorhus/has-ansi.git","sindresorhus.com","Sindre Sorhus"],["has-binary2","1.0.3","MIT","Unknown","Unknown","Kevin Roark"],["has-cors","1.1.0","MIT","git://github.com/component/has-cors.git","http://n8.io/","Nathan Rajlich"],["has-flag","3.0.0","MIT","https://github.com/sindresorhus/has-flag.git","sindresorhus.com","Sindre Sorhus"],["has-flag","4.0.0","MIT","https://github.com/sindresorhus/has-flag.git","sindresorhus.com","Sindre Sorhus"],["has-flag","2.0.0","MIT","https://github.com/sindresorhus/has-flag.git","sindresorhus.com","Sindre Sorhus"],["has-symbols","1.0.1","MIT","git://github.com/ljharb/has-symbols.git","http://ljharb.codes","Jordan Harband"],["has-value","1.0.0","MIT","https://github.com/jonschlinkert/has-value.git","https://github.com/jonschlinkert/has-value","Jon Schlinkert"],["has-value","0.3.1","MIT","https://github.com/jonschlinkert/has-value.git","https://github.com/jonschlinkert/has-value","Jon Schlinkert"],["has-values","1.0.0","MIT","https://github.com/jonschlinkert/has-values.git","https://github.com/jonschlinkert/has-values","Jon Schlinkert"],["has-values","0.1.4","MIT","https://github.com/jonschlinkert/has-values.git","https://github.com/jonschlinkert/has-values","Jon Schlinkert"],["hash-base","3.1.0","MIT","https://github.com/crypto-browserify/hash-base.git","https://github.com/crypto-browserify/hash-base","Kirill Fomichev"],["hash.js","1.1.7","MIT","**************:indutny/hash.js","https://github.com/indutny/hash.js","Fedor Indutny"],["hasha","5.2.0","MIT","https://github.com/sindresorhus/hasha.git","sindresorhus.com","Sindre Sorhus"],["he","1.2.0","MIT","https://github.com/mathiasbynens/he.git","https://mths.be/he","Mathias Bynens"],["help-me","1.1.0","MIT","https://github.com/mcollina/help-me.git","https://github.com/mcollina/help-me","Matteo Collina"],["hmac-drbg","1.0.1","MIT","git+ssh://**************/indutny/hmac-drbg.git","https://github.com/indutny/hmac-drbg#readme","Fedor Indutny"],["hoopy","0.1.4","MIT","git+https://gitlab.com/philbooth/hoopy.git","https://gitlab.com/philbooth/hoopy#readme","Phil Booth"],["hpack.js","2.1.6","MIT","git+ssh://**************/indutny/hpack.js.git","https://github.com/indutny/hpack.js#readme","Fedor Indutny"],["html-entities","1.3.1","MIT","https://github.com/mdevils/node-html-entities.git","Unknown","Marat Dulin"],["html-escaper","2.0.2","MIT","https://github.com/WebReflection/html-escaper.git","https://github.com/WebReflection/html-escaper","Andrea Giammarchi"],["http-deceiver","1.2.7","MIT","git+ssh://**************/indutny/http-deceiver.git","https://github.com/indutny/http-deceiver#readme","Fedor Indutny"],["http-errors","1.7.2","MIT","https://github.com/jshttp/http-errors.git","http://jongleberry.com","Jonathan Ong"],["http-errors","1.8.0","MIT","https://github.com/jshttp/http-errors.git","http://jongleberry.com","Jonathan Ong"],["http-errors","1.7.3","MIT","https://github.com/jshttp/http-errors.git","http://jongleberry.com","Jonathan Ong"],["http-errors","1.6.3","MIT","https://github.com/jshttp/http-errors.git","http://jongleberry.com","Jonathan Ong"],["http-parser-js","0.5.2","MIT","git://github.com/creationix/http-parser-js.git","https://github.com/creationix","Tim Caswell"],["http-proxy","1.18.1","MIT","https://github.com/http-party/node-http-proxy.git","Unknown","Charlie Robbins"],["http-proxy-agent","2.1.0","MIT","git://github.com/TooTallNate/node-http-proxy-agent.git","http://n8.io/","Nathan Rajlich"],["http-proxy-middleware","0.19.2","MIT","https://github.com/chimurai/http-proxy-middleware.git","https://github.com/chimurai/http-proxy-middleware","Steven Chim"],["http-signature","1.2.0","MIT","git://github.com/joyent/node-http-signature.git","https://github.com/joyent/node-http-signature/","Joyent, Inc"],["http-status-codes","1.4.0","MIT","https://github.com/prettymuchbryce/node-http-status.git","Unknown","Bryce Neal"],["https-browserify","1.0.0","MIT","git://github.com/substack/https-browserify.git","https://github.com/substack/https-browserify","James Halliday"],["https-proxy-agent","2.2.4","MIT","git://github.com/TooTallNate/node-https-proxy-agent.git","http://n8.io/","Nathan Rajlich"],["https-proxy-agent","4.0.0","MIT","git://github.com/TooTallNate/node-https-proxy-agent.git","http://n8.io/","Nathan Rajlich"],["https-proxy-agent","3.0.1","MIT","git://github.com/TooTallNate/node-https-proxy-agent.git","http://n8.io/","Nathan Rajlich"],["humanize-ms","1.2.1","MIT","https://github.com/node-modules/humanize-ms","http://deadhorse.me","dead-horse"],["iconv-lite","0.4.24","MIT","git://github.com/ashtuchkin/iconv-lite.git","https://github.com/ashtuchkin/iconv-lite","Alexander Shtuchkin"],["iconv-lite","0.6.2","MIT","git://github.com/ashtuchkin/iconv-lite.git","https://github.com/ashtuchkin/iconv-lite","Alexander Shtuchkin"],["iferr","0.1.5","MIT","https://github.com/shesek/iferr","https://github.com/shesek/iferr","Nadav Ivgi"],["ignore","5.1.8","MIT","**************:kaelzhang/node-ignore.git","Unknown","kael"],["ignore","3.3.10","MIT","**************:kaelzhang/node-ignore.git","Unknown","kael"],["image-size","0.5.5","MIT","https://github.com/image-size/image-size.git","http://netroy.in/","netroy"],["immer","5.0.0","MIT","https://github.com/immerjs/immer.git","https://github.com/immerjs/immer#readme","Michel Weststrate"],["import-cwd","2.1.0","MIT","https://github.com/sindresorhus/import-cwd.git","sindresorhus.com","Sindre Sorhus"],["import-fresh","2.0.0","MIT","https://github.com/sindresorhus/import-fresh.git","sindresorhus.com","Sindre Sorhus"],["import-from","2.1.0","MIT","https://github.com/sindresorhus/import-from.git","sindresorhus.com","Sindre Sorhus"],["import-lazy","2.1.0","MIT","https://github.com/sindresorhus/import-lazy.git","sindresorhus.com","Sindre Sorhus"],["import-local","2.0.0","MIT","https://github.com/sindresorhus/import-local.git","sindresorhus.com","Sindre Sorhus"],["imurmurhash","0.1.4","MIT","https://github.com/jensyt/imurmurhash-js","https://github.com/jensyt/imurmurhash-js","Jens Taylor"],["indent-string","4.0.0","MIT","https://github.com/sindresorhus/indent-string.git","sindresorhus.com","Sindre Sorhus"],["indent-string","3.2.0","MIT","https://github.com/sindresorhus/indent-string.git","sindresorhus.com","Sindre Sorhus"],["indent-string","2.1.0","MIT","https://github.com/sindresorhus/indent-string.git","sindresorhus.com","Sindre Sorhus"],["inquirer","0.12.0","MIT","https://github.com/SBoudrias/Inquirer.js.git","Unknown","Simon Boudrias"],["inquirer","6.5.1","MIT","https://github.com/SBoudrias/Inquirer.js.git","Unknown","Simon Boudrias"],["internal-ip","4.3.0","MIT","https://github.com/sindresorhus/internal-ip.git","sindresorhus.com","Sindre Sorhus"],["into-stream","5.1.1","MIT","https://github.com/sindresorhus/into-stream.git","sindresorhus.com","Sindre Sorhus"],["invariant","2.2.4","MIT","https://github.com/zertosh/invariant","Unknown","Andres Suarez"],["invert-kv","1.0.0","MIT","https://github.com/sindresorhus/invert-kv.git","http://sindresorhus.com","Sindre Sorhus"],["invert-kv","2.0.0","MIT","https://github.com/sindresorhus/invert-kv.git","sindresorhus.com","Sindre Sorhus"],["ip","1.1.5","MIT","http://github.com/indutny/node-ip.git","https://github.com/indutny/node-ip","Fedor Indutny"],["ip-regex","2.1.0","MIT","https://github.com/sindresorhus/ip-regex.git","sindresorhus.com","Sindre Sorhus"],["ipaddr.js","1.9.1","MIT","git://github.com/whitequark/ipaddr.js","Unknown","whitequark"],["is-absolute","1.0.0","MIT","https://github.com/jonschlinkert/is-absolute.git","https://github.com/jonschlinkert/is-absolute","Jon Schlinkert"],["is-absolute-url","3.0.3","MIT","https://github.com/sindresorhus/is-absolute-url.git","sindresorhus.com","Sindre Sorhus"],["is-accessor-descriptor","1.0.0","MIT","https://github.com/jonschlinkert/is-accessor-descriptor.git","https://github.com/jonschlinkert/is-accessor-descriptor","Jon Schlinkert"],["is-accessor-descriptor","0.1.6","MIT","https://github.com/jonschlinkert/is-accessor-descriptor.git","https://github.com/jonschlinkert/is-accessor-descriptor","Jon Schlinkert"],["is-arguments","1.0.4","MIT","git://github.com/ljharb/is-arguments.git","https://github.com/ljharb/is-arguments","Jordan Harband"],["is-arrayish","0.2.1","MIT","https://github.com/qix-/node-is-arrayish.git","http://github.com/qix-","Qix"],["is-arrayish","0.3.2","MIT","https://github.com/qix-/node-is-arrayish.git","http://github.com/qix-","Qix"],["is-binary-path","2.1.0","MIT","https://github.com/sindresorhus/is-binary-path.git","sindresorhus.com","Sindre Sorhus"],["is-binary-path","1.0.1","MIT","https://github.com/sindresorhus/is-binary-path.git","sindresorhus.com","Sindre Sorhus"],["is-buffer","2.0.4","MIT","git://github.com/feross/is-buffer.git","https://feross.org","Feross Aboukhadijeh"],["is-buffer","1.1.6","MIT","git://github.com/feross/is-buffer.git","http://feross.org/","Feross Aboukhadijeh"],["is-callable","1.2.0","MIT","git://github.com/ljharb/is-callable.git","http://ljharb.codes","Jordan Harband"],["is-ci","2.0.0","MIT","https://github.com/watson/is-ci.git","https://github.com/watson/is-ci","Thomas Watson Steen"],["is-ci","1.2.1","MIT","https://github.com/watson/is-ci.git","https://github.com/watson/is-ci","Thomas Watson Steen"],["is-data-descriptor","1.0.0","MIT","https://github.com/jonschlinkert/is-data-descriptor.git","https://github.com/jonschlinkert/is-data-descriptor","Jon Schlinkert"],["is-data-descriptor","0.1.4","MIT","https://github.com/jonschlinkert/is-data-descriptor.git","https://github.com/jonschlinkert/is-data-descriptor","Jon Schlinkert"],["is-date-object","1.0.2","MIT","git://github.com/ljharb/is-date-object.git","Unknown","Jordan Harband"],["is-descriptor","1.0.2","MIT","https://github.com/jonschlinkert/is-descriptor.git","https://github.com/jonschlinkert/is-descriptor","Jon Schlinkert"],["is-descriptor","0.1.6","MIT","https://github.com/jonschlinkert/is-descriptor.git","https://github.com/jonschlinkert/is-descriptor","Jon Schlinkert"],["is-directory","0.3.1","MIT","https://github.com/jonschlinkert/is-directory.git","https://github.com/jonschlinkert/is-directory","Jon Schlinkert"],["is-docker","2.1.1","MIT","https://github.com/sindresorhus/is-docker.git","https://sindresorhus.com","Sindre Sorhus"],["is-extendable","1.0.1","MIT","https://github.com/jonschlinkert/is-extendable.git","https://github.com/jonschlinkert/is-extendable","Jon Schlinkert"],["is-extendable","0.1.1","MIT","https://github.com/jonschlinkert/is-extendable.git","https://github.com/jonschlinkert/is-extendable","Jon Schlinkert"],["is-extglob","2.1.1","MIT","https://github.com/jonschlinkert/is-extglob.git","https://github.com/jonschlinkert/is-extglob","Jon Schlinkert"],["is-finite","1.1.0","MIT","https://github.com/sindresorhus/is-finite.git","sindresorhus.com","Sindre Sorhus"],["is-fullwidth-code-point","1.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point.git","sindresorhus.com","Sindre Sorhus"],["is-fullwidth-code-point","3.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point.git","sindresorhus.com","Sindre Sorhus"],["is-fullwidth-code-point","2.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point.git","sindresorhus.com","Sindre Sorhus"],["is-glob","4.0.1","MIT","https://github.com/micromatch/is-glob.git","https://github.com/micromatch/is-glob","Jon Schlinkert"],["is-glob","3.1.0","MIT","https://github.com/jonschlinkert/is-glob.git","https://github.com/jonschlinkert/is-glob","Jon Schlinkert"],["is-installed-globally","0.3.2","MIT","https://github.com/sindresorhus/is-installed-globally.git","sindresorhus.com","Sindre Sorhus"],["is-installed-globally","0.1.0","MIT","https://github.com/sindresorhus/is-installed-globally.git","sindresorhus.com","Sindre Sorhus"],["is-natural-number","4.0.1","MIT","https://github.com/shinnn/is-natural-number.js.git","https://github.com/shinnn","Shinnosuke Watanabe"],["is-negated-glob","1.0.0","MIT","https://github.com/jonschlinkert/is-negated-glob.git","https://github.com/jonschlinkert/is-negated-glob","Jon Schlinkert"],["is-npm","1.0.0","MIT","https://github.com/sindresorhus/is-npm.git","http://sindresorhus.com","Sindre Sorhus"],["is-number","3.0.0","MIT","https://github.com/jonschlinkert/is-number.git","https://github.com/jonschlinkert/is-number","Jon Schlinkert"],["is-number","7.0.0","MIT","https://github.com/jonschlinkert/is-number.git","https://github.com/jonschlinkert/is-number","Jon Schlinkert"],["is-obj","1.0.1","MIT","https://github.com/sindresorhus/is-obj.git","sindresorhus.com","Sindre Sorhus"],["is-observable","1.1.0","MIT","https://github.com/sindresorhus/is-observable.git","sindresorhus.com","Sindre Sorhus"],["is-path-cwd","1.0.0","MIT","https://github.com/sindresorhus/is-path-cwd.git","http://sindresorhus.com","Sindre Sorhus"],["is-path-cwd","2.2.0","MIT","https://github.com/sindresorhus/is-path-cwd.git","sindresorhus.com","Sindre Sorhus"],["is-path-in-cwd","1.0.1","MIT","https://github.com/sindresorhus/is-path-in-cwd.git","http://sindresorhus.com","Sindre Sorhus"],["is-path-in-cwd","2.1.0","MIT","https://github.com/sindresorhus/is-path-in-cwd.git","sindresorhus.com","Sindre Sorhus"],["is-path-inside","1.0.1","MIT","https://github.com/sindresorhus/is-path-inside.git","sindresorhus.com","Sindre Sorhus"],["is-path-inside","3.0.2","MIT","https://github.com/sindresorhus/is-path-inside.git","sindresorhus.com","Sindre Sorhus"],["is-path-inside","2.1.0","MIT","https://github.com/sindresorhus/is-path-inside.git","sindresorhus.com","Sindre Sorhus"],["is-plain-obj","1.1.0","MIT","https://github.com/sindresorhus/is-plain-obj.git","sindresorhus.com","Sindre Sorhus"],["is-plain-object","2.0.4","MIT","https://github.com/jonschlinkert/is-plain-object.git","https://github.com/jonschlinkert/is-plain-object","Jon Schlinkert"],["is-promise","2.2.2","MIT","https://github.com/then/is-promise.git","Unknown","ForbesLindesay"],["is-redirect","1.0.0","MIT","https://github.com/sindresorhus/is-redirect.git","sindresorhus.com","Sindre Sorhus"],["is-regex","1.1.1","MIT","git://github.com/ljharb/is-regex.git","https://github.com/ljharb/is-regex","Jordan Harband"],["is-relative","1.0.0","MIT","https://github.com/jonschlinkert/is-relative.git","https://github.com/jonschlinkert/is-relative","Jon Schlinkert"],["is-retry-allowed","1.2.0","MIT","https://github.com/floatdrop/is-retry-allowed.git","github.com/floatdrop","Vsevolod Strukchinsky"],["is-stream","1.1.0","MIT","https://github.com/sindresorhus/is-stream.git","sindresorhus.com","Sindre Sorhus"],["is-stream","2.0.0","MIT","https://github.com/sindresorhus/is-stream.git","sindresorhus.com","Sindre Sorhus"],["is-symbol","1.0.3","MIT","git://github.com/inspect-js/is-symbol.git","Unknown","Jordan Harband"],["is-typedarray","1.0.0","MIT","git://github.com/hughsk/is-typedarray.git","https://github.com/hughsk/is-typedarray","Hugh Kennedy"],["is-unc-path","1.0.0","MIT","https://github.com/jonschlinkert/is-unc-path.git","https://github.com/jonschlinkert/is-unc-path","Jon Schlinkert"],["is-utf8","0.2.1","MIT","https://github.com/wayfind/is-utf8.git","Unknown","wayfind"],["is-windows","1.0.2","MIT","https://github.com/jonschlinkert/is-windows.git","https://github.com/jonschlinkert/is-windows","Jon Schlinkert"],["is-wsl","1.1.0","MIT","https://github.com/sindresorhus/is-wsl.git","sindresorhus.com","Sindre Sorhus"],["is-wsl","2.2.0","MIT","https://github.com/sindresorhus/is-wsl.git","sindresorhus.com","Sindre Sorhus"],["isarray","1.0.0","MIT","git://github.com/juliangruber/isarray.git","https://github.com/juliangruber/isarray","Julian Gruber"],["isarray","0.0.1","MIT","git://github.com/juliangruber/isarray.git","https://github.com/juliangruber/isarray","Julian Gruber"],["isarray","2.0.1","MIT","git://github.com/juliangruber/isarray.git","https://github.com/juliangruber/isarray","Julian Gruber"],["isbinaryfile","3.0.3","MIT","https://github.com/gjtorikian/isBinaryFile","Unknown","Unknown"],["isobject","3.0.1","MIT","https://github.com/jonschlinkert/isobject.git","https://github.com/jonschlinkert/isobject","Jon Schlinkert"],["isobject","2.1.0","MIT","https://github.com/jonschlinkert/isobject.git","https://github.com/jonschlinkert/isobject","Jon Schlinkert"],["isstream","0.1.2","MIT","https://github.com/rvagg/isstream.git","https://github.com/rvagg/isstream","Rod Vagg"],["istanbul-instrumenter-loader","3.0.1","MIT","https://github.com/webpack-contrib/istanbul-instrumenter-loader.git","https://github.com/webpack-contrib/istanbul-instrumenter-loader","Kir Belevich"],["jasmine","3.5.0","MIT","https://github.com/jasmine/jasmine-npm","http://jasmine.github.io/","Unknown"],["jasmine-core","3.5.0","MIT","https://github.com/jasmine/jasmine.git","https://jasmine.github.io/","Unknown"],["jasmine-core","3.6.0","MIT","https://github.com/jasmine/jasmine.git","https://jasmine.github.io/","Unknown"],["jasmine-reporters","2.3.2","MIT","git://github.com/larrymyers/jasmine-reporters.git","https://github.com/larrymyers/jasmine-reporters","Larry Myers"],["jquery","3.5.0","MIT","https://github.com/jquery/jquery.git","https://jquery.com/","JS Foundation and other contributors"],["js-levenshtein","1.1.6","MIT","https://github.com/gustf/js-levenshtein.git","Unknown","Gustaf Andersson"],["js-tokens","4.0.0","MIT","https://github.com/lydell/js-tokens.git","Unknown","Simon Lydell"],["js-tokens","3.0.2","MIT","https://github.com/lydell/js-tokens.git","Unknown","Simon Lydell"],["js-yaml","3.13.1","MIT","https://github.com/nodeca/js-yaml.git","https://github.com/nodeca/js-yaml","Vladimir Zapparov"],["js-yaml","3.14.0","MIT","https://github.com/nodeca/js-yaml.git","https://github.com/nodeca/js-yaml","Vladimir Zapparov"],["jsbn","0.1.1","MIT","https://github.com/andyperlitch/jsbn.git","Unknown","Tom Wu"],["jsesc","2.5.2","MIT","https://github.com/mathiasbynens/jsesc.git","https://mths.be/jsesc","Mathias Bynens"],["jsesc","1.3.0","MIT","https://github.com/mathiasbynens/jsesc.git","https://mths.be/jsesc","Mathias Bynens"],["jsesc","0.5.0","MIT","https://github.com/mathiasbynens/jsesc.git","http://mths.be/jsesc","Mathias Bynens"],["json-parse-better-errors","1.0.2","MIT","https://github.com/zkat/json-parse-better-errors","Unknown","Kat Marchán"],["json-parse-even-better-errors","2.3.1","MIT","https://github.com/npm/json-parse-even-better-errors","Unknown","Kat Marchán"],["json-schema-traverse","0.4.1","MIT","git+https://github.com/epoberezkin/json-schema-traverse.git","https://github.com/epoberezkin/json-schema-traverse#readme","Evgeny Poberezkin"],["json-schema-traverse","0.3.1","MIT","git+https://github.com/epoberezkin/json-schema-traverse.git","https://github.com/epoberezkin/json-schema-traverse#readme","Evgeny Poberezkin"],["json-socket","0.3.0","MIT","http://github.com/sebastianseilund/node-json-socket.git","Unknown","Sebastian Seilund"],["json-stable-stringify-without-jsonify","1.0.1","MIT","git://github.com/samn/json-stable-stringify.git","https://github.com/samn/json-stable-stringify","James Halliday"],["json3","3.3.3","MIT","git://github.com/bestiejs/json3.git","https://bestiejs.github.io/json3","Kit Cambridge"],["json5","1.0.1","MIT","git+https://github.com/json5/json5.git","http://json5.org/","Aseem Kishore"],["json5","2.1.3","MIT","git+https://github.com/json5/json5.git","http://json5.org/","Aseem Kishore"],["jsonfile","4.0.0","MIT","**************:jprichardson/node-jsonfile.git","Unknown","JP Richardson"],["jsonparse","1.3.1","MIT","http://github.com/creationix/jsonparse.git","Unknown","Tim Caswell"],["jsonwebtoken","8.5.1","MIT","https://github.com/auth0/node-jsonwebtoken","Unknown","auth0"],["jsprim","1.4.1","MIT","git://github.com/joyent/node-jsprim.git","Unknown","Unknown"],["jwa","1.4.1","MIT","git://github.com/brianloveswords/node-jwa.git","Unknown","Brian J. Brennan"],["jws","3.2.2","MIT","git://github.com/brianloveswords/node-jws.git","Unknown","Brian J Brennan"],["karma","4.3.0","MIT","git://github.com/karma-runner/karma.git","http://karma-runner.github.io/","Vojta Jína"],["karma-chrome-launcher","3.1.0","MIT","git://github.com/karma-runner/karma-chrome-launcher.git","Unknown","Vojta Jina"],["karma-cli","2.0.0","MIT","git://github.com/karma-runner/karma-cli.git","http://karma-runner.github.io/","Vojta Jína"],["karma-coverage-istanbul-reporter","2.1.0","MIT","git+https://github.com/mattlewis92/karma-coverage-istanbul-reporter.git","https://github.com/mattlewis92/karma-coverage-istanbul-reporter#readme","Matt Lewis"],["karma-firefox-launcher","1.2.0","MIT","git://github.com/karma-runner/karma-firefox-launcher.git","Unknown","Vojta Jina"],["karma-jasmine","2.0.1","MIT","git://github.com/karma-runner/karma-jasmine.git","Unknown","Vojta Jina"],["karma-jasmine-html-reporter","1.4.2","MIT","https://github.com/dfederm/karma-jasmine-html-reporter","Unknown","Unknown"],["karma-junit-reporter","1.2.0","MIT","git://github.com/karma-runner/karma-junit-reporter.git","Unknown","Vojta Jina"],["karma-source-map-support","1.4.0","MIT","git://github.com/tschaub/karma-source-map-support.git","https://github.com/tschaub/karma-source-map-support","Tim Schaub"],["kind-of","6.0.3","MIT","https://github.com/jonschlinkert/kind-of.git","https://github.com/jonschlinkert/kind-of","Jon Schlinkert"],["kind-of","3.2.2","MIT","https://github.com/jonschlinkert/kind-of.git","https://github.com/jonschlinkert/kind-of","Jon Schlinkert"],["kind-of","5.1.0","MIT","https://github.com/jonschlinkert/kind-of.git","https://github.com/jonschlinkert/kind-of","Jon Schlinkert"],["kind-of","4.0.0","MIT","https://github.com/jonschlinkert/kind-of.git","https://github.com/jonschlinkert/kind-of","Jon Schlinkert"],["kuler","1.0.1","MIT","https://github.com/3rd-Eden/kuler","https://github.com/3rd-Eden/kuler","Arnout Kazemier"],["latest-version","3.1.0","MIT","https://github.com/sindresorhus/latest-version.git","sindresorhus.com","Sindre Sorhus"],["lazy-ass","1.6.0","MIT","https://github.com/bahmutov/lazy-ass.git","https://github.com/bahmutov/lazy-ass","Gleb Bahmutov"],["lcid","1.0.0","MIT","https://github.com/sindresorhus/lcid.git","sindresorhus.com","Sindre Sorhus"],["lcid","2.0.0","MIT","https://github.com/sindresorhus/lcid.git","sindresorhus.com","Sindre Sorhus"],["less-loader","5.0.0","MIT","https://github.com/webpack-contrib/less-loader.git","https://github.com/webpack-contrib/less-loader","Johannes Ewald @jhnns"],["leven","2.1.0","MIT","https://github.com/sindresorhus/leven.git","sindresorhus.com","Sindre Sorhus"],["leven","3.1.0","MIT","https://github.com/sindresorhus/leven.git","sindresorhus.com","Sindre Sorhus"],["levenary","1.1.1","MIT","https://github.com/tanhauhau/levenary.git","Unknown","Tan Li Hau"],["levn","0.3.0","MIT","git://github.com/gkz/levn.git","https://github.com/gkz/levn","George Zahariev"],["libxmljs","0.19.7","MIT","http://github.com/libxmljs/libxmljs.git","Unknown","Marco Rogers"],["listr","0.14.3","MIT","https://github.com/SamVerschueren/listr.git","github.com/SamVerschueren","Sam Verschueren"],["listr-silent-renderer","1.1.1","MIT","https://github.com/SamVerschueren/listr-silent-renderer.git","github.com/SamVerschueren","Sam Verschueren"],["listr-update-renderer","0.5.0","MIT","https://github.com/SamVerschueren/listr-update-renderer.git","github.com/SamVerschueren","Sam Verschueren"],["listr-verbose-renderer","0.5.0","MIT","https://github.com/SamVerschueren/listr-verbose-renderer.git","github.com/SamVerschueren","Sam Verschueren"],["load-json-file","1.1.0","MIT","https://github.com/sindresorhus/load-json-file.git","sindresorhus.com","Sindre Sorhus"],["loader-runner","2.4.0","MIT","git+https://github.com/webpack/loader-runner.git","https://github.com/webpack/loader-runner#readme","Tobias Koppers @sokra"],["loader-utils","1.4.0","MIT","https://github.com/webpack/loader-utils.git","Unknown","Tobias Koppers @sokra"],["loader-utils","1.2.3","MIT","https://github.com/webpack/loader-utils.git","Unknown","Tobias Koppers @sokra"],["locate-path","3.0.0","MIT","https://github.com/sindresorhus/locate-path.git","sindresorhus.com","Sindre Sorhus"],["locate-path","5.0.0","MIT","https://github.com/sindresorhus/locate-path.git","sindresorhus.com","Sindre Sorhus"],["lodash","4.17.20","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.assign","4.2.0","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.clonedeep","4.5.0","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.flattendeep","4.4.0","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.includes","4.3.0","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isboolean","3.0.3","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isempty","4.4.0","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isfunction","3.0.9","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isinteger","4.0.4","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isnumber","3.0.3","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isobject","3.0.2","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isplainobject","4.0.6","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.isstring","4.0.1","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["lodash.once","4.1.1","MIT","https://github.com/lodash/lodash.git","https://lodash.com/","John-David Dalton"],["log-symbols","3.0.0","MIT","https://github.com/sindresorhus/log-symbols.git","sindresorhus.com","Sindre Sorhus"],["log-symbols","2.2.0","MIT","https://github.com/sindresorhus/log-symbols.git","sindresorhus.com","Sindre Sorhus"],["log-symbols","1.0.2","MIT","https://github.com/sindresorhus/log-symbols.git","http://sindresorhus.com","Sindre Sorhus"],["log-update","2.3.0","MIT","https://github.com/sindresorhus/log-update.git","sindresorhus.com","Sindre Sorhus"],["logform","2.2.0","MIT","git+https://github.com/winstonjs/logform.git","https://github.com/winstonjs/logform#readme","Charlie Robbins"],["loglevel","1.7.0","MIT","git://github.com/pimterry/loglevel.git","https://github.com/pimterry/loglevel","Tim Perry"],["loose-envify","1.4.0","MIT","git://github.com/zertosh/loose-envify.git","https://github.com/zertosh/loose-envify","Andres Suarez"],["loud-rejection","1.6.0","MIT","https://github.com/sindresorhus/loud-rejection.git","sindresorhus.com","Sindre Sorhus"],["lowercase-keys","1.0.1","MIT","https://github.com/sindresorhus/lowercase-keys.git","sindresorhus.com","Sindre Sorhus"],["lru-queue","0.1.0","MIT","git://github.com/medikoo/lru-queue.git","http://www.medikoo.com/","Mariusz Nowak"],["magic-string","0.25.3","MIT","https://github.com/rich-harris/magic-string","Unknown","Rich Harris"],["magic-string","0.25.7","MIT","https://github.com/rich-harris/magic-string","Unknown","Rich Harris"],["make-dir","2.1.0","MIT","https://github.com/sindresorhus/make-dir.git","sindresorhus.com","Sindre Sorhus"],["make-dir","3.1.0","MIT","https://github.com/sindresorhus/make-dir.git","sindresorhus.com","Sindre Sorhus"],["make-dir","1.3.0","MIT","https://github.com/sindresorhus/make-dir.git","sindresorhus.com","Sindre Sorhus"],["mamacro","0.0.3","MIT","Unknown","Unknown","Sven Sauleau"],["map-age-cleaner","0.1.3","MIT","https://github.com/SamVerschueren/map-age-cleaner.git","github.com/SamVerschueren","Sam Verschueren"],["map-cache","0.2.2","MIT","https://github.com/jonschlinkert/map-cache.git","https://github.com/jonschlinkert/map-cache","Jon Schlinkert"],["map-obj","1.0.1","MIT","https://github.com/sindresorhus/map-obj.git","sindresorhus.com","Sindre Sorhus"],["map-visit","1.0.0","MIT","https://github.com/jonschlinkert/map-visit.git","https://github.com/jonschlinkert/map-visit","Jon Schlinkert"],["marked","0.7.0","MIT","git://github.com/markedjs/marked.git","https://marked.js.org/","Christopher Jeffrey"],["md5-file","4.0.0","MIT","https://github.com/roryrjb/md5-file.git","Unknown","Rory Bradford"],["md5.js","1.3.5","MIT","https://github.com/crypto-browserify/md5.js.git","https://github.com/crypto-browserify/md5.js","Kirill Fomichev"],["media-typer","0.3.0","MIT","https://github.com/jshttp/media-typer.git","Unknown","Douglas Christopher Wilson"],["mem","4.3.0","MIT","https://github.com/sindresorhus/mem.git","sindresorhus.com","Sindre Sorhus"],["memory-fs","0.4.1","MIT","https://github.com/webpack/memory-fs.git","https://github.com/webpack/memory-fs","Tobias Koppers @sokra"],["memory-fs","0.5.0","MIT","https://github.com/webpack/memory-fs.git","https://github.com/webpack/memory-fs","Tobias Koppers @sokra"],["memory-pager","1.5.0","MIT","https://github.com/mafintosh/memory-pager.git","https://github.com/mafintosh/memory-pager","Mathias Buus"],["meow","3.7.0","MIT","https://github.com/sindresorhus/meow.git","sindresorhus.com","Sindre Sorhus"],["merge","1.2.1","MIT","https://github.com/yeikos/js.merge.git","https://github.com/yeikos/js.merge","yeikos"],["merge-descriptors","1.0.1","MIT","https://github.com/component/merge-descriptors.git","http://jongleberry.com","Jonathan Ong"],["merge-stream","2.0.0","MIT","https://github.com/grncdr/merge-stream.git","Unknown","Stephen Sugden"],["merge2","1.4.1","MIT","**************:teambition/merge2.git","https://github.com/teambition/merge2","Unknown"],["messageformat","2.3.0","MIT","https://github.com/messageformat/messageformat.git","https://messageformat.github.io/messageformat/","Alex Sexton"],["messageformat-formatters","2.0.1","MIT","https://github.com/messageformat/messageformat.git","https://messageformat.github.io/","Eemeli Aro"],["messageformat-parser","4.1.3","MIT","https://github.com/messageformat/messageformat.git","https://messageformat.github.io/","Unknown"],["methods","1.1.2","MIT","https://github.com/jshttp/methods.git","Unknown","Unknown"],["micromatch","4.0.2","MIT","https://github.com/micromatch/micromatch.git","https://github.com/micromatch/micromatch","Jon Schlinkert"],["micromatch","3.1.10","MIT","https://github.com/micromatch/micromatch.git","https://github.com/micromatch/micromatch","Jon Schlinkert"],["migrate-mongoose","4.0.0","MIT","https://github.com/balmasi/migrate-mongoose.git","Unknown","Borna Almasi"],["miller-rabin","4.0.1","MIT","**************:indutny/miller-rabin","https://github.com/indutny/miller-rabin","Fedor Indutny"],["mime","2.4.6","MIT","https://github.com/broofa/mime","http://github.com/broofa","Robert Kieffer"],["mime","1.6.0","MIT","https://github.com/broofa/node-mime","http://github.com/broofa","Robert Kieffer"],["mime-db","1.44.0","MIT","https://github.com/jshttp/mime-db.git","Unknown","Unknown"],["mime-types","2.1.27","MIT","https://github.com/jshttp/mime-types.git","Unknown","Unknown"],["mimic-fn","2.1.0","MIT","https://github.com/sindresorhus/mimic-fn.git","sindresorhus.com","Sindre Sorhus"],["mimic-fn","1.2.0","MIT","https://github.com/sindresorhus/mimic-fn.git","sindresorhus.com","Sindre Sorhus"],["mini-css-extract-plugin","0.8.0","MIT","https://github.com/webpack-contrib/mini-css-extract-plugin.git","https://github.com/webpack-contrib/mini-css-extract-plugin","Tobias Koppers @sokra"],["minimalistic-crypto-utils","1.0.1","MIT","git+ssh://**************/indutny/minimalistic-crypto-utils.git","https://github.com/indutny/minimalistic-crypto-utils#readme","Fedor Indutny"],["minimist","1.2.5","MIT","git://github.com/substack/minimist.git","https://github.com/substack/minimist","James Halliday"],["minimist","0.0.10","MIT","git://github.com/substack/minimist.git","https://github.com/substack/minimist","James Halliday"],["minimist","0.0.8","MIT","git://github.com/substack/minimist.git","https://github.com/substack/minimist","James Halliday"],["minizlib","1.3.3","MIT","git+https://github.com/isaacs/minizlib.git","http://blog.izs.me/","Isaac Z. Schlueter"],["mixin-deep","1.3.2","MIT","https://github.com/jonschlinkert/mixin-deep.git","https://github.com/jonschlinkert/mixin-deep","Jon Schlinkert"],["mkdirp","0.5.5","MIT","https://github.com/substack/node-mkdirp.git","http://substack.net","James Halliday"],["mkdirp","0.5.1","MIT","https://github.com/substack/node-mkdirp.git","http://substack.net","James Halliday"],["mocha","6.2.2","MIT","https://github.com/mochajs/mocha.git","https://mochajs.org/","TJ Holowaychuk"],["mocha-junit-reporter","1.23.1","MIT","https://github.com/michaelleeallen/mocha-junit-reporter","Unknown","Michael Allen"],["mocha-multi-reporters","1.1.7","MIT","https://github.com/stanleyhlng/mocha-multi-reporters","Unknown","Stanley Ng"],["mochawesome","4.1.0","MIT","https://github.com/adamgruber/mochawesome","Unknown","Adam Gruber"],["mochawesome-merge","2.0.1","MIT","**************:Antontelesh/mochawesome-merge.git","Unknown","Anton Telesh"],["mochawesome-report-generator","4.0.1","MIT","https://github.com/adamgruber/mochawesome-report-generator","Unknown","Adam Gruber"],["mochawesome-report-generator","4.1.0","MIT","https://github.com/adamgruber/mochawesome-report-generator","Unknown","Adam Gruber"],["mock-http-server","1.4.2","MIT","https://github.com/spreaker/node-mock-http-server.git","https://github.com/spreaker/node-mock-http-server","Marco Pracucci"],["moment","2.24.0","MIT","https://github.com/moment/moment.git","http://momentjs.com/","Iskren Ivov Chernev"],["moment","2.26.0","MIT","https://github.com/moment/moment.git","https://momentjs.com/","Iskren Ivov Chernev"],["moment","2.27.0","MIT","https://github.com/moment/moment.git","https://momentjs.com/","Iskren Ivov Chernev"],["mongodb-memory-server","6.3.2","MIT","https://github.com/nodkz/mongodb-memory-server.git","https://github.com/nodkz/mongodb-memory-server","Unknown"],["mongodb-memory-server","6.0.1","MIT","https://github.com/nodkz/mongodb-memory-server.git","https://github.com/nodkz/mongodb-memory-server","Unknown"],["mongodb-memory-server-core","6.3.2","MIT","https://github.com/nodkz/mongodb-memory-server.git","https://github.com/nodkz/mongodb-memory-server","Unknown"],["mongodb-memory-server-core","6.0.1","MIT","https://github.com/nodkz/mongodb-memory-server.git","https://github.com/nodkz/mongodb-memory-server","Unknown"],["mongodb-memory-server-global","6.3.2","MIT","https://github.com/nodkz/mongodb-memory-server.git","https://github.com/nodkz/mongodb-memory-server","Unknown"],["mongodb-memory-server-global","6.0.1","MIT","https://github.com/nodkz/mongodb-memory-server.git","https://github.com/nodkz/mongodb-memory-server","Unknown"],["mongoose","5.9.2","MIT","git://github.com/Automattic/mongoose.git","https://mongoosejs.com/","Guillermo Rauch"],["mongoose","5.7.5","MIT","git://github.com/Automattic/mongoose.git","https://mongoosejs.com/","Guillermo Rauch"],["mongoose","5.10.3","MIT","git://github.com/Automattic/mongoose.git","https://mongoosejs.com/","Guillermo Rauch"],["mpath","0.6.0","MIT","git://github.com/aheckmann/mpath.git","Unknown","Aaron Heckmann"],["mpath","0.7.0","MIT","git://github.com/aheckmann/mpath.git","Unknown","Aaron Heckmann"],["mqtt","3.0.0","MIT","git://github.com/mqttjs/MQTT.js.git","Unknown","Unknown"],["mqtt-packet","6.6.0","MIT","https://github.com/mqttjs/mqtt-packet.git","https://github.com/mqttjs/mqtt-packet","Unknown"],["mquery","3.2.2","MIT","git://github.com/aheckmann/mquery.git","https://github.com/aheckmann/mquery/","Aaron Heckmann"],["ms","2.1.2","MIT","https://github.com/zeit/ms.git","Unknown","Unknown"],["ms","2.1.1","MIT","https://github.com/zeit/ms.git","Unknown","Unknown"],["ms","2.0.0","MIT","https://github.com/zeit/ms.git","Unknown","Unknown"],["multer","1.4.2","MIT","https://github.com/expressjs/multer.git","Unknown","Unknown"],["multicast-dns","6.2.3","MIT","https://github.com/mafintosh/multicast-dns.git","https://github.com/mafintosh/multicast-dns","Mathias Buus"],["multicast-dns-service-types","1.1.0","MIT","https://github.com/mafintosh/multicast-dns-service-types.git","https://github.com/mafintosh/multicast-dns-service-types","Mathias Buus"],["multiparty","4.2.2","MIT","https://github.com/pillarjs/multiparty.git","Unknown","Andrew Kelley"],["multistream","2.1.1","MIT","git://github.com/feross/multistream.git","https://github.com/feross/multistream","Feross Aboukhadijeh"],["nan","2.14.1","MIT","git://github.com/nodejs/nan.git","Unknown","Unknown"],["nanomatch","1.2.13","MIT","https://github.com/micromatch/nanomatch.git","https://github.com/micromatch/nanomatch","Jon Schlinkert"],["needle","2.5.2","MIT","https://github.com/tomas/needle.git","Unknown","Tomás Pollak"],["negotiator","0.6.2","MIT","https://github.com/jshttp/negotiator.git","Unknown","Unknown"],["neo-async","2.6.2","MIT","**************:suguru03/neo-async.git","https://github.com/suguru03/neo-async","Unknown"],["next-tick","1.0.0","MIT","git://github.com/medikoo/next-tick.git","http://www.medikoo.com/","Mariusz Nowak"],["ng-drag-drop","5.0.0","MIT","git+https://github.com/ObaidUrRehman/ng-drag-drop.git","https://github.com/ObaidUrRehman/ng-drag-drop#readme","Obaid ur Rehman"],["ng-mocks","8.1.0","MIT","git+https://github.com/ike18t/ng-mocks.git","https://github.com/ike18t/ng-mocks#readme","Isaac Datlof"],["ngrx-store-localstorage","8.0.0","MIT","git+https://github.com/btroncone/ngrx-store-localstorage.git","https://github.com/btroncone/ngrx-store-localstorage#readme","Brian Troncone"],["ngx-clipboard","12.3.0","MIT","https://github.com/maxisam/ngx-clipboard","https://github.com/maxisam/ngx-clipboard","Sam Lin"],["ngx-color-picker","8.2.0","MIT","https://github.com/zefoy/ngx-color-picker.git","Unknown","Unknown"],["ngx-cookie","4.1.2","MIT","https://github.com/salemdar/ngx-cookie.git","https://github.com/salemdar/ngx-cookie#readme","Samet Alemdar"],["ngx-markdown","8.1.0","MIT","https://github.com/jfcere/ngx-markdown","https://github.com/jfcere/ngx-markdown","Jean-Francois Cere"],["ngx-quill","5.2.0","MIT","https://github.com/KillerCodeMonkey/ngx-quill","https://github.com/KillerCodeMonkey/ngx-quill","Bengt Weiße"],["ngx-toastr","11.0.0","MIT","https://github.com/scttcper/ngx-toastr.git","https://github.com/scttcper/ngx-toastr","Unknown"],["ngx-translate-messageformat-compiler","4.5.0","MIT","git+https://github.com/lephyrus/ngx-translate-messageformat-compiler.git","https://github.com/lephyrus/ngx-translate-messageformat-compiler#readme","Lukas Rieder"],["ngx-window-token","2.0.1","MIT","https://github.com/maxisam/ngx-window-token","https://github.com/maxisam/ngx-window-token","Sam Lin"],["nice-try","1.0.5","MIT","https://github.com/electerious/nice-try.git","https://github.com/electerious/nice-try","Unknown"],["nock","10.0.4","MIT","https://github.com/nock/nock.git","Unknown","Pedro Teixeira"],["node-fetch","2.6.1","MIT","https://github.com/bitinn/node-fetch.git","https://github.com/bitinn/node-fetch","David Frank"],["node-fetch-npm","2.0.4","MIT","https://github.com/npm/node-fetch-npm.git","https://github.com/npm/node-fetch-npm","David Frank"],["node-gyp","3.8.0","MIT","git://github.com/nodejs/node-gyp.git","http://tootallnate.net","Nathan Rajlich"],["node-libs-browser","2.2.1","MIT","git+https://github.com/webpack/node-libs-browser.git","http://github.com/webpack/node-libs-browser","Tobias Koppers @sokra"],["node-preload","0.2.1","MIT","git+https://github.com/cfware/node-preload.git","https://github.com/cfware/node-preload#readme","Corey Farrell"],["node-releases","1.1.61","MIT","git+https://github.com/chicoxyzzy/node-releases.git","Unknown","Sergey Rubanov"],["node-sass","4.12.0","MIT","https://github.com/sass/node-sass","https://github.com/sass/node-sass","Andrew Nesbitt"],["nodemailer","4.7.0","MIT","https://github.com/nodemailer/nodemailer.git","https://nodemailer.com/","Andris Reinman"],["nodemailer-express-handlebars","3.1.0","MIT","https://github.com/yads/nodemailer-express-handlebars","https://github.com/yads/nodemailer-express-handlebars","Vadim Kazakov"],["nodemon","2.0.2","MIT","https://github.com/remy/nodemon.git","http://nodemon.io/","Remy Sharp"],["nodemon","1.19.3","MIT","https://github.com/remy/nodemon.git","http://nodemon.io/","Remy Sharp"],["nopt","1.0.10","MIT","http://github.com/isaacs/nopt","http://blog.izs.me/","Isaac Z. Schlueter"],["normalize-path","3.0.0","MIT","https://github.com/jonschlinkert/normalize-path.git","https://github.com/jonschlinkert/normalize-path","Jon Schlinkert"],["normalize-path","2.1.1","MIT","https://github.com/jonschlinkert/normalize-path.git","https://github.com/jonschlinkert/normalize-path","Jon Schlinkert"],["normalize-range","0.1.2","MIT","https://github.com/jamestalmage/normalize-range.git","github.com/jamestalmage","James Talmage"],["normalize-url","1.9.1","MIT","https://github.com/sindresorhus/normalize-url.git","sindresorhus.com","Sindre Sorhus"],["npm-run-path","2.0.2","MIT","https://github.com/sindresorhus/npm-run-path.git","sindresorhus.com","Sindre Sorhus"],["npm-run-path","4.0.1","MIT","https://github.com/sindresorhus/npm-run-path.git","sindresorhus.com","Sindre Sorhus"],["num2fraction","1.2.2","MIT","**************:yisibl/num2fraction.git","http://iyunlu.com/view","yisi"],["number-is-nan","1.0.1","MIT","https://github.com/sindresorhus/number-is-nan.git","sindresorhus.com","Sindre Sorhus"],["object-assign","4.1.1","MIT","https://github.com/sindresorhus/object-assign.git","sindresorhus.com","Sindre Sorhus"],["object-copy","0.1.0","MIT","https://github.com/jonschlinkert/object-copy.git","https://github.com/jonschlinkert/object-copy","Jon Schlinkert"],["object-hash","2.0.0","MIT","https://github.com/puleos/object-hash","https://github.com/puleos/object-hash","Scott Puleo"],["object-inspect","1.8.0","MIT","git://github.com/inspect-js/object-inspect.git","https://github.com/inspect-js/object-inspect","James Halliday"],["object-is","1.1.2","MIT","git://github.com/es-shims/object-is.git","https://github.com/es-shims/object-is","Jordan Harband"],["object-keys","1.1.1","MIT","git://github.com/ljharb/object-keys.git","http://ljharb.codes","Jordan Harband"],["object-visit","1.0.1","MIT","https://github.com/jonschlinkert/object-visit.git","https://github.com/jonschlinkert/object-visit","Jon Schlinkert"],["object.assign","4.1.0","MIT","git://github.com/ljharb/object.assign.git","Unknown","Jordan Harband"],["object.getownpropertydescriptors","2.1.0","MIT","git://github.com/es-shims/object.getownpropertydescriptors.git","Unknown","Jordan Harband"],["object.pick","1.3.0","MIT","https://github.com/jonschlinkert/object.pick.git","https://github.com/jonschlinkert/object.pick","Jon Schlinkert"],["obuf","1.1.2","MIT","**************:indutny/offset-buffer","https://github.com/indutny/offset-buffer","Fedor Indutny"],["on-finished","2.3.0","MIT","https://github.com/jshttp/on-finished.git","Unknown","Unknown"],["on-headers","1.0.2","MIT","https://github.com/jshttp/on-headers.git","Unknown","Douglas Christopher Wilson"],["one-time","0.0.4","MIT","https://github.com/unshiftio/one-time","Unknown","Arnout Kazemier"],["onetime","5.1.2","MIT","https://github.com/sindresorhus/onetime.git","https://sindresorhus.com","Sindre Sorhus"],["onetime","1.1.0","MIT","https://github.com/sindresorhus/onetime.git","sindresorhus.com","Sindre Sorhus"],["onetime","2.0.1","MIT","https://github.com/sindresorhus/onetime.git","sindresorhus.com","Sindre Sorhus"],["open","6.4.0","MIT","https://github.com/sindresorhus/open.git","sindresorhus.com","Sindre Sorhus"],["opn","5.5.0","MIT","https://github.com/sindresorhus/opn.git","sindresorhus.com","Sindre Sorhus"],["optimist","0.6.1","MIT","http://github.com/substack/node-optimist.git","http://substack.net","James Halliday"],["optional","0.1.4","MIT","**************:tony-o/node-optional.git","http://segomos.com/","Unknown"],["optionator","0.8.3","MIT","git://github.com/gkz/optionator.git","https://github.com/gkz/optionator","George Zahariev"],["ordered-read-streams","1.0.1","MIT","https://github.com/armed/ordered-read-streams.git","Unknown","Artem Medeusheyev"],["original","1.0.2","MIT","https://github.com/unshiftio/original","Unknown","Arnout Kazemier"],["os","0.1.1","MIT","git+https://github.com/DiegoRBaquero/node-os.git","https://github.com/DiegoRBaquero/node-os#readme","Diego Rodríguez Baquero"],["os-browserify","0.3.0","MIT","http://github.com/CoderPuppy/os-browserify.git","Unknown","CoderPuppy"],["os-homedir","1.0.2","MIT","https://github.com/sindresorhus/os-homedir.git","sindresorhus.com","Sindre Sorhus"],["os-locale","1.4.0","MIT","https://github.com/sindresorhus/os-locale.git","sindresorhus.com","Sindre Sorhus"],["os-locale","3.1.0","MIT","https://github.com/sindresorhus/os-locale.git","sindresorhus.com","Sindre Sorhus"],["os-tmpdir","1.0.2","MIT","https://github.com/sindresorhus/os-tmpdir.git","sindresorhus.com","Sindre Sorhus"],["ospath","1.2.2","MIT","git+https://github.com/jprichardson/ospath.git","https://github.com/jprichardson/ospath#readme","JP Richardson"],["p-defer","1.0.0","MIT","https://github.com/sindresorhus/p-defer.git","sindresorhus.com","Sindre Sorhus"],["p-finally","1.0.0","MIT","https://github.com/sindresorhus/p-finally.git","sindresorhus.com","Sindre Sorhus"],["p-finally","2.0.1","MIT","https://github.com/sindresorhus/p-finally.git","sindresorhus.com","Sindre Sorhus"],["p-is-promise","3.0.0","MIT","https://github.com/sindresorhus/p-is-promise.git","sindresorhus.com","Sindre Sorhus"],["p-is-promise","2.1.0","MIT","https://github.com/sindresorhus/p-is-promise.git","sindresorhus.com","Sindre Sorhus"],["p-limit","2.3.0","MIT","https://github.com/sindresorhus/p-limit.git","sindresorhus.com","Sindre Sorhus"],["p-locate","3.0.0","MIT","https://github.com/sindresorhus/p-locate.git","sindresorhus.com","Sindre Sorhus"],["p-locate","4.1.0","MIT","https://github.com/sindresorhus/p-locate.git","sindresorhus.com","Sindre Sorhus"],["p-map","1.2.0","MIT","https://github.com/sindresorhus/p-map.git","sindresorhus.com","Sindre Sorhus"],["p-map","3.0.0","MIT","https://github.com/sindresorhus/p-map.git","sindresorhus.com","Sindre Sorhus"],["p-map","2.1.0","MIT","https://github.com/sindresorhus/p-map.git","sindresorhus.com","Sindre Sorhus"],["p-retry","3.0.1","MIT","https://github.com/sindresorhus/p-retry.git","sindresorhus.com","Sindre Sorhus"],["p-try","2.2.0","MIT","https://github.com/sindresorhus/p-try.git","sindresorhus.com","Sindre Sorhus"],["package-json","4.0.1","MIT","https://github.com/sindresorhus/package-json.git","sindresorhus.com","Sindre Sorhus"],["pacote","9.5.5","MIT","https://github.com/npm/pacote","Unknown","Kat Marchán"],["parallel-transform","1.2.0","MIT","git://github.com/mafintosh/parallel-transform","Unknown","Mathias Buus Madsen"],["parse-json","4.0.0","MIT","https://github.com/sindresorhus/parse-json.git","sindresorhus.com","Sindre Sorhus"],["parse-json","2.2.0","MIT","https://github.com/sindresorhus/parse-json.git","sindresorhus.com","Sindre Sorhus"],["parse5","5.1.1","MIT","git://github.com/inikulin/parse5.git","https://github.com/inikulin/parse5","Ivan Nikulin"],["parse5","4.0.0","MIT","git://github.com/inikulin/parse5.git","https://github.com/inikulin/parse5","Ivan Nikulin"],["parseqs","0.0.5","MIT","https://github.com/get/querystring.git","https://github.com/get/querystring","Gal Koren"],["parseuri","0.0.5","MIT","https://github.com/get/parseuri.git","https://github.com/get/parseuri","Unknown"],["parseurl","1.3.3","MIT","https://github.com/pillarjs/parseurl.git","Unknown","Unknown"],["pascalcase","0.1.1","MIT","https://github.com/jonschlinkert/pascalcase.git","https://github.com/jonschlinkert/pascalcase","Jon Schlinkert"],["passport","0.4.0","MIT","git://github.com/jaredhanson/passport.git","http://passportjs.org/","Jared Hanson"],["passport-http-bearer","1.0.1","MIT","git://github.com/jaredhanson/passport-http-bearer.git","http://www.jaredhanson.net/","Jared Hanson"],["passport-jwt","4.0.0","MIT","https://github.com/themikenicholson/passport-jwt.git","https://github.com/themikenicholson/passport-jwt","Mike Nicholson"],["passport-strategy","1.0.0","MIT","git://github.com/jaredhanson/passport-strategy.git","http://www.jaredhanson.net/","Jared Hanson"],["password-hash","1.2.2","MIT","http://github.com/davidwood/node-password-hash.git","Unknown","David Wood"],["path","0.12.7","MIT","git://github.com/jinder/path.git","http://nodejs.org/docs/latest/api/path.html","Joyent"],["path-browserify","0.0.1","MIT","git://github.com/substack/path-browserify.git","https://github.com/substack/path-browserify","James Halliday"],["path-dirname","1.0.2","MIT","https://github.com/es128/path-dirname.git","Unknown","Elan Shanker"],["path-exists","3.0.0","MIT","https://github.com/sindresorhus/path-exists.git","sindresorhus.com","Sindre Sorhus"],["path-exists","4.0.0","MIT","https://github.com/sindresorhus/path-exists.git","sindresorhus.com","Sindre Sorhus"],["path-exists","2.1.0","MIT","https://github.com/sindresorhus/path-exists.git","sindresorhus.com","Sindre Sorhus"],["path-is-absolute","1.0.1","MIT","https://github.com/sindresorhus/path-is-absolute.git","sindresorhus.com","Sindre Sorhus"],["path-key","3.1.1","MIT","https://github.com/sindresorhus/path-key.git","sindresorhus.com","Sindre Sorhus"],["path-key","2.0.1","MIT","https://github.com/sindresorhus/path-key.git","sindresorhus.com","Sindre Sorhus"],["path-parse","1.0.6","MIT","https://github.com/jbgutierrez/path-parse.git","https://github.com/jbgutierrez/path-parse#readme","Javier Blanco"],["path-to-regexp","3.0.0","MIT","https://github.com/pillarjs/path-to-regexp.git","Unknown","Unknown"],["path-to-regexp","0.1.7","MIT","https://github.com/component/path-to-regexp.git","Unknown","Unknown"],["path-type","4.0.0","MIT","https://github.com/sindresorhus/path-type.git","sindresorhus.com","Sindre Sorhus"],["path-type","1.1.0","MIT","https://github.com/sindresorhus/path-type.git","sindresorhus.com","Sindre Sorhus"],["path-type","3.0.0","MIT","https://github.com/sindresorhus/path-type.git","sindresorhus.com","Sindre Sorhus"],["pathval","1.1.0","MIT","git+ssh://**************/chaijs/pathval.git","https://github.com/chaijs/pathval","Veselin Todorov"],["pause","0.0.1","MIT","Unknown","Unknown","TJ Holowaychuk"],["pbkdf2","3.1.1","MIT","https://github.com/crypto-browserify/pbkdf2.git","https://github.com/crypto-browserify/pbkdf2","Daniel Cousens"],["pend","1.2.0","MIT","git://github.com/andrewrk/node-pend.git","Unknown","Andrew Kelley"],["performance-now","2.1.0","MIT","git://github.com/braveg1rl/performance-now.git","https://github.com/braveg1rl/performance-now","Braveg1rl"],["picomatch","2.2.2","MIT","https://github.com/micromatch/picomatch.git","https://github.com/micromatch/picomatch","Jon Schlinkert"],["pify","4.0.1","MIT","https://github.com/sindresorhus/pify.git","sindresorhus.com","Sindre Sorhus"],["pify","3.0.0","MIT","https://github.com/sindresorhus/pify.git","sindresorhus.com","Sindre Sorhus"],["pify","2.3.0","MIT","https://github.com/sindresorhus/pify.git","sindresorhus.com","Sindre Sorhus"],["pinkie","2.0.4","MIT","https://github.com/floatdrop/pinkie.git","github.com/floatdrop","Vsevolod Strukchinsky"],["pinkie-promise","2.0.1","MIT","https://github.com/floatdrop/pinkie-promise.git","github.com/floatdrop","Vsevolod Strukchinsky"],["pkg","4.4.8","MIT","https://github.com/zeit/pkg.git","Unknown","Unknown"],["pkg-dir","3.0.0","MIT","https://github.com/sindresorhus/pkg-dir.git","sindresorhus.com","Sindre Sorhus"],["pkg-dir","4.2.0","MIT","https://github.com/sindresorhus/pkg-dir.git","sindresorhus.com","Sindre Sorhus"],["pkg-fetch","2.6.9","MIT","https://github.com/zeit/pkg-fetch.git","Unknown","Unknown"],["plotly.js-basic-dist-min","1.52.3","MIT","https://github.com/plotly/plotly.js.git","Unknown","Plotly, Inc."],["popper.js","1.15.0","MIT","git+https://github.com/FezVrasta/popper.js.git","https://popper.js.org/","Federico Zivolo"],["portfinder","1.0.26","MIT","**************:http-party/node-portfinder.git","Unknown","Charlie Robbins"],["portfinder","1.0.24","MIT","**************:indexzero/node-portfinder.git","Unknown","Charlie Robbins"],["portfinder","1.0.28","MIT","**************:http-party/node-portfinder.git","Unknown","Charlie Robbins"],["posix-character-classes","0.1.1","MIT","https://github.com/jonschlinkert/posix-character-classes.git","https://github.com/jonschlinkert/posix-character-classes","Jon Schlinkert"],["postcss","7.0.17","MIT","https://github.com/postcss/postcss.git","https://postcss.org/","Andrey Sitnik"],["postcss","7.0.32","MIT","https://github.com/postcss/postcss.git","https://postcss.org/","Andrey Sitnik"],["postcss-import","12.0.1","MIT","https://github.com/postcss/postcss-import.git","Unknown","Maxime Thirouin"],["postcss-load-config","2.1.0","MIT","https://github.com/michael-ciniawsky/postcss-load-config.git","https://github.com/michael-ciniawsky/postcss-load-config#readme","Michael Ciniawky"],["postcss-loader","3.0.0","MIT","https://github.com/postcss/postcss-loader.git","https://github.com/postcss/postcss-loader#readme","Andrey Sitnik"],["postcss-value-parser","4.1.0","MIT","https://github.com/TrySound/postcss-value-parser.git","https://github.com/TrySound/postcss-value-parser","Bogdan Chadkin"],["postcss-value-parser","3.3.1","MIT","https://github.com/TrySound/postcss-value-parser.git","https://github.com/TrySound/postcss-value-parser","Bogdan Chadkin"],["prelude-ls","1.1.2","MIT","git://github.com/gkz/prelude-ls.git","http://preludels.com/","George Zahariev"],["prepend-http","1.0.4","MIT","https://github.com/sindresorhus/prepend-http.git","sindresorhus.com","Sindre Sorhus"],["prettier","2.1.1","MIT","https://github.com/prettier/prettier.git","https://prettier.io/","James Long"],["pretty-bytes","5.3.0","MIT","https://github.com/sindresorhus/pretty-bytes.git","sindresorhus.com","Sindre Sorhus"],["pretty-format","24.9.0","MIT","https://github.com/facebook/jest.git","Unknown","James Kyle"],["prismjs","1.21.0","MIT","https://github.com/PrismJS/prism.git","Unknown","Lea Verou"],["process","0.11.10","MIT","git://github.com/shtylman/node-process.git","Unknown","Roman Shtylman"],["process-nextick-args","2.0.1","MIT","https://github.com/calvinmetcalf/process-nextick-args.git","https://github.com/calvinmetcalf/process-nextick-args","Unknown"],["process-on-spawn","1.0.0","MIT","git+https://github.com/cfware/process-on-spawn.git","https://github.com/cfware/process-on-spawn#readme","Corey Farrell"],["progress","2.0.3","MIT","git://github.com/visionmedia/node-progress","Unknown","TJ Holowaychuk"],["promise","7.3.1","MIT","https://github.com/then/promise.git","Unknown","ForbesLindesay"],["promise","8.1.0","MIT","https://github.com/then/promise.git","Unknown","ForbesLindesay"],["promise-retry","1.1.1","MIT","git://github.com/IndigoUnited/node-promise-retry.git","http://indigounited.com","IndigoUnited"],["prop-types","15.7.2","MIT","https://github.com/facebook/prop-types.git","https://facebook.github.io/react/","Unknown"],["propagate","1.0.0","MIT","http://github.com/pgte/propagate.git","Unknown","Pedro Teixeira"],["propagating-hammerjs","1.4.7","MIT","https://github.com/josdejong/propagating-hammerjs.git","https://github.com/josdejong/propagating-hammerjs","Jos de Jong"],["protoduck","5.0.1","MIT","git+https://github.com/zkat/protoduck.git","https://github.com/zkat/protoduck#readme","Kat Marchán"],["proxy-addr","2.0.6","MIT","https://github.com/jshttp/proxy-addr.git","Unknown","Douglas Christopher Wilson"],["proxy-from-env","1.1.0","MIT","https://github.com/Rob--W/proxy-from-env.git","https://github.com/Rob--W/proxy-from-env#readme","Rob Wu"],["prr","1.0.1","MIT","https://github.com/rvagg/prr.git","https://github.com/rvagg/prr","Rod Vagg"],["ps-tree","1.2.0","MIT","https://github.com/indexzero/ps-tree.git","http://github.com/indexzero/ps-tree#readme","Charlie Robbins"],["psl","1.8.0","MIT","**************:lupomontero/psl.git","https://lupomontero.com/","Lupo Montero"],["pstree.remy","1.1.8","MIT","https://github.com/remy/pstree.git","Unknown","Remy Sharp"],["public-encrypt","4.0.3","MIT","https://github.com/crypto-browserify/publicEncrypt.git","https://github.com/crypto-browserify/publicEncrypt","Calvin Metcalf"],["pump","3.0.0","MIT","git://github.com/mafintosh/pump.git","Unknown","Mathias Buus Madsen"],["pump","2.0.1","MIT","git://github.com/mafintosh/pump.git","Unknown","Mathias Buus Madsen"],["pumpify","1.5.1","MIT","git://github.com/mafintosh/pumpify","https://github.com/mafintosh/pumpify","Mathias Buus"],["punycode","2.1.1","MIT","https://github.com/bestiejs/punycode.js.git","https://mths.be/punycode","Mathias Bynens"],["punycode","1.4.1","MIT","https://github.com/bestiejs/punycode.js.git","https://mths.be/punycode","Mathias Bynens"],["punycode","1.3.2","MIT","https://github.com/bestiejs/punycode.js.git","https://mths.be/punycode","Mathias Bynens"],["qjobs","1.2.0","MIT","git://github.com/franck34/qjobs.git","Unknown","Franck TABARY"],["query-string","4.3.4","MIT","https://github.com/sindresorhus/query-string.git","sindresorhus.com","Sindre Sorhus"],["querystring","0.2.0","MIT","git://github.com/Gozala/querystring.git","Unknown","Irakli Gozalishvili"],["querystring-es3","0.2.1","MIT","git://github.com/mike-spainhower/querystring.git","Unknown","Irakli Gozalishvili"],["querystringify","2.2.0","MIT","https://github.com/unshiftio/querystringify","https://github.com/unshiftio/querystringify","Arnout Kazemier"],["quill-delta","3.6.3","MIT","https://github.com/quilljs/delta","https://github.com/quilljs/delta","Jason Chen"],["ramda","0.26.1","MIT","git://github.com/ramda/ramda.git","https://ramdajs.com/","Scott Sauyet"],["random-bytes","1.0.0","MIT","https://github.com/crypto-utils/random-bytes.git","Unknown","Unknown"],["randombytes","2.1.0","MIT","**************:crypto-browserify/randombytes.git","https://github.com/crypto-browserify/randombytes","Unknown"],["randomfill","1.0.4","MIT","https://github.com/crypto-browserify/randomfill.git","https://github.com/crypto-browserify/randomfill","Unknown"],["range-parser","1.2.1","MIT","https://github.com/jshttp/range-parser.git","http://tjholowaychuk.com","TJ Holowaychuk"],["raw-body","2.4.0","MIT","https://github.com/stream-utils/raw-body.git","http://jongleberry.com","Jonathan Ong"],["raw-loader","3.1.0","MIT","https://github.com/webpack-contrib/raw-loader.git","https://github.com/webpack-contrib/raw-loader","Tobias Koppers @sokra"],["react","16.13.1","MIT","https://github.com/facebook/react.git","https://reactjs.org/","Unknown"],["react-dom","16.13.1","MIT","https://github.com/facebook/react.git","https://reactjs.org/","Unknown"],["react-is","16.13.1","MIT","https://github.com/facebook/react.git","https://reactjs.org/","Unknown"],["read-cache","1.0.0","MIT","git+https://github.com/TrySound/read-cache.git","https://github.com/TrySound/read-cache#readme","Bogdan Chadkin"],["read-pkg","4.0.1","MIT","https://github.com/sindresorhus/read-pkg.git","sindresorhus.com","Sindre Sorhus"],["read-pkg","1.1.0","MIT","https://github.com/sindresorhus/read-pkg.git","sindresorhus.com","Sindre Sorhus"],["read-pkg-up","1.0.1","MIT","https://github.com/sindresorhus/read-pkg-up.git","sindresorhus.com","Sindre Sorhus"],["readable-stream","2.3.7","MIT","git://github.com/nodejs/readable-stream","Unknown","Unknown"],["readable-stream","3.6.0","MIT","git://github.com/nodejs/readable-stream","Unknown","Unknown"],["readable-stream","1.1.14","MIT","git://github.com/isaacs/readable-stream","http://blog.izs.me/","Isaac Z. Schlueter"],["readdirp","3.4.0","MIT","git://github.com/paulmillr/readdirp.git","https://github.com/paulmillr/readdirp","Thorsten Lorenz"],["readdirp","2.2.1","MIT","git://github.com/paulmillr/readdirp.git","https://github.com/paulmillr/readdirp","Thorsten Lorenz"],["readline2","1.0.1","MIT","https://github.com/SBoudrias/readline2.git","Unknown","Simon Boudrias"],["redent","1.0.0","MIT","https://github.com/sindresorhus/redent.git","sindresorhus.com","Sindre Sorhus"],["regenerate","1.4.1","MIT","https://github.com/mathiasbynens/regenerate.git","https://mths.be/regenerate","Mathias Bynens"],["regenerate-unicode-properties","8.2.0","MIT","https://github.com/mathiasbynens/regenerate-unicode-properties.git","https://github.com/mathiasbynens/regenerate-unicode-properties","Mathias Bynens"],["regenerator-runtime","0.13.5","MIT","https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime","Unknown","Ben Newman"],["regenerator-runtime","0.13.3","MIT","https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime","Unknown","Ben Newman"],["regenerator-runtime","0.13.7","MIT","https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime","Unknown","Ben Newman"],["regenerator-runtime","0.11.1","MIT","https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime","Unknown","Ben Newman"],["regenerator-transform","0.14.5","MIT","https://github.com/facebook/regenerator/tree/master/packages/regenerator-transform","Unknown","Ben Newman"],["regex-not","1.0.2","MIT","https://github.com/jonschlinkert/regex-not.git","https://github.com/jonschlinkert/regex-not","Jon Schlinkert"],["regexp-clone","1.0.0","MIT","git://github.com/aheckmann/regexp-clone.git","Unknown","Aaron Heckmann"],["regexp.prototype.flags","1.3.0","MIT","git://github.com/es-shims/RegExp.prototype.flags.git","Unknown","Jordan Harband"],["regexpu-core","4.7.0","MIT","https://github.com/mathiasbynens/regexpu-core.git","https://mths.be/regexpu","Mathias Bynens"],["registry-auth-token","3.4.0","MIT","git+ssh://**************/rexxars/registry-auth-token.git","https://github.com/rexxars/registry-auth-token#readme","Espen Hovlandsdal"],["registry-url","3.1.0","MIT","https://github.com/sindresorhus/registry-url.git","sindresorhus.com","Sindre Sorhus"],["regjsgen","0.5.2","MIT","https://github.com/bnjmnt4n/regjsgen.git","https://github.com/bnjmnt4n/regjsgen","Benjamin Tan"],["reinterval","1.1.0","MIT","https://github.com/4rzael/reInterval.git","https://github.com/4rzael/reInterval","4rzael"],["repeat-element","1.1.3","MIT","https://github.com/jonschlinkert/repeat-element.git","https://github.com/jonschlinkert/repeat-element","Jon Schlinkert"],["repeat-string","1.6.1","MIT","https://github.com/jonschlinkert/repeat-string.git","https://github.com/jonschlinkert/repeat-string","Jon Schlinkert"],["repeating","2.0.1","MIT","https://github.com/sindresorhus/repeating.git","sindresorhus.com","Sindre Sorhus"],["replace-in-file","4.3.1","MIT","https://github.com/adamreisnz/replace-in-file.git","https://github.com/adamreisnz/replace-in-file#readme","Adam Reis"],["request-progress","3.0.0","MIT","git://github.com/IndigoUnited/node-request-progress","http://indigounited.com","IndigoUnited"],["require-directory","2.1.1","MIT","git://github.com/troygoode/node-require-directory.git","https://github.com/troygoode/node-require-directory/","Troy Goode"],["requires-port","1.0.0","MIT","https://github.com/unshiftio/requires-port","https://github.com/unshiftio/requires-port","Arnout Kazemier"],["resolve","1.17.0","MIT","git://github.com/browserify/resolve.git","http://substack.net","James Halliday"],["resolve-cwd","2.0.0","MIT","https://github.com/sindresorhus/resolve-cwd.git","sindresorhus.com","Sindre Sorhus"],["resolve-from","5.0.0","MIT","https://github.com/sindresorhus/resolve-from.git","sindresorhus.com","Sindre Sorhus"],["resolve-from","2.0.0","MIT","https://github.com/sindresorhus/resolve-from.git","sindresorhus.com","Sindre Sorhus"],["resolve-from","3.0.0","MIT","https://github.com/sindresorhus/resolve-from.git","sindresorhus.com","Sindre Sorhus"],["resolve-url","0.2.1","MIT","https://github.com/lydell/resolve-url.git","Unknown","Simon Lydell"],["restore-cursor","1.0.1","MIT","https://github.com/sindresorhus/restore-cursor.git","http://sindresorhus.com","Sindre Sorhus"],["restore-cursor","3.1.0","MIT","https://github.com/sindresorhus/restore-cursor.git","sindresorhus.com","Sindre Sorhus"],["restore-cursor","2.0.0","MIT","https://github.com/sindresorhus/restore-cursor.git","sindresorhus.com","Sindre Sorhus"],["ret","0.1.15","MIT","git://github.com/fent/ret.js.git","https://github.com/fent","Roly Fentanes"],["rete","1.4.3","MIT","git+https://github.com/retejs/rete.git","https://github.com/retejs/rete#readme","Vitaliy Stoliarov"],["rete-angular-render-plugin","0.2.2","MIT","git+https://github.com/retejs/angular-render-plugin.git","https://github.com/retejs/angular-render-plugin#readme","Vitaliy Stoliarov"],["rete-connection-plugin","0.9.0","MIT","https://github.com/retejs/connection-plugin.git","Unknown","Vitaliy Stoliarov"],["rete-readonly-plugin","0.4.1","MIT","https://github.com/retejs/readonly-plugin.git","Unknown","Vitaliy Stoliarov"],["retry","0.12.0","MIT","git://github.com/tim-kos/node-retry.git","https://github.com/tim-kos/node-retry","Tim Koschützki"],["retry","0.10.1","MIT","git://github.com/tim-kos/node-retry.git","https://github.com/tim-kos/node-retry","Tim Koschützki"],["reusify","1.0.4","MIT","git+https://github.com/mcollina/reusify.git","https://github.com/mcollina/reusify#readme","Matteo Collina"],["rfdc","1.1.4","MIT","git+https://github.com/davidmarkclements/rfdc.git","https://github.com/davidmarkclements/rfdc#readme","David Mark Clements"],["ripemd160","2.0.2","MIT","https://github.com/crypto-browserify/ripemd160","Unknown","Unknown"],["run-async","0.1.0","MIT","git://github.com/SBoudrias/run-async","https://github.com/SBoudrias/run-async","Simon Boudrias"],["run-async","2.4.1","MIT","https://github.com/SBoudrias/run-async.git","Unknown","Simon Boudrias"],["run-parallel","1.1.9","MIT","git://github.com/feross/run-parallel.git","https://github.com/feross/run-parallel","Feross Aboukhadijeh"],["rxjs-tslint","0.1.7","MIT","git+https://github.com/reactivex/rxjs-tslint.git","https://github.com/reactivex/rxjs-tslint#readme","Minko Gechev"],["rxjs-tslint-rules","4.25.0","MIT","https://github.com/cartant/rxjs-tslint-rules.git","https://github.com/cartant/rxjs-tslint-rules","Nicholas Jamieson"],["rxjs-tslint-rules","4.24.3","MIT","https://github.com/cartant/rxjs-tslint-rules.git","https://github.com/cartant/rxjs-tslint-rules","Nicholas Jamieson"],["safe-buffer","5.2.1","MIT","git://github.com/feross/safe-buffer.git","https://github.com/feross/safe-buffer","Feross Aboukhadijeh"],["safe-buffer","5.1.2","MIT","git://github.com/feross/safe-buffer.git","https://github.com/feross/safe-buffer","Feross Aboukhadijeh"],["safe-regex","1.1.0","MIT","git://github.com/substack/safe-regex.git","https://github.com/substack/safe-regex","James Halliday"],["safer-buffer","2.1.2","MIT","git+https://github.com/ChALkeR/safer-buffer.git","https://github.com/ChALkeR","Nikita Skovoroda"],["saslprep","1.0.3","MIT","git+https://github.com/reklatsmasters/saslprep.git","https://github.com/reklatsmasters/saslprep#readme","Dmitry Tsvettsikh"],["sass","1.22.9","MIT","https://github.com/sass/dart-sass","https://github.com/sass/dart-sass","Natalie Weizenbaum"],["sass-graph","2.2.6","MIT","https://github.com/xzyfer/sass-graph.git","Unknown","xzyfer"],["sass-loader","7.2.0","MIT","https://github.com/webpack-contrib/sass-loader.git","https://github.com/webpack-contrib/sass-loader","J. Tangelder"],["scheduler","0.19.1","MIT","https://github.com/facebook/react.git","https://reactjs.org/","Unknown"],["schema-utils","1.0.0","MIT","https://github.com/webpack-contrib/schema-utils","https://github.com/webpack-contrib/schema-utils","webpack Contrib"],["schema-utils","2.7.1","MIT","https://github.com/webpack/schema-utils.git","https://github.com/webpack/schema-utils","webpack Contrib"],["schema-utils","0.3.0","MIT","git+https://github.com/webpack-contrib/schema-utils.git","https://github.com/webpack-contrib/schema-utils#readme","Webpack Contrib"],["screenfull","4.2.1","MIT","https://github.com/sindresorhus/screenfull.js.git","sindresorhus.com","Sindre Sorhus"],["scss-tokenizer","0.2.3","MIT","https://github.com/sasstools/scss-tokenizer.git","https://github.com/sasstools/scss-tokenizer","xzyfer"],["seek-bzip","1.0.6","MIT","https://github.com/cscott/seek-bzip.git","Unknown","Unknown"],["select","1.1.2","MIT","https://github.com/zenorocha/select.git","Unknown","Unknown"],["select-hose","2.0.0","MIT","git+ssh://**************/indutny/select-hose.git","https://github.com/indutny/select-hose#readme","Fedor Indutny"],["selfsigned","1.10.7","MIT","git://github.com/jfromaniello/selfsigned.git","http://joseoncode.com","José F. Romaniello"],["semver-diff","2.1.0","MIT","https://github.com/sindresorhus/semver-diff.git","http://sindresorhus.com","Sindre Sorhus"],["semver-dsl","1.0.1","MIT","git+https://github.com/mgechev/semver-dsl.git","https://github.com/mgechev/semver-dsl#readme","Minko Gechev"],["semver-intersect","1.4.0","MIT","**************:snyamathi/semver-intersect.git","Unknown","Suneil Nyamathi"],["send","0.17.1","MIT","https://github.com/pillarjs/send.git","Unknown","TJ Holowaychuk"],["serve-index","1.9.1","MIT","https://github.com/expressjs/serve-index.git","Unknown","Douglas Christopher Wilson"],["serve-static","1.14.1","MIT","https://github.com/expressjs/serve-static.git","Unknown","Douglas Christopher Wilson"],["set-value","2.0.1","MIT","https://github.com/jonschlinkert/set-value.git","https://github.com/jonschlinkert/set-value","Jon Schlinkert"],["setimmediate","1.0.5","MIT","https://github.com/YuzuJS/setImmediate.git","Unknown","YuzuJS"],["shallow-clone","3.0.1","MIT","https://github.com/jonschlinkert/shallow-clone.git","https://github.com/jonschlinkert/shallow-clone","Jon Schlinkert"],["shebang-command","2.0.0","MIT","https://github.com/kevva/shebang-command.git","github.com/kevva","Kevin Mårtensson"],["shebang-command","1.2.0","MIT","https://github.com/kevva/shebang-command.git","github.com/kevva","Kevin Martensson"],["shebang-regex","3.0.0","MIT","https://github.com/sindresorhus/shebang-regex.git","sindresorhus.com","Sindre Sorhus"],["shebang-regex","1.0.0","MIT","https://github.com/sindresorhus/shebang-regex.git","sindresorhus.com","Sindre Sorhus"],["sift","7.0.1","MIT","https://github.com/crcn/sift.js.git","http://crcn.io","Craig Condon"],["simple-swizzle","0.2.2","MIT","https://github.com/qix-/node-simple-swizzle.git","http://github.com/qix-","Qix"],["slash","3.0.0","MIT","https://github.com/sindresorhus/slash.git","sindresorhus.com","Sindre Sorhus"],["slash","1.0.0","MIT","https://github.com/sindresorhus/slash.git","http://sindresorhus.com","Sindre Sorhus"],["slice-ansi","0.0.4","MIT","https://github.com/chalk/slice-ansi.git","Unknown","David Caccavella"],["sliced","1.0.1","MIT","git://github.com/aheckmann/sliced","Unknown","Aaron Heckmann"],["smart-buffer","4.1.0","MIT","https://github.com/JoshGlazebrook/smart-buffer.git","https://github.com/JoshGlazebrook/smart-buffer/","Josh Glazebrook"],["snapdragon","0.8.2","MIT","https://github.com/jonschlinkert/snapdragon.git","https://github.com/jonschlinkert/snapdragon","Jon Schlinkert"],["snapdragon-node","2.1.1","MIT","https://github.com/jonschlinkert/snapdragon-node.git","https://github.com/jonschlinkert/snapdragon-node","Jon Schlinkert"],["snapdragon-util","3.0.1","MIT","https://github.com/jonschlinkert/snapdragon-util.git","https://github.com/jonschlinkert/snapdragon-util","Jon Schlinkert"],["socket.io","2.1.1","MIT","git://github.com/socketio/socket.io","Unknown","Unknown"],["socket.io-adapter","1.1.2","MIT","git://github.com/socketio/socket.io-adapter.git","Unknown","Unknown"],["socket.io-client","2.1.1","MIT","https://github.com/Automattic/socket.io-client.git","Unknown","Unknown"],["socket.io-parser","3.2.0","MIT","https://github.com/Automattic/socket.io-parser.git","Unknown","Unknown"],["sockjs","0.3.19","MIT","https://github.com/sockjs/sockjs-node.git","https://github.com/sockjs/sockjs-node","Marek Majkowski"],["sockjs-client","1.3.0","MIT","https://github.com/sockjs/sockjs-client.git","http://sockjs.org/","Bryce Kahle"],["socks","2.3.3","MIT","https://github.com/JoshGlazebrook/socks.git","https://github.com/JoshGlazebrook/socks/","Josh Glazebrook"],["socks-proxy-agent","4.0.2","MIT","git://github.com/TooTallNate/node-socks-proxy-agent.git","http://n8.io/","Nathan Rajlich"],["sort-keys","1.1.2","MIT","https://github.com/sindresorhus/sort-keys.git","sindresorhus.com","Sindre Sorhus"],["source-list-map","2.0.1","MIT","https://github.com/webpack/source-list-map.git","https://github.com/webpack/source-list-map","Tobias Koppers @sokra"],["source-list-map","0.1.8","MIT","https://github.com/webpack/source-list-map.git","https://github.com/webpack/source-list-map","Tobias Koppers @sokra"],["source-map-loader","0.2.4","MIT","https://github.com/webpack-contrib/source-map-loader.git","https://github.com/webpack-contrib/source-map-loader","Tobias Koppers @sokra"],["source-map-resolve","0.5.3","MIT","https://github.com/lydell/source-map-resolve.git","Unknown","Simon Lydell"],["source-map-support","0.5.19","MIT","https://github.com/evanw/node-source-map-support","Unknown","Unknown"],["source-map-support","0.5.16","MIT","https://github.com/evanw/node-source-map-support","Unknown","Unknown"],["source-map-support","0.5.13","MIT","https://github.com/evanw/node-source-map-support","Unknown","Unknown"],["source-map-url","0.4.0","MIT","https://github.com/lydell/source-map-url.git","Unknown","Simon Lydell"],["sourcemap-codec","1.4.8","MIT","https://github.com/Rich-Harris/sourcemap-codec","https://github.com/Rich-Harris/sourcemap-codec","Rich Harris"],["sparse-bitfield","3.0.3","MIT","https://github.com/mafintosh/sparse-bitfield.git","https://github.com/mafintosh/sparse-bitfield","Mathias Buus"],["spawn-command","0.0.2-1","MIT","https://github.com/mmalecki/spawn-command","Unknown","Maciej Małecki"],["spdx-expression-parse","3.0.1","MIT","https://github.com/jslicense/spdx-expression-parse.js.git","https://kemitchell.com","Kyle E. Mitchell"],["spdy","4.0.2","MIT","git://github.com/indutny/node-spdy.git","https://github.com/indutny/node-spdy","Fedor Indutny"],["spdy-transport","3.0.0","MIT","git://github.com/spdy-http2/spdy-transport.git","https://github.com/spdy-http2/spdy-transport","Fedor Indutny"],["speed-measure-webpack-plugin","1.3.1","MIT","git+https://github.com/stephencookdev/speed-measure-webpack-plugin.git","https://github.com/stephencookdev/speed-measure-webpack-plugin#readme","Stephen Cook"],["split","0.3.3","MIT","git://github.com/dominictarr/split.git","http://github.com/dominictarr/split","Dominic Tarr"],["split-string","3.1.0","MIT","https://github.com/jonschlinkert/split-string.git","https://github.com/jonschlinkert/split-string","Jon Schlinkert"],["sshpk","1.16.1","MIT","git+https://github.com/joyent/node-sshpk.git","https://github.com/arekinath/node-sshpk#readme","Joyent, Inc"],["stack-generator","2.0.5","MIT","git://github.com/stacktracejs/stack-generator.git","https://www.stacktracejs.com/","Unknown"],["stack-trace","0.0.10","MIT","git://github.com/felixge/node-stack-trace.git","https://github.com/felixge/node-stack-trace","Felix Geisendörfer"],["stackframe","1.2.0","MIT","git://github.com/stacktracejs/stackframe.git","https://www.stacktracejs.com/","Unknown"],["stacktrace-gps","3.0.4","MIT","git://github.com/stacktracejs/stacktrace-gps.git","https://www.stacktracejs.com/","Unknown"],["stacktrace-js","2.0.1","MIT","git://github.com/stacktracejs/stacktrace.js.git","https://www.stacktracejs.com/","Unknown"],["start","5.1.0","MIT","https://github.com/start-runner/start.git","https://github.com/start-runner/start","Kir Belevich"],["start-server-and-test","1.10.11","MIT","https://github.com/bahmutov/start-server-and-test.git","https://github.com/bahmutov/start-server-and-test#readme","Gleb Bahmutov"],["static-extend","0.1.2","MIT","https://github.com/jonschlinkert/static-extend.git","https://github.com/jonschlinkert/static-extend","Jon Schlinkert"],["statuses","1.5.0","MIT","https://github.com/jshttp/statuses.git","Unknown","Unknown"],["stdout-stream","1.4.1","MIT","https://github.com/mafintosh/stdout-stream.git","Unknown","Unknown"],["stream-browserify","2.0.2","MIT","git://github.com/browserify/stream-browserify.git","https://github.com/browserify/stream-browserify","James Halliday"],["stream-combiner","0.0.4","MIT","git://github.com/dominictarr/stream-combiner.git","https://github.com/dominictarr/stream-combiner","'Dominic Tarr'"],["stream-each","1.2.3","MIT","https://github.com/mafintosh/stream-each.git","https://github.com/mafintosh/stream-each","Mathias Buus"],["stream-http","2.8.3","MIT","git://github.com/jhiesey/stream-http.git","https://github.com/jhiesey/stream-http#readme","John Hiesey"],["stream-meter","1.0.4","MIT","https://github.com/brycebaril/node-stream-meter.git","Unknown","Bryce B. Baril"],["stream-shift","1.0.1","MIT","https://github.com/mafintosh/stream-shift.git","https://github.com/mafintosh/stream-shift","Mathias Buus"],["streamroller","1.0.6","MIT","https://github.com/nomiddlename/streamroller.git","Unknown","Gareth Jones"],["streamsearch","0.1.2","MIT","http://github.com/mscdex/streamsearch.git","Unknown","Brian White"],["strict-uri-encode","1.1.0","MIT","https://github.com/kevva/strict-uri-encode.git","github.com/kevva","Kevin Mårtensson"],["string_decoder","1.1.1","MIT","git://github.com/nodejs/string_decoder.git","https://github.com/nodejs/string_decoder","Unknown"],["string_decoder","1.3.0","MIT","git://github.com/nodejs/string_decoder.git","https://github.com/nodejs/string_decoder","Unknown"],["string_decoder","0.10.31","MIT","git://github.com/rvagg/string_decoder.git","https://github.com/rvagg/string_decoder","Unknown"],["string-width","1.0.2","MIT","https://github.com/sindresorhus/string-width.git","sindresorhus.com","Sindre Sorhus"],["string-width","4.2.0","MIT","https://github.com/sindresorhus/string-width.git","sindresorhus.com","Sindre Sorhus"],["string-width","3.1.0","MIT","https://github.com/sindresorhus/string-width.git","sindresorhus.com","Sindre Sorhus"],["string-width","2.1.1","MIT","https://github.com/sindresorhus/string-width.git","sindresorhus.com","Sindre Sorhus"],["string.prototype.trimend","1.0.1","MIT","git://github.com/es-shims/String.prototype.trimEnd.git","Unknown","Jordan Harband"],["string.prototype.trimstart","1.0.1","MIT","git://github.com/es-shims/String.prototype.trimStart.git","Unknown","Jordan Harband"],["strip-ansi","4.0.0","MIT","https://github.com/chalk/strip-ansi.git","sindresorhus.com","Sindre Sorhus"],["strip-ansi","5.2.0","MIT","https://github.com/chalk/strip-ansi.git","sindresorhus.com","Sindre Sorhus"],["strip-ansi","3.0.1","MIT","https://github.com/chalk/strip-ansi.git","sindresorhus.com","Sindre Sorhus"],["strip-ansi","6.0.0","MIT","https://github.com/chalk/strip-ansi.git","sindresorhus.com","Sindre Sorhus"],["strip-bom","3.0.0","MIT","https://github.com/sindresorhus/strip-bom.git","sindresorhus.com","Sindre Sorhus"],["strip-bom","4.0.0","MIT","https://github.com/sindresorhus/strip-bom.git","sindresorhus.com","Sindre Sorhus"],["strip-bom","2.0.0","MIT","https://github.com/sindresorhus/strip-bom.git","sindresorhus.com","Sindre Sorhus"],["strip-dirs","2.1.0","MIT","https://github.com/shinnn/node-strip-dirs.git","https://github.com/shinnn","Shinnosuke Watanabe"],["strip-eof","1.0.0","MIT","https://github.com/sindresorhus/strip-eof.git","sindresorhus.com","Sindre Sorhus"],["strip-final-newline","2.0.0","MIT","https://github.com/sindresorhus/strip-final-newline.git","sindresorhus.com","Sindre Sorhus"],["strip-indent","1.0.1","MIT","https://github.com/sindresorhus/strip-indent.git","http://sindresorhus.com","Sindre Sorhus"],["strip-json-comments","2.0.1","MIT","https://github.com/sindresorhus/strip-json-comments.git","sindresorhus.com","Sindre Sorhus"],["style-loader","1.0.0","MIT","https://github.com/webpack-contrib/style-loader.git","https://github.com/webpack-contrib/style-loader","Tobias Koppers @sokra"],["stylus","0.54.5","MIT","git://github.com/stylus/stylus","https://github.com/stylus/stylus","TJ Holowaychuk"],["stylus-loader","3.0.2","MIT","**************:shama/stylus-loader.git","http://dontkry.com","Kyle Robinson Young"],["superagent","4.1.0","MIT","git://github.com/visionmedia/superagent.git","Unknown","TJ Holowaychuk"],["superagent","3.8.3","MIT","git://github.com/visionmedia/superagent.git","Unknown","TJ Holowaychuk"],["supertest","4.0.2","MIT","https://github.com/visionmedia/supertest.git","Unknown","TJ Holowaychuk"],["supports-color","5.5.0","MIT","https://github.com/chalk/supports-color.git","sindresorhus.com","Sindre Sorhus"],["supports-color","6.1.0","MIT","https://github.com/chalk/supports-color.git","sindresorhus.com","Sindre Sorhus"],["supports-color","7.1.0","MIT","https://github.com/chalk/supports-color.git","sindresorhus.com","Sindre Sorhus"],["supports-color","6.0.0","MIT","https://github.com/chalk/supports-color.git","sindresorhus.com","Sindre Sorhus"],["supports-color","7.2.0","MIT","https://github.com/chalk/supports-color.git","sindresorhus.com","Sindre Sorhus"],["supports-color","2.0.0","MIT","https://github.com/chalk/supports-color.git","sindresorhus.com","Sindre Sorhus"],["supports-hyperlinks","1.0.1","MIT","https://github.com/jamestalmage/supports-hyperlinks.git","github.com/jamestalmage","James Talmage"],["swagger-ui-express","4.1.2","MIT","**************:scottie1984/swagger-ui-express.git","https://github.com/scottie1984/swagger-ui-express","Stephen Scott"],["symbol-observable","1.2.0","MIT","https://github.com/blesh/symbol-observable.git","Unknown","Ben Lesh"],["tapable","1.1.3","MIT","http://github.com/webpack/tapable.git","https://github.com/webpack/tapable","Tobias Koppers @sokra"],["tar-stream","1.6.2","MIT","git+https://github.com/mafintosh/tar-stream.git","https://github.com/mafintosh/tar-stream","Mathias Buus"],["tcomb","3.2.29","MIT","https://github.com/gcanti/tcomb.git","https://github.com/gcanti/tcomb","Giulio Canti"],["tcomb-validation","3.4.1","MIT","https://github.com/gcanti/tcomb-validation.git","https://github.com/gcanti/tcomb-validation","Giulio Canti"],["term-size","2.2.0","MIT","https://github.com/sindresorhus/term-size.git","sindresorhus.com","Sindre Sorhus"],["term-size","1.2.0","MIT","https://github.com/sindresorhus/term-size.git","sindresorhus.com","Sindre Sorhus"],["terminal-link","1.3.0","MIT","https://github.com/sindresorhus/terminal-link.git","sindresorhus.com","Sindre Sorhus"],["terser-webpack-plugin","1.4.5","MIT","https://github.com/webpack-contrib/terser-webpack-plugin.git","https://github.com/webpack-contrib/terser-webpack-plugin","webpack Contrib Team"],["text-hex","1.0.0","MIT","https://github.com/3rd-Eden/text-hex","https://github.com/3rd-Eden/text-hex","Arnout Kazemier"],["throttleit","1.0.0","MIT","git://github.com/component/throttle.git","Unknown","Unknown"],["through","2.3.8","MIT","https://github.com/dominictarr/through.git","https://github.com/dominictarr/through","Dominic Tarr"],["through2","2.0.5","MIT","https://github.com/rvagg/through2.git","https://github.com/rvagg","Rod Vagg"],["through2-filter","3.0.0","MIT","**************:brycebaril/through2-filter.git","Unknown","Bryce B. Baril"],["thunky","1.1.0","MIT","git://github.com/mafintosh/thunky.git","https://github.com/mafintosh/thunky#readme","Mathias Buus Madsen"],["timed-out","4.0.1","MIT","https://github.com/floatdrop/timed-out.git","Unknown","Vsevolod Strukchinsky"],["timers-browserify","2.0.11","MIT","git://github.com/jryans/timers-browserify.git","https://github.com/jryans/timers-browserify","J. Ryan Stinnett"],["tiny-emitter","2.1.0","MIT","https://github.com/scottcorgan/tiny-emitter.git","Unknown","Scott Corgan"],["tmp","0.1.0","MIT","https://github.com/raszi/node-tmp.git","http://github.com/raszi/node-tmp","KARASZI István"],["tmp","0.0.33","MIT","https://github.com/raszi/node-tmp.git","http://github.com/raszi/node-tmp","KARASZI István"],["to-absolute-glob","2.0.2","MIT","https://github.com/jonschlinkert/to-absolute-glob.git","https://github.com/jonschlinkert/to-absolute-glob","Jon Schlinkert"],["to-array","0.1.4","MIT","git://github.com/Raynos/to-array.git","https://github.com/Raynos/to-array","Raynos"],["to-arraybuffer","1.0.1","MIT","git://github.com/jhiesey/to-arraybuffer.git","https://github.com/jhiesey/to-arraybuffer#readme","John Hiesey"],["to-buffer","1.1.1","MIT","https://github.com/mafintosh/to-buffer.git","https://github.com/mafintosh/to-buffer","Mathias Buus"],["to-fast-properties","2.0.0","MIT","https://github.com/sindresorhus/to-fast-properties.git","sindresorhus.com","Sindre Sorhus"],["to-fast-properties","1.0.3","MIT","https://github.com/sindresorhus/to-fast-properties.git","sindresorhus.com","Sindre Sorhus"],["to-object-path","0.3.0","MIT","https://github.com/jonschlinkert/to-object-path.git","https://github.com/jonschlinkert/to-object-path","Jon Schlinkert"],["to-regex","3.0.2","MIT","https://github.com/jonschlinkert/to-regex.git","https://github.com/jonschlinkert/to-regex","Jon Schlinkert"],["to-regex-range","5.0.1","MIT","https://github.com/micromatch/to-regex-range.git","https://github.com/micromatch/to-regex-range","Jon Schlinkert"],["to-regex-range","2.1.1","MIT","https://github.com/micromatch/to-regex-range.git","https://github.com/micromatch/to-regex-range","Jon Schlinkert"],["toidentifier","1.0.0","MIT","https://github.com/component/toidentifier.git","Unknown","Douglas Christopher Wilson"],["tree-kill","1.2.1","MIT","git://github.com/pkrumins/node-tree-kill.git","https://github.com/pkrumins/node-tree-kill","Peteris Krumins"],["tree-kill","1.2.2","MIT","git://github.com/pkrumins/node-tree-kill.git","https://github.com/pkrumins/node-tree-kill","Peteris Krumins"],["trim-newlines","1.0.0","MIT","https://github.com/sindresorhus/trim-newlines.git","sindresorhus.com","Sindre Sorhus"],["trim-right","1.0.1","MIT","https://github.com/sindresorhus/trim-right.git","sindresorhus.com","Sindre Sorhus"],["triple-beam","1.3.0","MIT","git+https://github.com/winstonjs/triple-beam.git","https://github.com/winstonjs/triple-beam#readme","Charlie Robbins"],["tryer","1.0.1","MIT","git+https://gitlab.com/philbooth/tryer.git","https://gitlab.com/philbooth/tryer","Phil Booth"],["ts-custom-error","3.2.0","MIT","https://github.com/adriengibrat/ts-custom-error.git","Unknown","Adrien Gibrat"],["ts-loader","6.2.0","MIT","https://github.com/TypeStrong/ts-loader.git","https://github.com/TypeStrong/ts-loader","John Reilly"],["ts-md5","1.2.7","MIT","git+https://github.com/cotag/ts-md5.git","https://github.com/cotag/ts-md5","Unknown"],["ts-node","8.4.1","MIT","git://github.com/TypeStrong/ts-node.git","https://github.com/TypeStrong/ts-node","Blake Embrey"],["ts-node","8.6.2","MIT","git://github.com/TypeStrong/ts-node.git","https://github.com/TypeStrong/ts-node","Blake Embrey"],["tsconfig-paths","3.9.0","MIT","https://github.com/dividab/tsconfig-paths","Unknown","Jonas Kello"],["tsutils","3.17.1","MIT","https://github.com/ajafff/tsutils","Unknown","Klaus Meinhardt"],["tsutils","2.29.0","MIT","https://github.com/ajafff/tsutils","Unknown","Klaus Meinhardt"],["tsutils-etc","1.3.1","MIT","https://github.com/cartant/tsutils-etc.git","https://github.com/cartant/tsutils-etc","Nicholas Jamieson"],["tty-browserify","0.0.0","MIT","git://github.com/substack/tty-browserify.git","https://github.com/substack/tty-browserify","James Halliday"],["type-check","0.3.2","MIT","git://github.com/gkz/type-check.git","https://github.com/gkz/type-check","George Zahariev"],["type-detect","4.0.8","MIT","git+ssh://**************/chaijs/type-detect.git","http://alogicalparadox.com","Jake Luer"],["type-is","1.6.18","MIT","https://github.com/jshttp/type-is.git","Unknown","Unknown"],["typedarray","0.0.6","MIT","git://github.com/substack/typedarray.git","https://github.com/substack/typedarray","James Halliday"],["typedarray-to-buffer","3.1.5","MIT","git://github.com/feross/typedarray-to-buffer.git","http://feross.org/","Feross Aboukhadijeh"],["ua-parser-js","0.7.20","MIT","https://github.com/faisalman/ua-parser-js.git","http://github.com/faisalman/ua-parser-js","Faisal Salman"],["uid-safe","2.1.5","MIT","https://github.com/crypto-utils/uid-safe.git","Unknown","Unknown"],["uid2","0.0.3","MIT","Unknown","Unknown","Unknown"],["ultron","1.1.1","MIT","https://github.com/unshiftio/ultron","https://github.com/unshiftio/ultron","Arnout Kazemier"],["unbzip2-stream","1.4.3","MIT","https://github.com/regular/unbzip2-stream.git","Unknown","Jan Bölsche"],["unc-path-regex","0.1.2","MIT","https://github.com/regexhq/unc-path-regex.git","https://github.com/regexhq/unc-path-regex","Jon Schlinkert"],["undefsafe","2.0.3","MIT","https://github.com/remy/undefsafe.git","Unknown","Remy Sharp"],["underscore","1.11.0","MIT","git://github.com/jashkenas/underscore.git","https://underscorejs.org/","Jeremy Ashkenas"],["unicode-canonical-property-names-ecmascript","1.0.4","MIT","https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git","https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript","Mathias Bynens"],["unicode-match-property-ecmascript","1.0.4","MIT","https://github.com/mathiasbynens/unicode-match-property-ecmascript.git","https://github.com/mathiasbynens/unicode-match-property-ecmascript","Mathias Bynens"],["unicode-match-property-value-ecmascript","1.2.0","MIT","https://github.com/mathiasbynens/unicode-match-property-value-ecmascript.git","https://github.com/mathiasbynens/unicode-match-property-value-ecmascript","Mathias Bynens"],["unicode-property-aliases-ecmascript","1.1.0","MIT","https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git","https://github.com/mathiasbynens/unicode-property-aliases-ecmascript","Mathias Bynens"],["union-value","1.0.1","MIT","https://github.com/jonschlinkert/union-value.git","https://github.com/jonschlinkert/union-value","Jon Schlinkert"],["unique-stream","2.3.1","MIT","https://github.com/eugeneware/unique-stream.git","Unknown","Eugene Ware"],["unique-string","1.0.0","MIT","https://github.com/sindresorhus/unique-string.git","sindresorhus.com","Sindre Sorhus"],["unique-temp-dir","1.0.0","MIT","https://github.com/jamestalmage/unique-temp-dir.git","github.com/jamestalmage","James Talmage"],["universal-analytics","0.4.23","MIT","**************:peaksandpies/universal-analytics.git","Unknown","Jörg Tillmann"],["universalify","0.1.2","MIT","git+https://github.com/RyanZim/universalify.git","https://github.com/RyanZim/universalify#readme","Ryan Zimmerman"],["unpipe","1.0.0","MIT","https://github.com/stream-utils/unpipe.git","Unknown","Douglas Christopher Wilson"],["unset-value","1.0.0","MIT","https://github.com/jonschlinkert/unset-value.git","https://github.com/jonschlinkert/unset-value","Jon Schlinkert"],["untildify","4.0.0","MIT","https://github.com/sindresorhus/untildify.git","sindresorhus.com","Sindre Sorhus"],["unzip-response","2.0.1","MIT","https://github.com/sindresorhus/unzip-response.git","Unknown","Unknown"],["upath","1.2.0","MIT","git://github.com/anodynos/upath","http://github.com/anodynos/upath/","Angelos Pikoulas"],["urix","0.1.0","MIT","https://github.com/lydell/urix.git","Unknown","Simon Lydell"],["url","0.11.0","MIT","https://github.com/defunctzombie/node-url.git","Unknown","Unknown"],["url-parse","1.4.7","MIT","https://github.com/unshiftio/url-parse.git","Unknown","Arnout Kazemier"],["url-parse-lax","1.0.0","MIT","https://github.com/sindresorhus/url-parse-lax.git","sindresorhus.com","Sindre Sorhus"],["use","3.1.1","MIT","https://github.com/jonschlinkert/use.git","https://github.com/jonschlinkert/use","Jon Schlinkert"],["useragent","2.3.0","MIT","http://github.com/3rd-Eden/useragent.git","Unknown","Arnout Kazemier"],["util","0.11.1","MIT","git://github.com/defunctzombie/node-util","https://github.com/defunctzombie/node-util","Joyent"],["util","0.10.4","MIT","git://github.com/defunctzombie/node-util","https://github.com/defunctzombie/node-util","Joyent"],["util","0.10.3","MIT","git://github.com/defunctzombie/node-util","https://github.com/defunctzombie/node-util","Joyent"],["util-deprecate","1.0.2","MIT","git://github.com/TooTallNate/util-deprecate.git","https://github.com/TooTallNate/util-deprecate","Nathan Rajlich"],["util-promisify","2.1.0","MIT","https://github.com/juliangruber/util-promisify.git","Unknown","Unknown"],["util.promisify","1.0.1","MIT","git+https://github.com/ljharb/util.promisify.git","https://github.com/ljharb/util.promisify#readme","Jordan Harband"],["utils-merge","1.0.1","MIT","git://github.com/jaredhanson/utils-merge.git","http://www.jaredhanson.net/","Jared Hanson"],["uuid","3.3.3","MIT","https://github.com/kelektiv/node-uuid.git","Unknown","Unknown"],["uuid","7.0.3","MIT","https://github.com/uuidjs/uuid.git","Unknown","Unknown"],["uuid","3.4.0","MIT","https://github.com/uuidjs/uuid.git","Unknown","Unknown"],["validator","13.1.1","MIT","https://github.com/chriso/validator.js.git","https://github.com/chriso/validator.js","Chris O'Hara"],["validator","13.0.0","MIT","https://github.com/chriso/validator.js.git","https://github.com/chriso/validator.js","Chris O'Hara"],["validator","10.11.0","MIT","https://github.com/chriso/validator.js.git","https://github.com/chriso/validator.js","Chris O'Hara"],["validator","11.1.0","MIT","https://github.com/chriso/validator.js.git","https://github.com/chriso/validator.js","Chris O'Hara"],["vary","1.1.2","MIT","https://github.com/jshttp/vary.git","Unknown","Douglas Christopher Wilson"],["verror","1.10.0","MIT","git://github.com/davepacheco/node-verror.git","Unknown","Unknown"],["vm-browserify","1.1.2","MIT","http://github.com/substack/vm-browserify.git","http://substack.net","James Halliday"],["void-elements","2.0.1","MIT","https://github.com/hemanth/void-elements.git","https://github.com/hemanth/void-elements","hemanth.hm"],["wait-for-expect","1.3.0","MIT","https://github.com/TheBrainFamily/wait-for-expect.git","https://github.com/TheBrainFamily/wait-for-expect#readme","Lukasz Gandecki"],["wait-on","4.0.0","MIT","http://github.com/jeffbski/wait-on.git","Unknown","Jeff Barczewski"],["watchpack","1.7.4","MIT","https://github.com/webpack/watchpack.git","https://github.com/webpack/watchpack","Tobias Koppers @sokra"],["watchpack-chokidar2","2.0.0","MIT","https://github.com/webpack/watchpack.git","https://github.com/webpack/watchpack","Tobias Koppers @sokra"],["wbuf","1.7.3","MIT","**************:indutny/wbuf","https://github.com/indutny/wbuf","Fedor Indutny"],["webpack","4.42.0","MIT","https://github.com/webpack/webpack.git","https://github.com/webpack/webpack","Tobias Koppers @sokra"],["webpack","4.39.2","MIT","https://github.com/webpack/webpack.git","https://github.com/webpack/webpack","Tobias Koppers @sokra"],["webpack-bundle-analyzer","3.6.1","MIT","git+https://github.com/webpack-contrib/webpack-bundle-analyzer.git","https://github.com/webpack-contrib/webpack-bundle-analyzer","Yury Grunin"],["webpack-core","0.6.9","MIT","https://github.com/webpack/core.git","http://github.com/webpack/core","Tobias Koppers @sokra"],["webpack-dev-middleware","3.7.0","MIT","https://github.com/webpack/webpack-dev-middleware.git","https://github.com/webpack/webpack-dev-middleware","Tobias Koppers @sokra"],["webpack-dev-middleware","3.7.2","MIT","https://github.com/webpack/webpack-dev-middleware.git","https://github.com/webpack/webpack-dev-middleware","Tobias Koppers @sokra"],["webpack-dev-server","3.8.0","MIT","https://github.com/webpack/webpack-dev-server.git","https://github.com/webpack/webpack-dev-server#readme","Tobias Koppers @sokra"],["webpack-log","2.0.0","MIT","https://github.com/webpack-contrib/webpack-log.git","https://github.com/webpack-contrib/webpack-log#readme","Andrew Powell"],["webpack-merge","4.2.2","MIT","https://github.com/survivejs/webpack-merge.git","https://github.com/survivejs/webpack-merge","Juho Vepsalainen"],["webpack-merge","4.2.1","MIT","https://github.com/survivejs/webpack-merge.git","https://github.com/survivejs/webpack-merge","Juho Vepsalainen"],["webpack-sources","1.4.3","MIT","git+https://github.com/webpack/webpack-sources.git","https://github.com/webpack/webpack-sources#readme","Tobias Koppers @sokra"],["webpack-subresource-integrity","1.1.0-rc.6","MIT","https://github.com/waysact/webpack-subresource-integrity.git","https://github.com/waysact/webpack-subresource-integrity#readme","Julian Scheid"],["when","3.6.4","MIT","https://github.com/cujojs/when","http://cujojs.com/","Unknown"],["widest-line","3.1.0","MIT","https://github.com/sindresorhus/widest-line.git","sindresorhus.com","Sindre Sorhus"],["widest-line","2.0.1","MIT","https://github.com/sindresorhus/widest-line.git","sindresorhus.com","Sindre Sorhus"],["window-size","0.2.0","MIT","https://github.com/jonschlinkert/window-size.git","https://github.com/jonschlinkert/window-size","Jon Schlinkert"],["winston","3.2.1","MIT","https://github.com/winstonjs/winston.git","Unknown","Charlie Robbins"],["winston-transport","4.4.0","MIT","**************:winstonjs/winston-transport.git","https://github.com/winstonjs/winston-transport#readme","Charlie Robbins"],["word-wrap","1.2.3","MIT","https://github.com/jonschlinkert/word-wrap.git","https://github.com/jonschlinkert/word-wrap","Jon Schlinkert"],["wordwrap","1.0.0","MIT","git://github.com/substack/node-wordwrap.git","http://substack.net","James Halliday"],["wordwrap","0.0.3","MIT","git://github.com/substack/node-wordwrap.git","http://substack.net","James Halliday"],["worker-farm","1.7.0","MIT","https://github.com/rvagg/node-worker-farm.git","https://github.com/rvagg/node-worker-farm","Unknown"],["wrap-ansi","2.1.0","MIT","https://github.com/chalk/wrap-ansi.git","sindresorhus.com","Sindre Sorhus"],["wrap-ansi","6.2.0","MIT","https://github.com/chalk/wrap-ansi.git","sindresorhus.com","Sindre Sorhus"],["wrap-ansi","5.1.0","MIT","https://github.com/chalk/wrap-ansi.git","sindresorhus.com","Sindre Sorhus"],["wrap-ansi","3.0.1","MIT","https://github.com/chalk/wrap-ansi.git","sindresorhus.com","Sindre Sorhus"],["ws","6.1.2","MIT","https://github.com/websockets/ws.git","https://github.com/websockets/ws","Einar Otto Stangvik"],["ws","6.2.1","MIT","https://github.com/websockets/ws.git","https://github.com/websockets/ws","Einar Otto Stangvik"],["ws","3.3.3","MIT","https://github.com/websockets/ws.git","https://github.com/websockets/ws","Einar Otto Stangvik"],["xdg-basedir","3.0.0","MIT","https://github.com/sindresorhus/xdg-basedir.git","sindresorhus.com","Sindre Sorhus"],["xhr2","0.1.4","MIT","https://github.com/pwnall/node-xhr2.git","https://github.com/pwnall/node-xhr2","Victor Costan"],["xml","1.0.1","MIT","http://github.com/dylang/node-xml","http://github.com/dylang/node-xml","Dylan Greene"],["xml2js","0.4.22","MIT","https://github.com/Leonidas-from-XIV/node-xml2js.git","https://github.com/Leonidas-from-XIV/node-xml2js","Marek Kubica"],["xmlbuilder","8.2.2","MIT","git://github.com/oozcitak/xmlbuilder-js.git","http://github.com/oozcitak/xmlbuilder-js","Ozgur Ozcitak"],["xmlbuilder","11.0.1","MIT","git://github.com/oozcitak/xmlbuilder-js.git","http://github.com/oozcitak/xmlbuilder-js","Ozgur Ozcitak"],["xmlhttprequest-ssl","1.5.5","MIT","git://github.com/mjwwit/node-XMLHttpRequest.git","Unknown","Michael de Wit"],["xregexp","4.3.0","MIT","https://github.com/slevithan/xregexp.git","http://xregexp.com/","Steven Levithan"],["xtend","4.0.2","MIT","git://github.com/Raynos/xtend.git","https://github.com/Raynos/xtend","Raynos"],["yargs","4.8.1","MIT","http://github.com/yargs/yargs.git","http://yargs.js.org/","Unknown"],["yargs","15.4.1","MIT","https://github.com/yargs/yargs.git","https://yargs.js.org/","Unknown"],["yargs","13.1.0","MIT","https://github.com/yargs/yargs.git","https://yargs.js.org/","Unknown"],["yargs","14.2.3","MIT","https://github.com/yargs/yargs.git","https://yargs.js.org/","Unknown"],["yargs","13.3.2","MIT","https://github.com/yargs/yargs.git","https://yargs.js.org/","Unknown"],["yargs","13.3.0","MIT","https://github.com/yargs/yargs.git","https://yargs.js.org/","Unknown"],["yargs","12.0.5","MIT","https://github.com/yargs/yargs.git","https://yargs.js.org/","Unknown"],["yargs","7.1.1","MIT","http://github.com/yargs/yargs.git","http://yargs.js.org/","Unknown"],["yargs-unparser","1.6.0","MIT","**************:yargs/yargs-unparser.git","https://github.com/yargs/yargs-unparser","André Cruz"],["yauzl","2.10.0","MIT","https://github.com/thejoshwolfe/yauzl.git","https://github.com/thejoshwolfe/yauzl","Josh Wolfe"],["yeast","0.1.2","MIT","https://github.com/unshiftio/yeast.git","https://github.com/unshiftio/yeast","Arnout Kazemier"],["yn","3.1.1","MIT","https://github.com/sindresorhus/yn.git","sindresorhus.com","Sindre Sorhus"],["zone.js","0.9.1","MIT","git://github.com/angular/zone.js.git","Unknown","Brian Ford"],["@angularclass/hmr","2.1.3","Apache-2.0","git+https://github.com/angularclass/angular-hmr.git","https://github.com/angularclass/angular-hmr#readme","PatrickJS"],["@cypress/request","2.88.5","Apache-2.0","https://github.com/cypress-io/request.git","Unknown","Mikeal Rogers"],["@xtuc/long","4.2.2","Apache-2.0","https://github.com/dcodeIO/long.js.git","Unknown","Daniel Wirtz"],["adler-32","1.2.0","Apache-2.0","git://github.com/SheetJS/js-adler32.git","http://sheetjs.com/opensource","sheetjs"],["ansi-html","0.0.7","Apache-2.0","git://github.com/Tjatse/ansi-html.git","https://github.com/Tjatse/ansi-html","Tjatse"],["aria-query","3.0.0","Apache-2.0","git+https://github.com/A11yance/aria-query.git","https://github.com/A11yance/aria-query#readme","Jesse Beach"],["aws-sign2","0.7.0","Apache-2.0","https://github.com/mikeal/aws-sign","http://www.futurealoof.com","Mikeal Rogers"],["axobject-query","2.2.0","Apache-2.0","git+https://github.com/A11yance/axobject-query.git","https://github.com/A11yance/axobject-query#readme","Jesse Beach"],["bson","1.1.5","Apache-2.0","https://github.com/mongodb/js-bson.git","Unknown","Christian Amor Kvalheim"],["caseless","0.12.0","Apache-2.0","https://github.com/mikeal/caseless","Unknown","Mikeal Rogers"],["cassandra-driver","4.2.0","Apache-2.0","https://github.com/datastax/nodejs-driver.git","Unknown","DataStax"],["cfb","1.2.0","Apache-2.0","git://github.com/SheetJS/js-cfb.git","http://sheetjs.com/","sheetjs"],["codepage","1.14.0","Apache-2.0","git://github.com/SheetJS/js-codepage.git","http://sheetjs.com/opensource","SheetJS"],["crc-32","1.2.0","Apache-2.0","git://github.com/SheetJS/js-crc32.git","http://sheetjs.com/opensource","sheetjs"],["denque","1.4.1","Apache-2.0","git+https://github.com/invertase/denque.git","https://github.com/invertase/denque#readme","Invertase"],["detect-libc","1.0.3","Apache-2.0","git://github.com/lovell/detect-libc","Unknown","Lovell Fuller"],["ecdsa-sig-formatter","1.0.11","Apache-2.0","git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git","https://github.com/Brightspace/node-ecdsa-sig-formatter#readme","D2L Corporation"],["ejs","2.7.4","Apache-2.0","git://github.com/mde/ejs.git","https://github.com/mde/ejs","Matthew Eernisse"],["exit-on-epipe","1.0.1","Apache-2.0","git://github.com/SheetJS/node-exit-on-epipe.git","Unknown","sheetjs"],["fast-diff","1.1.2","Apache-2.0","https://github.com/jhchen/fast-diff","Unknown","Jason Chen"],["faye-websocket","0.11.3","Apache-2.0","git://github.com/faye/faye-websocket-node.git","https://github.com/faye/faye-websocket-node","James Coglan"],["forever-agent","0.6.1","Apache-2.0","https://github.com/mikeal/forever-agent","http://www.futurealoof.com","Mikeal Rogers"],["frac","1.1.2","Apache-2.0","git://github.com/SheetJS/frac.git","http://sheetjs.com/opensource","SheetJS"],["human-signals","1.1.1","Apache-2.0","https://github.com/ehmicky/human-signals.git","https://git.io/JeluP","ehmicky"],["jasmine-spec-reporter","4.2.1","Apache-2.0","https://github.com/bcaudan/jasmine-spec-reporter","https://github.com/bcaudan/jasmine-spec-reporter","Bastien Caudan"],["kareem","2.3.1","Apache-2.0","git://github.com/vkarpov15/kareem.git","Unknown","Valeri Karpov"],["less","3.9.0","Apache-2.0","https://github.com/less/less.js.git","http://lesscss.org/","Alexis Sellier"],["log4js","4.5.1","Apache-2.0","https://github.com/log4js-node/log4js-node.git","https://log4js-node.github.io/log4js-node/","Gareth Jones"],["long","2.4.0","Apache-2.0","https://github.com/dcodeIO/Long.js.git","Unknown","Daniel Wirtz"],["mongodb","3.5.3","Apache-2.0","**************:mongodb/node-mongodb-native.git","https://github.com/mongodb/node-mongodb-native","Unknown"],["mongodb","3.3.2","Apache-2.0","**************:mongodb/node-mongodb-native.git","https://github.com/mongodb/node-mongodb-native","Unknown"],["mongodb","3.6.1","Apache-2.0","**************:mongodb/node-mongodb-native.git","https://github.com/mongodb/node-mongodb-native","Unknown"],["mongoose-legacy-pluralize","1.0.2","Apache-2.0","https://github.com/vkarpov15/mongoose-legacy-pluralize.git","https://github.com/vkarpov15/mongoose-legacy-pluralize","Valeri Karpov"],["node-environment-flags","1.0.5","Apache-2.0","https://github.com/boneskull/node-environment-flags.git","https://boneskull.com/","Christopher Hiller"],["oauth-sign","0.9.0","Apache-2.0","https://github.com/mikeal/oauth-sign","http://www.futurealoof.com","Mikeal Rogers"],["pause-stream","0.0.11","Apache-2.0","git://github.com/dominictarr/pause-stream.git","dominictarr.com","Dominic Tarr"],["printj","1.1.2","Apache-2.0","git://github.com/SheetJS/printj.git","http://sheetjs.com/opensource","sheetjs"],["puppeteer","1.20.0","Apache-2.0","https://github.com/GoogleChrome/puppeteer.git","Unknown","The Chromium Authors"],["reflect-metadata","0.1.13","Apache-2.0","https://github.com/rbuckton/reflect-metadata.git","http://rbuckton.github.io/reflect-metadata","Ron Buckton"],["request","2.88.0","Apache-2.0","https://github.com/request/request.git","Unknown","Mikeal Rogers"],["request","2.88.2","Apache-2.0","https://github.com/request/request.git","Unknown","Mikeal Rogers"],["require_optional","1.0.1","Apache-2.0","https://github.com/christkv/require_optional.git","https://github.com/christkv/require_optional","Christian Kvalheim Amor"],["rx-lite","3.1.2","Apache-2.0","https://github.com/Reactive-Extensions/RxJS.git","https://github.com/Reactive-Extensions/RxJS","Cloud Programmability Team"],["rxjs","6.5.3","Apache-2.0","https://github.com/reactivex/rxjs.git","https://github.com/ReactiveX/RxJS","Ben Lesh"],["rxjs","6.5.2","Apache-2.0","https://github.com/reactivex/rxjs.git","https://github.com/ReactiveX/RxJS","Ben Lesh"],["rxjs","6.4.0","Apache-2.0","https://github.com/reactivex/rxjs.git","https://github.com/ReactiveX/RxJS","Ben Lesh"],["rxjs","6.6.3","Apache-2.0","https://github.com/reactivex/rxjs.git","https://github.com/ReactiveX/RxJS","Ben Lesh"],["rxjs","6.6.2","Apache-2.0","https://github.com/reactivex/rxjs.git","https://github.com/ReactiveX/RxJS","Ben Lesh"],["spdx-correct","3.1.1","Apache-2.0","https://github.com/jslicense/spdx-correct.js.git","https://kemitchell.com","Kyle E. Mitchell"],["ssf","0.11.2","Apache-2.0","git://github.com/SheetJS/ssf.git","http://sheetjs.com/","sheetjs"],["swagger-ui-dist","3.32.5","Apache-2.0","**************:swagger-api/swagger-ui.git","Unknown","Unknown"],["true-case-path","1.0.3","Apache-2.0","git+https://github.com/barsh/true-case-path.git","https://github.com/barsh/true-case-path#readme","barsh"],["tslib","1.9.0","Apache-2.0","https://github.com/Microsoft/tslib.git","http://typescriptlang.org/","Microsoft Corp."],["tslib","1.10.0","Apache-2.0","https://github.com/Microsoft/tslib.git","http://typescriptlang.org/","Microsoft Corp."],["tslint","5.20.0","Apache-2.0","https://github.com/palantir/tslint.git","https://palantir.github.io/tslint","Unknown"],["tslint","6.0.0","Apache-2.0","https://github.com/palantir/tslint.git","https://palantir.github.io/tslint","Unknown"],["tslint","5.19.0","Apache-2.0","https://github.com/palantir/tslint.git","https://palantir.github.io/tslint","Unknown"],["tslint","5.20.1","Apache-2.0","https://github.com/palantir/tslint.git","https://palantir.github.io/tslint","Unknown"],["tunnel-agent","0.6.0","Apache-2.0","https://github.com/mikeal/tunnel-agent","http://www.futurealoof.com","Mikeal Rogers"],["typescript","3.5.3","Apache-2.0","https://github.com/Microsoft/TypeScript.git","https://www.typescriptlang.org/","Microsoft Corp."],["typescript","3.6.2","Apache-2.0","https://github.com/Microsoft/TypeScript.git","https://www.typescriptlang.org/","Microsoft Corp."],["typescript","3.9.7","Apache-2.0","https://github.com/Microsoft/TypeScript.git","https://www.typescriptlang.org/","Microsoft Corp."],["typescript","4.0.2","Apache-2.0","https://github.com/Microsoft/TypeScript.git","https://www.typescriptlang.org/","Microsoft Corp."],["validate-npm-package-license","3.0.4","Apache-2.0","https://github.com/kemitchell/validate-npm-package-license.js.git","https://kemitchell.com","Kyle E. Mitchell"],["watch","1.0.2","Apache-2.0","git://github.com/mikeal/watch.git","https://github.com/mikeal/watch","Mikeal Rogers"],["websocket-driver","0.7.4","Apache-2.0","git://github.com/faye/websocket-driver-node.git","https://github.com/faye/websocket-driver-node","James Coglan"],["websocket-extensions","0.1.4","Apache-2.0","git://github.com/faye/websocket-extensions-node.git","http://github.com/faye/websocket-extensions-node","James Coglan"],["wmf","1.0.2","Apache-2.0","git://github.com/SheetJS/js-wmf.git","https://sheetjs.com/","sheetjs"],["word","0.3.0","Apache-2.0","git://github.com/SheetJS/js-word.git","https://wordjs.com/","sheetjs"],["worker-plugin","3.2.0","Apache-2.0","https://github.com/GoogleChromeLabs/worker-plugin.git","Unknown","The Chromium Authors"],["xlsx","0.16.6","Apache-2.0","git://github.com/SheetJS/sheetjs.git","https://sheetjs.com/","sheetjs"],["@hapi/address","2.1.4","BSD-3-Clause","git://github.com/hapijs/address","Unknown","Unknown"],["@hapi/formula","1.2.0","BSD-3-Clause","git://github.com/hapijs/formula","Unknown","Unknown"],["@hapi/hoek","8.5.1","BSD-3-Clause","git://github.com/hapijs/hoek","Unknown","Unknown"],["@hapi/joi","16.1.8","BSD-3-Clause","git://github.com/hapijs/joi","Unknown","Unknown"],["@hapi/pinpoint","1.0.2","BSD-3-Clause","git://github.com/hapijs/pinpoint","Unknown","Unknown"],["@hapi/topo","3.1.6","BSD-3-Clause","git://github.com/hapijs/topo","Unknown","Unknown"],["@webcomponents/webcomponentsjs","2.4.4","BSD-3-Clause","https://github.com/webcomponents/polyfills.git","https://github.com/webcomponents/polyfills/tree/master/packages/webcomponentsjs","The Polymer Project Authors"],["@xtuc/ieee754","1.2.0","BSD-3-Clause","git://github.com/feross/ieee754.git","http://feross.org","Feross Aboukhadijeh"],["bcrypt-pbkdf","1.0.2","BSD-3-Clause","git://github.com/joyent/node-bcrypt-pbkdf.git","Unknown","Unknown"],["buffer-equal-constant-time","1.0.1","BSD-3-Clause","**************:goinstant/buffer-equal-constant-time.git","Unknown","GoInstant Inc., a salesforce.com company"],["charenc","0.0.2","BSD-3-Clause","git://github.com/pvorb/node-charenc.git","http://vorb.de","Paul Vorbach"],["crypt","0.0.2","BSD-3-Clause","git://github.com/pvorb/node-crypt.git","http://vorb.de","Paul Vorbach"],["diff","4.0.2","BSD-3-Clause","git://github.com/kpdecker/jsdiff.git","Unknown","Unknown"],["diff","3.5.0","BSD-3-Clause","git://github.com/kpdecker/jsdiff.git","Unknown","Unknown"],["duplexer3","0.1.4","BSD-3-Clause","https://github.com/floatdrop/duplexer3.git","http://www.fknsrs.biz/","Conrad Pankoff"],["esquery","1.3.1","BSD-3-Clause","https://github.com/estools/esquery.git","https://github.com/estools/esquery/","Joel Feenstra"],["express-handlebars","3.1.0","BSD-3-Clause","git://github.com/ericf/express-handlebars.git","https://github.com/ericf/express-handlebars","Eric Ferraiuolo"],["filesize","3.6.1","BSD-3-Clause","git://github.com/avoidwork/filesize.js.git","https://filesizejs.com/","Jason Mulligan"],["flat","4.1.0","BSD-3-Clause","git://github.com/hughsk/flat.git","https://github.com/hughsk/flat","Hugh Kennedy"],["ieee754","1.1.13","BSD-3-Clause","git://github.com/feross/ieee754.git","http://feross.org","Feross Aboukhadijeh"],["istanbul-api","2.1.7","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-coverage","3.0.0","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-coverage","2.0.5","BSD-3-Clause","**************:istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-coverage","1.2.1","BSD-3-Clause","**************:istanbuljs/istanbuljs.git","https://github.com/istanbuljs/istanbuljs","Krishnan Anantheswaran"],["istanbul-lib-hook","3.0.0","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-hook","2.0.7","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-instrument","4.0.3","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-instrument","1.10.2","BSD-3-Clause","**************:istanbuljs/istanbuljs.git","https://github.com/istanbuljs/istanbuljs","Krishnan Anantheswaran"],["istanbul-lib-instrument","3.3.0","BSD-3-Clause","**************:istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-report","3.0.0","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-report","2.0.8","BSD-3-Clause","**************:istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-source-maps","4.0.0","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-lib-source-maps","3.0.6","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-reports","3.0.2","BSD-3-Clause","git+ssh://**************/istanbuljs/istanbuljs.git","https://istanbul.js.org/","Krishnan Anantheswaran"],["istanbul-reports","2.2.7","BSD-3-Clause","**************:istanbuljs/istanbuljs","https://istanbul.js.org/","Krishnan Anantheswaran"],["js-base64","2.6.4","BSD-3-Clause","git://github.com/dankogai/js-base64.git","Unknown","Dan Kogai"],["md5","2.3.0","BSD-3-Clause","git://github.com/pvorb/node-md5.git","http://paul.vorba.ch","Paul Vorbach"],["node-pre-gyp","0.11.0","BSD-3-Clause","git://github.com/mapbox/node-pre-gyp.git","Unknown","Dane Springmeyer"],["parchment","1.1.4","BSD-3-Clause","https://github.com/quilljs/parchment","http://quilljs.com/docs/parchment","Jason Chen"],["qs","6.5.2","BSD-3-Clause","https://github.com/ljharb/qs.git","https://github.com/ljharb/qs","Unknown"],["qs","6.9.4","BSD-3-Clause","https://github.com/ljharb/qs.git","https://github.com/ljharb/qs","Unknown"],["qs","6.7.0","BSD-3-Clause","https://github.com/ljharb/qs.git","https://github.com/ljharb/qs","Unknown"],["quill","1.3.7","BSD-3-Clause","https://github.com/quilljs/quill","http://quilljs.com/","Jason Chen"],["serialize-javascript","4.0.0","BSD-3-Clause","git+https://github.com/yahoo/serialize-javascript.git","https://github.com/yahoo/serialize-javascript","Eric Ferraiuolo"],["serialize-javascript","1.9.1","BSD-3-Clause","git+https://github.com/yahoo/serialize-javascript.git","https://github.com/yahoo/serialize-javascript","Eric Ferraiuolo"],["source-map","0.6.1","BSD-3-Clause","http://github.com/mozilla/source-map.git","https://github.com/mozilla/source-map","Nick Fitzgerald"],["source-map","0.7.3","BSD-3-Clause","http://github.com/mozilla/source-map.git","https://github.com/mozilla/source-map","Nick Fitzgerald"],["source-map","0.5.7","BSD-3-Clause","http://github.com/mozilla/source-map.git","https://github.com/mozilla/source-map","Nick Fitzgerald"],["source-map","0.5.6","BSD-3-Clause","http://github.com/mozilla/source-map.git","https://github.com/mozilla/source-map","Nick Fitzgerald"],["source-map","0.1.43","BSD-3-Clause","http://github.com/mozilla/source-map.git","https://github.com/mozilla/source-map","Nick Fitzgerald"],["source-map","0.4.4","BSD-3-Clause","http://github.com/mozilla/source-map.git","https://github.com/mozilla/source-map","Nick Fitzgerald"],["sprintf-js","1.1.2","BSD-3-Clause","https://github.com/alexei/sprintf.js.git","Unknown","Alexandru Mărășteanu"],["sprintf-js","1.0.3","BSD-3-Clause","https://github.com/alexei/sprintf.js.git","http://alexei.ro/","Alexandru Marasteanu"],["tough-cookie","2.4.3","BSD-3-Clause","git://github.com/salesforce/tough-cookie.git","https://github.com/salesforce/tough-cookie","Jeremy Stashewsky"],["tough-cookie","2.5.0","BSD-3-Clause","git://github.com/salesforce/tough-cookie.git","https://github.com/salesforce/tough-cookie","Jeremy Stashewsky"],["@istanbuljs/load-nyc-config","1.1.0","ISC","git+https://github.com/istanbuljs/load-nyc-config.git","https://github.com/istanbuljs/load-nyc-config#readme","Unknown"],["@webassemblyjs/helper-fsm","1.8.5","ISC","Unknown","Unknown","Mauro Bringolf"],["abbrev","1.1.1","ISC","http://github.com/isaacs/abbrev-js","Unknown","Isaac Z. Schlueter"],["ansi-align","3.0.0","ISC","git+https://github.com/nexdrew/ansi-align.git","https://github.com/nexdrew/ansi-align#readme","nexdrew"],["ansi-align","2.0.0","ISC","git+https://github.com/nexdrew/ansi-align.git","https://github.com/nexdrew/ansi-align#readme","nexdrew"],["anymatch","3.1.1","ISC","https://github.com/micromatch/anymatch","https://github.com/micromatch/anymatch","Elan Shanker"],["anymatch","2.0.0","ISC","https://github.com/micromatch/anymatch","https://github.com/micromatch/anymatch","Elan Shanker"],["aproba","1.2.0","ISC","https://github.com/iarna/aproba","https://github.com/iarna/aproba","Rebecca Turner"],["are-we-there-yet","1.1.5","ISC","https://github.com/iarna/are-we-there-yet.git","https://github.com/iarna/are-we-there-yet","Rebecca Turner"],["ast-types-flow","0.0.7","ISC","git+https://github.com/kyldvs/ast-types-flow.git","https://github.com/kyldvs/ast-types-flow#readme","kyldvs"],["block-stream","0.0.9","ISC","git://github.com/isaacs/block-stream.git","http://blog.izs.me/","Isaac Z. Schlueter"],["browser-stdout","1.3.1","ISC","http://github.com/kumavis/browser-stdout.git","Unknown","kumavis"],["browserify-sign","4.2.1","ISC","https://github.com/crypto-browserify/browserify-sign.git","Unknown","Unknown"],["cacache","12.0.4","ISC","https://github.com/npm/cacache","Unknown","Kat Marchán"],["cacache","12.0.2","ISC","https://github.com/npm/cacache","Unknown","Kat Marchán"],["cacache","11.3.3","ISC","https://github.com/zkat/cacache","Unknown","Kat Marchán"],["chownr","1.1.4","ISC","git://github.com/isaacs/chownr.git","http://blog.izs.me/","Isaac Z. Schlueter"],["circular-dependency-plugin","5.2.0","ISC","git+https://github.com/aackerman/circular-dependency-plugin.git","https://github.com/aackerman/circular-dependency-plugin#readme","Unknown"],["cli-color","2.0.0","ISC","git://github.com/medikoo/cli-color.git","http://www.medikoo.com/","Mariusz Nowak"],["cli-width","2.2.1","ISC","**************:knownasilya/cli-width.git","https://github.com/knownasilya/cli-width","Ilya Radchenko"],["cliui","3.2.0","ISC","http://github.com/yargs/cliui.git","Unknown","Ben Coe"],["cliui","6.0.0","ISC","http://github.com/yargs/cliui.git","Unknown","Ben Coe"],["cliui","4.1.0","ISC","http://github.com/yargs/cliui.git","Unknown","Ben Coe"],["cliui","5.0.0","ISC","http://github.com/yargs/cliui.git","Unknown","Ben Coe"],["console-control-strings","1.1.0","ISC","https://github.com/iarna/console-control-strings","http://re-becca.org/","Rebecca Turner"],["copy-concurrently","1.0.5","ISC","git+https://github.com/npm/copy-concurrently.git","https://www.npmjs.com/package/copy-concurrently","Rebecca Turner"],["d","1.0.1","ISC","git://github.com/medikoo/d.git","http://www.medikoo.com/","Mariusz Nowak"],["detect-node","2.0.4","ISC","https://github.com/iliakan/detect-node","https://github.com/iliakan/detect-node","Ilya Kantor"],["dezalgo","1.0.3","ISC","https://github.com/npm/dezalgo","https://github.com/npm/dezalgo","Isaac Z. Schlueter"],["electron-to-chromium","1.3.564","ISC","https://github.com/kilian/electron-to-chromium/","Unknown","Kilian Valkhof"],["es5-ext","0.10.53","ISC","https://github.com/medikoo/es5-ext.git","http://www.medikoo.com/","Mariusz Nowak"],["es6-symbol","3.1.3","ISC","git://github.com/medikoo/es6-symbol.git","http://www.medikoo.com/","Mariusz Nowak"],["es6-weak-map","2.0.3","ISC","git://github.com/medikoo/es6-weak-map.git","http://www.medikoo.com/","Mariusz Nowak"],["ext","1.4.0","ISC","https://github.com/medikoo/es5-ext/tree/ext","http://www.medikoo.com/","Mariusz Nowak"],["fastq","1.8.0","ISC","git+https://github.com/mcollina/fastq.git","https://github.com/mcollina/fastq#readme","Matteo Collina"],["figgy-pudding","3.5.2","ISC","https://github.com/npm/figgy-pudding","Unknown","Kat Marchán"],["flatted","2.0.2","ISC","git+https://github.com/WebReflection/flatted.git","https://github.com/WebReflection/flatted#readme","Andrea Giammarchi"],["foreground-child","2.0.0","ISC","git+https://github.com/tapjs/foreground-child.git","https://github.com/tapjs/foreground-child#readme","Isaac Z. Schlueter"],["fs-minipass","1.2.7","ISC","git+https://github.com/npm/fs-minipass.git","https://github.com/npm/fs-minipass#readme","Isaac Z. Schlueter"],["fs-write-stream-atomic","1.0.10","ISC","https://github.com/npm/fs-write-stream-atomic","https://github.com/npm/fs-write-stream-atomic","Isaac Z. Schlueter"],["fs.realpath","1.0.0","ISC","git+https://github.com/isaacs/fs.realpath.git","http://blog.izs.me/","Isaac Z. Schlueter"],["fstream","1.0.12","ISC","https://github.com/npm/fstream.git","http://blog.izs.me/","Isaac Z. Schlueter"],["fuzzy-search","3.0.1","ISC","git+https://github.com/wouter2203/fuzzy-search.git","https://github.com/wouter2203/fuzzy-search#readme","Wouter Rutgers"],["gauge","2.7.4","ISC","https://github.com/iarna/gauge","https://github.com/iarna/gauge","Rebecca Turner"],["get-caller-file","1.0.3","ISC","git+https://github.com/stefanpenner/get-caller-file.git","https://github.com/stefanpenner/get-caller-file#readme","Stefan Penner"],["get-caller-file","2.0.5","ISC","git+https://github.com/stefanpenner/get-caller-file.git","https://github.com/stefanpenner/get-caller-file#readme","Stefan Penner"],["glob","7.1.6","ISC","git://github.com/isaacs/node-glob.git","http://blog.izs.me/","Isaac Z. Schlueter"],["glob","7.1.4","ISC","git://github.com/isaacs/node-glob.git","http://blog.izs.me/","Isaac Z. Schlueter"],["glob","7.1.3","ISC","git://github.com/isaacs/node-glob.git","http://blog.izs.me/","Isaac Z. Schlueter"],["glob","7.0.6","ISC","git://github.com/isaacs/node-glob.git","http://blog.izs.me/","Isaac Z. Schlueter"],["glob-parent","5.1.1","ISC","https://github.com/gulpjs/glob-parent.git","https://gulpjs.com/","Gulp Team"],["glob-parent","3.1.0","ISC","https://github.com/es128/glob-parent","https://github.com/es128/glob-parent","Elan Shanker"],["graceful-fs","4.2.4","ISC","https://github.com/isaacs/node-graceful-fs","Unknown","Unknown"],["guid-typescript","1.0.9","ISC","https://github.com/NicolasDeveloper/guid-typescript","Unknown","nicolas"],["har-schema","2.0.0","ISC","https://github.com/ahmadnassri/har-schema.git","https://github.com/ahmadnassri/har-schema","Ahmad Nassri"],["has-unicode","2.0.1","ISC","https://github.com/iarna/has-unicode","https://github.com/iarna/has-unicode","Rebecca Turner"],["hosted-git-info","2.8.8","ISC","git+https://github.com/npm/hosted-git-info.git","https://github.com/npm/hosted-git-info","Rebecca Turner"],["ignore-by-default","1.0.1","ISC","git+https://github.com/novemberborn/ignore-by-default.git","https://github.com/novemberborn/ignore-by-default#readme","Mark Wubben"],["ignore-walk","3.0.3","ISC","git+https://github.com/isaacs/ignore-walk.git","http://blog.izs.me/","Isaac Z. Schlueter"],["in-publish","2.0.1","ISC","https://github.com/iarna/in-publish","https://github.com/iarna/in-publish","Rebecca Turner"],["infer-owner","1.0.4","ISC","https://github.com/npm/infer-owner","https://izs.me","Isaac Z. Schlueter"],["inflight","1.0.6","ISC","https://github.com/npm/inflight.git","https://github.com/isaacs/inflight","Isaac Z. Schlueter"],["inherits","2.0.4","ISC","git://github.com/isaacs/inherits","Unknown","Unknown"],["inherits","2.0.3","ISC","git://github.com/isaacs/inherits","Unknown","Unknown"],["inherits","2.0.1","ISC","git://github.com/isaacs/inherits","Unknown","Unknown"],["ini","1.3.5","ISC","git://github.com/isaacs/ini.git","http://blog.izs.me/","Isaac Z. Schlueter"],["isexe","2.0.0","ISC","git+https://github.com/isaacs/isexe.git","https://github.com/isaacs/isexe#readme","Isaac Z. Schlueter"],["istanbul-lib-processinfo","2.0.2","ISC","git+https://github.com/istanbuljs/istanbul-lib-processinfo.git","Unknown","Unknown"],["iterare","1.2.0","ISC","https://github.com/felixfbecker/iterare","Unknown","Felix Becker"],["json-stringify-safe","5.0.1","ISC","git://github.com/isaacs/json-stringify-safe","https://github.com/isaacs/json-stringify-safe","Isaac Z. Schlueter"],["killable","1.0.1","ISC","https://github.com/marten-de-vries/killable.git","Unknown","Marten de Vries"],["license-webpack-plugin","2.1.2","ISC","git+ssh://**************/xz64/license-webpack-plugin.git","https://github.com/xz64/license-webpack-plugin#readme","S K"],["lockfile","1.0.4","ISC","https://github.com/npm/lockfile.git","http://blog.izs.me/","Isaac Z. Schlueter"],["lru-cache","5.1.1","ISC","git://github.com/isaacs/node-lru-cache.git","Unknown","Isaac Z. Schlueter"],["lru-cache","4.1.5","ISC","git://github.com/isaacs/node-lru-cache.git","Unknown","Isaac Z. Schlueter"],["make-error","1.3.6","ISC","git://github.com/JsCommunity/make-error.git","https://github.com/JsCommunity/make-error","Julien Fontanet"],["make-fetch-happen","5.0.2","ISC","https://github.com/zkat/make-fetch-happen","Unknown","Kat Marchán"],["make-plural","4.3.0","ISC","https://github.com/eemeli/make-plural.git","https://github.com/eemeli/make-plural#readme","Eemeli Aro"],["memoizee","0.4.14","ISC","git://github.com/medikoo/memoizee.git","http://www.medikoo.com/","Mariusz Nowak"],["minimalistic-assert","1.0.1","ISC","https://github.com/calvinmetcalf/minimalistic-assert.git","https://github.com/calvinmetcalf/minimalistic-assert","Unknown"],["minimatch","3.0.4","ISC","git://github.com/isaacs/minimatch.git","http://blog.izs.me","Isaac Z. Schlueter"],["minimatch","3.0.3","ISC","git://github.com/isaacs/minimatch.git","http://blog.izs.me","Isaac Z. Schlueter"],["minipass","2.9.0","ISC","git+https://github.com/isaacs/minipass.git","http://blog.izs.me/","Isaac Z. Schlueter"],["move-concurrently","1.0.1","ISC","git+https://github.com/npm/move-concurrently.git","https://www.npmjs.com/package/move-concurrently","Rebecca Turner"],["mute-stream","0.0.8","ISC","git://github.com/isaacs/mute-stream","http://blog.izs.me/","Isaac Z. Schlueter"],["mute-stream","0.0.5","ISC","git://github.com/isaacs/mute-stream","http://blog.izs.me/","Isaac Z. Schlueter"],["nestjs","0.0.1","ISC","Unknown","Unknown","Quang Van"],["next-tick","1.1.0","ISC","git://github.com/medikoo/next-tick.git","http://www.medikoo.com/","Mariusz Nowak"],["nopt","3.0.6","ISC","https://github.com/npm/nopt.git","http://blog.izs.me/","Isaac Z. Schlueter"],["nopt","4.0.3","ISC","https://github.com/npm/nopt.git","http://blog.izs.me/","Isaac Z. Schlueter"],["npm-bundled","1.1.1","ISC","git+https://github.com/npm/npm-bundled.git","http://blog.izs.me/","Isaac Z. Schlueter"],["npm-normalize-package-bin","1.0.1","ISC","git+https://github.com/npm/npm-normalize-package-bin","https://izs.me","Isaac Z. Schlueter"],["npm-package-arg","6.1.0","ISC","https://github.com/npm/npm-package-arg","https://github.com/npm/npm-package-arg","Isaac Z. Schlueter"],["npm-package-arg","6.1.1","ISC","https://github.com/npm/npm-package-arg","https://github.com/npm/npm-package-arg","Isaac Z. Schlueter"],["npm-packlist","1.4.8","ISC","git+https://github.com/npm/npm-packlist.git","https://www.npmjs.com/package/npm-packlist","Isaac Z. Schlueter"],["npm-pick-manifest","3.0.2","ISC","https://github.com/npm/npm-pick-manifest","Unknown","Kat Marchán"],["npm-pick-manifest","2.2.3","ISC","https://github.com/zkat/npm-pick-manifest","Unknown","Kat Marchán"],["npm-registry-fetch","4.0.7","ISC","https://github.com/npm/registry-fetch","Unknown","Kat Marchán"],["npmlog","4.1.2","ISC","https://github.com/npm/npmlog.git","http://blog.izs.me/","Isaac Z. Schlueter"],["nyc","15.0.1","ISC","**************:istanbuljs/nyc.git","https://istanbul.js.org/","Ben Coe"],["once","1.4.0","ISC","git://github.com/isaacs/once","http://blog.izs.me/","Isaac Z. Schlueter"],["osenv","0.1.5","ISC","https://github.com/npm/osenv","http://blog.izs.me/","Isaac Z. Schlueter"],["package-hash","4.0.0","ISC","git+https://github.com/novemberborn/package-hash.git","https://github.com/novemberborn/package-hash#readme","Mark Wubben"],["package-summary","1.1.0","ISC","Unknown","Unknown","Unknown"],["parse-asn1","5.1.6","ISC","git://github.com/crypto-browserify/parse-asn1.git","Unknown","Unknown"],["promise-inflight","1.0.1","ISC","git+https://github.com/iarna/promise-inflight.git","https://github.com/iarna/promise-inflight#readme","Rebecca Turner"],["pseudomap","1.0.2","ISC","git+https://github.com/isaacs/pseudomap.git","https://github.com/isaacs/pseudomap#readme","Isaac Z. Schlueter"],["read-package-json","2.1.2","ISC","https://github.com/npm/read-package-json.git","http://blog.izs.me/","Isaac Z. Schlueter"],["read-package-tree","5.3.1","ISC","https://github.com/npm/read-package-tree","https://github.com/npm/read-package-tree","Isaac Z. Schlueter"],["readdir-scoped-modules","1.1.0","ISC","https://github.com/npm/readdir-scoped-modules","https://github.com/npm/readdir-scoped-modules","Isaac Z. Schlueter"],["release-zalgo","1.0.0","ISC","git+https://github.com/novemberborn/release-zalgo.git","https://github.com/novemberborn/release-zalgo#readme","Mark Wubben"],["remove-trailing-separator","1.1.0","ISC","git+https://github.com/darsain/remove-trailing-separator.git","https://github.com/darsain/remove-trailing-separator#readme","darsain"],["request-promise-core","1.1.2","ISC","git+https://github.com/request/promise-core.git","https://github.com/request/promise-core#readme","Nicolai Kamenzky"],["request-promise-core","1.1.4","ISC","git+https://github.com/request/promise-core.git","https://github.com/request/promise-core#readme","Nicolai Kamenzky"],["request-promise-native","1.0.7","ISC","git+https://github.com/request/request-promise-native.git","https://github.com/request/request-promise-native#readme","Nicolai Kamenzky"],["request-promise-native","1.0.9","ISC","git+https://github.com/request/request-promise-native.git","https://github.com/request/request-promise-native#readme","Nicolai Kamenzky"],["require-main-filename","1.0.1","ISC","git+ssh://**************/yargs/require-main-filename.git","https://github.com/yargs/require-main-filename#readme","Ben Coe"],["require-main-filename","2.0.0","ISC","git+ssh://**************/yargs/require-main-filename.git","https://github.com/yargs/require-main-filename#readme","Ben Coe"],["rete-area-plugin","0.2.1","ISC","Unknown","Unknown","Unknown"],["rimraf","2.7.1","ISC","git://github.com/isaacs/rimraf.git","http://blog.izs.me/","Isaac Z. Schlueter"],["rimraf","3.0.0","ISC","git://github.com/isaacs/rimraf.git","http://blog.izs.me/","Isaac Z. Schlueter"],["rimraf","3.0.2","ISC","git://github.com/isaacs/rimraf.git","http://blog.izs.me/","Isaac Z. Schlueter"],["run-queue","1.0.3","ISC","git+https://github.com/iarna/run-queue.git","https://npmjs.com/package/run-queue","Rebecca Turner"],["sax","1.2.4","ISC","git://github.com/isaacs/sax-js.git","http://blog.izs.me/","Isaac Z. Schlueter"],["semver","5.7.1","ISC","https://github.com/npm/node-semver","Unknown","Unknown"],["semver","6.3.0","ISC","https://github.com/npm/node-semver","Unknown","Unknown"],["semver","5.3.0","ISC","https://github.com/npm/node-semver","Unknown","Unknown"],["semver","7.0.0","ISC","https://github.com/npm/node-semver","Unknown","Unknown"],["set-blocking","2.0.0","ISC","git+https://github.com/yargs/set-blocking.git","https://github.com/yargs/set-blocking#readme","Ben Coe"],["setprototypeof","1.1.1","ISC","https://github.com/wesleytodd/setprototypeof.git","https://github.com/wesleytodd/setprototypeof","Wes Todd"],["setprototypeof","1.2.0","ISC","https://github.com/wesleytodd/setprototypeof.git","https://github.com/wesleytodd/setprototypeof","Wes Todd"],["setprototypeof","1.1.0","ISC","https://github.com/wesleytodd/setprototypeof.git","https://github.com/wesleytodd/setprototypeof","Wes Todd"],["signal-exit","3.0.3","ISC","https://github.com/tapjs/signal-exit.git","https://github.com/tapjs/signal-exit","Ben Coe"],["spawn-wrap","2.0.0","ISC","git+https://github.com/istanbuljs/spawn-wrap.git","https://github.com/istanbuljs/spawn-wrap#readme","Isaac Z. Schlueter"],["split2","3.2.2","ISC","https://github.com/mcollina/split2.git","Unknown","Matteo Collina"],["ssri","6.0.1","ISC","https://github.com/zkat/ssri","Unknown","Kat Marchán"],["stealthy-require","1.1.1","ISC","git+https://github.com/analog-nico/stealthy-require.git","https://github.com/analog-nico/stealthy-require#readme","Nicolai Kamenzky"],["tar","4.4.13","ISC","https://github.com/npm/node-tar.git","http://blog.izs.me/","Isaac Z. Schlueter"],["tar","2.2.2","ISC","git://github.com/isaacs/node-tar.git","http://blog.izs.me/","Isaac Z. Schlueter"],["test-exclude","6.0.0","ISC","git+https://github.com/istanbuljs/test-exclude.git","https://istanbul.js.org/","Ben Coe"],["timers-ext","0.1.7","ISC","git://github.com/medikoo/timers-ext.git","http://www.medikoo.com/","Mariusz Nowak"],["touch","3.1.0","ISC","git://github.com/isaacs/node-touch.git","http://blog.izs.me/","Isaac Z. Schlueter"],["type","1.2.0","ISC","https://github.com/medikoo/type.git","https://www.medikoo.com/","Mariusz Nowak"],["type","2.1.0","ISC","https://github.com/medikoo/type.git","https://www.medikoo.com/","Mariusz Nowak"],["unique-filename","1.1.1","ISC","https://github.com/iarna/unique-filename.git","https://github.com/iarna/unique-filename","Rebecca Turner"],["unique-slug","2.0.2","ISC","git://github.com/iarna/unique-slug.git","http://re-becca.org","Rebecca Turner"],["validate-npm-package-name","3.0.0","ISC","https://github.com/npm/validate-npm-package-name","https://github.com/npm/validate-npm-package-name","zeke"],["wallaby-webpack","3.9.15","ISC","git+https://github.com/jeffling/wallaby-webpack.git","Unknown","Jeff Ling"],["which","1.3.1","ISC","git://github.com/isaacs/node-which.git","http://blog.izs.me","Isaac Z. Schlueter"],["which","2.0.2","ISC","git://github.com/isaacs/node-which.git","http://blog.izs.me","Isaac Z. Schlueter"],["which-module","1.0.0","ISC","git+https://github.com/nexdrew/which-module.git","https://github.com/nexdrew/which-module#readme","nexdrew"],["which-module","2.0.0","ISC","git+https://github.com/nexdrew/which-module.git","https://github.com/nexdrew/which-module#readme","nexdrew"],["wide-align","1.1.3","ISC","https://github.com/iarna/wide-align","http://re-becca.org/","Rebecca Turner"],["wrappy","1.0.2","ISC","https://github.com/npm/wrappy","https://github.com/npm/wrappy","Isaac Z. Schlueter"],["write-file-atomic","3.0.3","ISC","git://github.com/npm/write-file-atomic.git","https://github.com/npm/write-file-atomic","Rebecca Turner"],["write-file-atomic","2.4.3","ISC","**************:iarna/write-file-atomic.git","https://github.com/iarna/write-file-atomic","Rebecca Turner"],["y18n","4.0.0","ISC","**************:yargs/y18n.git","https://github.com/yargs/y18n","Ben Coe"],["y18n","3.2.1","ISC","**************:yargs/y18n.git","https://github.com/yargs/y18n","Ben Coe"],["yallist","3.1.1","ISC","git+https://github.com/isaacs/yallist.git","http://blog.izs.me/","Isaac Z. Schlueter"],["yallist","2.1.2","ISC","git+https://github.com/isaacs/yallist.git","http://blog.izs.me/","Isaac Z. Schlueter"],["yargs-parser","18.1.3","ISC","https://github.com/yargs/yargs-parser.git","Unknown","Ben Coe"],["@labdiscover/allocation","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/auth","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/base","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/comment","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/common","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/common-file-generator","1.0.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/db-migrate","1.1.0","UNLICENSED","Unknown","Unknown","Team LabOperations"],["@labdiscover/document","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/frontend","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/license-generator","1.0.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/logger","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/nestjs-common","1.1.0","UNLICENSED","https://bitbucket.it.keysight.com/scm/ld/labdiscover.git","Unknown","Unknown"],["@labdiscover/nestjs-mongoose-common","1.1.0","UNLICENSED","https://bitbucket.it.keysight.com/scm/ld/labdiscover.git","Unknown","Unknown"],["@labdiscover/nestjs-mongoose-test-common","1.1.0","UNLICENSED","https://bitbucket.it.keysight.com/scm/ld/labdiscover.git","Unknown","Unknown"],["@labdiscover/nestjs-test-common","1.1.0","UNLICENSED","https://bitbucket.it.keysight.com/scm/ld/labdiscover.git","Unknown","Unknown"],["@labdiscover/notification","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/reservation","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/storage","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/suiteexecution","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/testbench","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/testobject","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["@labdiscover/testrun","1.1.0","UNLICENSED","Unknown","Unknown","Unknown"],["deployment","0.3.0","UNLICENSED","Unknown","Unknown","Unknown"],["workspace-aggregator-bcfb0c69-c660-426f-b992-cba2d62f6003","1.1.0","UNLICENSED","ssh://*****************************:7999/ld/labdiscover.git","Unknown","Team Labdiscover"],["@yarnpkg/lockfile","1.1.0","BSD-2-Clause","https://github.com/yarnpkg/yarn/blob/master/packages/lockfile","Unknown","Unknown"],["configstore","3.1.5","BSD-2-Clause","https://github.com/yeoman/configstore.git","sindresorhus.com","Sindre Sorhus"],["damerau-levenshtein","1.0.6","BSD-2-Clause","https://github.com/tad-lispy/node-damerau-levenshtein.git","Unknown","The Spanish Inquisition"],["default-gateway","4.2.0","BSD-2-Clause","https://github.com/silverwind/default-gateway.git","Unknown","silverwind"],["domino","2.1.6","BSD-2-Clause","https://github.com/fgnass/domino.git","https://github.com/fgnass/domino","Felix Gnass"],["dotenv","8.2.0","BSD-2-Clause","git://github.com/motdotla/dotenv.git","Unknown","Unknown"],["escodegen","1.14.3","BSD-2-Clause","http://github.com/estools/escodegen.git","http://github.com/estools/escodegen","Unknown"],["eslint-scope","4.0.3","BSD-2-Clause","https://github.com/eslint/eslint-scope.git","http://github.com/eslint/eslint-scope","Unknown"],["esprima","4.0.1","BSD-2-Clause","https://github.com/jquery/esprima.git","http://esprima.org/","Ariya Hidayat"],["esrecurse","4.3.0","BSD-2-Clause","https://github.com/estools/esrecurse.git","https://github.com/estools/esrecurse","Unknown"],["estraverse","4.3.0","BSD-2-Clause","http://github.com/estools/estraverse.git","https://github.com/estools/estraverse","Unknown"],["estraverse","5.2.0","BSD-2-Clause","http://github.com/estools/estraverse.git","https://github.com/estools/estraverse","Unknown"],["esutils","2.0.3","BSD-2-Clause","http://github.com/estools/esutils.git","https://github.com/estools/esutils","Unknown"],["extract-zip","1.7.0","BSD-2-Clause","https://github.com/maxogden/extract-zip.git","Unknown","max ogden"],["http-cache-semantics","3.8.1","BSD-2-Clause","https://github.com/pornel/http-cache-semantics.git","https://kornel.ski/","Kornel Lesiński"],["mississippi","3.0.0","BSD-2-Clause","git+https://github.com/maxogden/mississippi.git","https://github.com/maxogden/mississippi#readme","max ogden"],["normalize-package-data","2.5.0","BSD-2-Clause","git://github.com/npm/normalize-package-data.git","Unknown","Meryn Stol"],["regjsparser","0.6.4","BSD-2-Clause","**************:jviereck/regjsparser.git","https://github.com/jviereck/regjsparser","'Julian Viereck'"],["terser","4.8.0","BSD-2-Clause","https://github.com/terser/terser","https://terser.org/","Mihai Bazon"],["terser","4.1.4","BSD-2-Clause","https://github.com/fabiosantoscode/terser.git","https://github.com/fabiosantoscode/terser","Mihai Bazon"],["uglify-js","3.10.4","BSD-2-Clause","https://github.com/mishoo/UglifyJS.git","http://lisperator.net/","Mihai Bazon"],["update-notifier","2.5.0","BSD-2-Clause","https://github.com/yeoman/update-notifier.git","https://sindresorhus.com","Sindre Sorhus"],["uri-js","4.4.0","BSD-2-Clause","http://github.com/garycourt/uri-js","https://github.com/garycourt/uri-js","Gary Court"],["websocket-stream","5.5.2","BSD-2-Clause","git+ssh://**************/maxogden/websocket-stream.git","https://github.com/maxogden/websocket-stream#readme","Unknown"],["@zxing/ngx-scanner","2.0.1","UNKNOWN","Unknown","Unknown","Unknown"],["emitter-component","1.1.1","UNKNOWN","https://github.com/component/emitter.git","Unknown","Unknown"],["map-stream","0.1.0","UNKNOWN","git://github.com/dominictarr/map-stream.git","http://github.com/dominictarr/map-stream","Dominic Tarr"],["amdefine","1.0.1","BSD-3-Clause OR MIT","https://github.com/jrburke/amdefine.git","http://github.com/jrburke/amdefine","James Burke"],["async-foreach","0.1.3","MIT*","git://github.com/cowboy/javascript-sync-async-foreach.git","http://github.com/cowboy/javascript-sync-async-foreach","\"Cowboy\" Ben Alman"],["callsite","1.0.0","MIT*","Unknown","Unknown","TJ Holowaychuk"],["component-bind","1.0.0","MIT*","https://github.com/component/bind.git","Unknown","Unknown"],["component-inherit","0.0.3","MIT*","https://github.com/component/inherit.git","Unknown","Unknown"],["indexof","0.0.1","MIT*","Unknown","Unknown","Unknown"],["object-component","0.0.3","MIT*","Unknown","Unknown","Unknown"],["atob","2.1.2","(MIT OR Apache-2.0)","git://git.coolaj86.com/coolaj86/atob.js.git","https://git.coolaj86.com/coolaj86/atob.js.git","AJ ONeal"],["JSONStream","1.3.5","(MIT OR Apache-2.0)","git://github.com/dominictarr/JSONStream.git","http://github.com/dominictarr/JSONStream","Dominic Tarr"],["caniuse-lite","1.0.30000989","CC-BY-4.0","https://github.com/ben-eb/caniuse-lite.git","http://beneb.info","Ben Briggs"],["caniuse-lite","1.0.30001125","CC-BY-4.0","https://github.com/ben-eb/caniuse-lite.git","http://beneb.info","Ben Briggs"],["classlist.js","1.1.20150312","Dedicated to the public domain","git+https://github.com/eligrey/classList.js.git","https://github.com/eligrey/classList.js#readme","Unknown"],["expand-template","2.0.3","(MIT OR WTFPL)","https://github.com/ralphtheninja/expand-template.git","https://github.com/ralphtheninja/expand-template","LM"],["google-libphonenumber","3.2.12","(MIT AND Apache-2.0)","https://github.com/ruimarinho/google-libphonenumber.git","https://ruimarinho.github.io/google-libphonenumber/","Rui Marinho"],["google-libphonenumber","3.2.13","(MIT AND Apache-2.0)","https://github.com/ruimarinho/google-libphonenumber.git","https://ruimarinho.github.io/google-libphonenumber/","Rui Marinho"],["json-schema","0.2.3","BSD*","http://github.com/kriszyp/json-schema","Unknown","Kris Zyp"],["keycharm","0.3.1","(Apache-2.0 OR MIT)","https://github.com/AlexDM0/keycharm","Unknown","Alex de Mulder"],["vis-data","6.1.1","(Apache-2.0 OR MIT)","git://github.com/visjs/vis-data.git","http://visjs.org/","Unknown"],["vis-timeline","6.3.1","(Apache-2.0 OR MIT)","https://github.com/visjs/vis-timeline.git","https://github.com/visjs/vis-timeline","Unknown"],["vis-util","1.1.4","(Apache-2.0 OR MIT)","git+https://github.com/visjs/vis-utils.git","https://github.com/visjs/vis-util","Alex de Mulder"],["vis-util","1.1.10","(Apache-2.0 OR MIT)","https://github.com/visjs/vis-util.git","https://github.com/visjs/vis-util","Alex de Mulder"],["vis-uuid","1.1.4","(Apache-2.0 OR MIT)","https://github.com/visjs/vis-uuid.git","https://github.com/visjs/vis-uuid#README.md","Alex de Mulder"],["vis-uuid","1.1.3","(Apache-2.0 OR MIT)","git+https://github.com/visjs/vis-uuid.git","https://github.com/visjs/vis-uuid#README.md","Alex de Mulder"],["node-forge","0.9.0","(BSD-3-Clause OR GPL-2.0)","https://github.com/digitalbazaar/forge","https://github.com/digitalbazaar/forge","Digital Bazaar, Inc."],["opener","1.5.2","(WTFPL OR MIT)","https://github.com/domenic/opener.git","https://domenic.me/","Domenic Denicola"],["path-is-inside","1.0.2","(WTFPL OR MIT)","https://github.com/domenic/path-is-inside.git","https://domenic.me","Domenic Denicola"],["pako","1.0.11","(MIT AND Zlib)","https://github.com/nodeca/pako.git","https://github.com/nodeca/pako","Unknown"],["rc","1.2.8","(BSD-2-Clause OR MIT OR Apache-2.0)","https://github.com/dominictarr/rc.git","dominictarr.com","Dominic Tarr"],["sax","0.5.8","BSD","git://github.com/isaacs/sax-js.git","http://blog.izs.me/","Isaac Z. Schlueter"],["sha.js","2.4.11","(MIT AND BSD-3-Clause)","git://github.com/crypto-browserify/sha.js.git","https://github.com/crypto-browserify/sha.js","Dominic Tarr"],["spdx-exceptions","2.3.0","CC-BY-3.0","https://github.com/kemitchell/spdx-exceptions.json.git","Unknown","The Linux Foundation"],["spdx-license-ids","3.0.5","CC0-1.0","https://github.com/shinnn/spdx-license-ids.git","https://github.com/shinnn","Shinnosuke Watanabe"],["text-encoding","0.7.0","(Unlicense OR Apache-2.0)","https://github.com/inexorabletash/text-encoding.git","https://github.com/inexorabletash/text-encoding","Joshua Bell"],["tslib","1.13.0","0BSD","https://github.com/Microsoft/tslib.git","https://www.typescriptlang.org/","Microsoft Corp."],["tslib","2.0.1","0BSD","https://github.com/Microsoft/tslib.git","https://www.typescriptlang.org/","Microsoft Corp."],["tweetnacl","0.14.5","Unlicense","https://github.com/dchest/tweetnacl-js.git","https://tweetnacl.js.org/","TweetNaCl-js contributors"],["type-fest","0.8.1","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest.git","sindresorhus.com","Sindre Sorhus"],["type-fest","0.11.0","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest.git","sindresorhus.com","Sindre Sorhus"],["type-fest","0.3.1","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest.git","sindresorhus.com","Sindre Sorhus"],["xmldom","0.1.31","(LGPL-2.0 or MIT)","git://github.com/xmldom/xmldom.git","https://github.com/xmldom/xmldom","jindw"]]}}
