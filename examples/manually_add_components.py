import time
import json
import warnings
from blackduck.HubRestApi import HubInstance


class DuplicateComponentVersionSearchResultsError(Exception):
    pass


class ComponentNotFound(Exception):
    pass


class ComponentVersionNotFound(Exception):
    pass


def search_component_by_name(component_name, limit=10):
    data = hub.search_components(component_name, limit=limit)
    if data['totalCount'] == 0:
        raise ComponentNotFound(f'No search results for {component_name}')
    components = {}
    for hit in data['items'][0]['hits']:
        if hit['fields']['name'][0] == component_name:
            # components.append(hit['component'])
            components[hit['component']] = float(hit['fields']['score'][0])
    if len(components) > 1:
        # Get the high rank component if duplicates in the search results
        warnings.warn(f'The search results of component name {component_name} '
                      f'are not unique: {len(components)}')
        return max(components, key=components.get)
    elif len(components) == 0:
        raise ComponentNotFound(f'No search results for {component_name}')
    else:
        return list(components.keys())[0]


def search_component_version_by_names(component_name, component_version_name,
                                      limit=10):
    component_link = search_component_by_name(component_name, limit=limit)
    search_link = component_link + '/versions?limit={}&q=versionName:{}'.\
        format(limit, component_version_name)
    r = hub.execute_get(search_link)
    data = json.loads(r.text)
    if data['totalCount'] > 1:
        # fail if duplicates in the search results
        raise DuplicateComponentVersionSearchResultsError(
            f'The search results of component version name '
            f'{component_version_name} are not unique: {data["totalCount"]}'
        )
    elif data['totalCount'] == 0:
        raise ComponentVersionNotFound(
            f'No version search results for {component_name} / '
            f'{component_version_name}')
    else:
        return data['items'][0]['_meta']['href']


def manually_add_component(project_id, project_version_id,
                           component_version_link):
    url = hub.get_urlbase() + 'api/projects/{}/versions/{}/components'\
        .format(project_id, project_version_id)
    payload = {
        'component': component_version_link
    }
    r = hub.execute_post(url, data=payload)
    return r


def run(input_file_path, interval=10):
    with open(input_file_path, encoding='utf-8') as f:
        for line in f:
            data = json.loads(line)
            if data['type'] == 'table':
                break
    for item in data['data']['body']:
        component_name = item[0]
        component_version_name = item[1]
        print(f'Trying to add {component_name} / {component_version_name}')
        try:
            component_version_link = search_component_version_by_names(
                component_name, component_version_name
            )
            r = manually_add_component(project_id, project_version_id,
                                       component_version_link)
            if r.status_code == 200:
                print(f'Successfully added {component_name} / '
                      f'{component_version_name}')
            elif r.status_code == 412:
                print(f'Failed to add {component_name} / '
                      f'{component_version_name} since it already exists.')
            else:
                print(f'Failed to add {component_name} / '
                      f'{component_version_name} with API return code '
                      f'{r.status_code}.')
        except DuplicateComponentVersionSearchResultsError:
            print(f'Ignored adding {component_name} / {component_version_name} '
                  f'due to duplicate component version search results.')
        except ComponentNotFound:
            print(f'Ignored adding {component_name} / {component_version_name} '
                  f'because component {component_name} cannot be found in HUB')
        except ComponentVersionNotFound:
            print(f'Ignored adding {component_name} / {component_version_name} '
                  f'because component version {component_version_name} cannot '
                  f'be found in HUB')
        except:
            print(f'Ignored adding {component_name} / {component_version_name} '
                  f'due to an uncatched error.')
        time.sleep(interval)


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub01.dc1.lan/'
    project_id = '073b8604-7bf3-4d5d-8f26-59b11e775797'
    project_version_id = 'a9d8c6cb-0320-46e4-91cd-17a305dc012f'
    input_file_path = 'licenses_full.json'

    hub = HubInstance(urlbase, username, password, insecure=True)
    run(input_file_path=input_file_path, interval=10)
