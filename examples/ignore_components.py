"""
使用API忽略组件的调用例子
PUT /api/projects/{projectId}/versions/{projectVersionId}/components/{componentId}/versions/{componentVersionId}
"""
import json

from blackduck import Client
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] {%(module)s:%(lineno)d} %(levelname)s - %(message)s"
)


def ignore_components(project_id, project_version_id, component_id,
                      component_version_id):
    url = '/api/projects/{}/versions/{}/components/{}/versions/{}'.format(
        project_id, project_version_id, component_id, component_version_id
    )
    url = bd.base_url.rstrip('/') + url

    headers = {
        'Accept': 'application/json'
    }

    r = bd.session.get(url, headers=headers)

    payload = json.loads(r.text)
    # 删除不必要的payload
    del payload['licenseRiskProfile']
    del payload['securityRiskProfile']
    del payload['versionRiskProfile']
    del payload['activityRiskProfile']
    del payload['operationalRiskProfile']
    del payload['_meta']

    payload['ignored'] = True

    headers = {
        # 'Accept': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
        'Content-Type': 'application/vnd.blackducksoftware.bill-of-materials-6+json'
    }

    r = bd.session.put(url, data=json.dumps(payload), headers=headers)
    if r.status_code == 200:
        print('忽略组件成功')
        print(r.text)
    else:
        print('忽略组件失败, 返回码{}'.format(r.status_code))
        print(r.text)


if __name__ == '__main__':
    bd = Client(
        token='NTY3ZDZlNTgtNmQ0ZS00ODkyLWJiMGUtZjhiMmNjZmQwYjJlOmU3YjAxMWIwLWZmZjYtNDY3My1iNzc4LWQyMDdlMTg5ZjViNA==',
        base_url='https://lobsterapj.app.blackduck.com/',
        verify=False  # TLS certificate verification
    )

    project_id = '2207f366-87fc-4dba-a294-8b3a74c095d4'
    project_version_id = '86c3402f-e416-4ed0-bad5-f5ccaefea6fe'

    ignore_components(
        project_id='427b9f51-769b-4a73-b154-d09da8074fba',
        project_version_id='e2b5ba5b-cb91-45ca-b5cd-5bf0800cf760',
        component_id='427b9f51-769b-4a73-b154-d09da8074fba',
        component_version_id='cecbfefd-a214-42af-9101-f8f61f610cc8',
    )
