"""
Download all Scan archives (bido files) for a given project/version
Install the module:
pip install blackduck
"""
import json
import os
import requests
from blackduck.HubRestApi import <PERSON><PERSON><PERSON>nst<PERSON>


def get_codelocations(project_id, project_version_id, limit=99999):
    url = '/api/projects/{}/versions/{}/codelocations?limit={}'.format(
        project_id, project_version_id, limit
    )
    url = hub.get_urlbase() + url
    r = hub.execute_get(url)
    if r.status_code == 200:
        return json.loads(r.text)
    else:
        raise Exception('Failed to get the codelocations with status '
                        'code{}'.format(r.status_code))


def download_scan_archives(project_id, project_version_id, limit=99999,
                           dl_path=None, generate_map_file=True):
    """
    Download all scan archives for a given project/version
    """
    cl = get_codelocations(project_id, project_version_id, limit)
    print('There are {} bdio files to be downloaded'.format(cl['totalCount']))
    if dl_path is None:
        dl_path = os.path.dirname(os.path.abspath(__file__))
    print('Download path: {!r}'.format(dl_path))

    map_dict = {}

    for it in cl['items']:
        dl_url = it['_meta']['links'][1]['href']
        scan_name = it['name']
        file_name = dl_url.split('/')[-1]
        headers = hub.get_headers()
        r = requests.get(dl_url, headers=headers, verify=False, stream=True)
        if r.status_code == 200:
            with open(os.path.join(dl_path, file_name), 'wb') as f:
                for chunk in r.iter_content():
                    f.write(chunk)
            print('Scan name: {} bdio name: {} Download '
                  'succeeded'.format(scan_name, file_name))
            map_dict[file_name] = scan_name
        else:
            print('Scan name {} bdio name {} Download failed with status '
                  'code {}'.format(scan_name, file_name, r.status_code))

    if generate_map_file:
        with open('scan_map.json', 'w') as f:
            f.write(json.dumps(map_dict))


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://test.app.blackduck.com/'
    urlbase = urlbase.rstrip('/')

    hub = HubInstance(urlbase, username, password, insecure=True)
    download_scan_archives(
        project_id='0ceee942-afa4-4f24-88c6-606f93efe0f9',
        project_version_id='d1f37aae-e156-4ed1-a2d3-3433aba4fb3b'
    )


