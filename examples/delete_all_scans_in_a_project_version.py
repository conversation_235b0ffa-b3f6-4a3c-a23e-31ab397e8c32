"""
Delete all scans mapped to a given project/version
"""

import json
from blackduck.HubRestApi import <PERSON><PERSON><PERSON><PERSON><PERSON>


def get_codelocations(project_id, project_version_id, limit=9999):
    url = '/api/projects/{}/versions/{}/codelocations?limit={}'.format(
        project_id, project_version_id, limit
    )
    url = hub.get_urlbase() + url
    r = hub.execute_get(url)
    if r.status_code == 200:
        return json.loads(r.text)
    else:
        raise Exception('Failed to get codelocations for {} {}, '
                        'status code {}'.format(project_id, project_version_id,
                                                r.status_code))


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub01.dc1.lan/'

    hub = HubInstance(urlbase, username, password, insecure=True)

    cl = get_codelocations('47485c50-fe9d-4ac7-8016-d2ad90607ced',
                           '44255bd9-edda-4702-bf42-d21ee42d0966')
    for it in cl['items']:
        print('Deleting scan {}'.format(it['name']))
        hub.execute_delete(it['_meta']['href'])
