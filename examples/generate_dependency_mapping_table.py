"""
获取Match Type为Direct Dependency或是Transitive Dependency对应的scan name
"""

import json
import requests
from blackduck.HubRestApi import HubInstance
from terminaltables import AsciiTable


def get_scan_id_name_dict(project_id, project_version_id, limit=9999):
    """
    通过公开API
    GET /api/projects/{projectId}/versions/{versionId}/source-trees
    获取scan id和scan name的对应关系
    """
    url = '/api/projects/{}/versions/{}/source-trees?' \
          'limit={}'.format(project_id, project_version_id, limit)
    url = hub.get_urlbase() + url
    r = hub.execute_get(url)
    data = json.loads(r.text)
    scan_id_name_dict = {}
    for it in data['items']:
        for scan_id in it['scanIds']:
            scan_id_name_dict[scan_id] = it['name']
    return scan_id_name_dict


def get_source_bom_entries_data(project_id, project_version_id, limit=999999):
    """
    使用
    api/internal/projects/projectId/versions/projectVersionId/source-bom-entries
    获取source bom entries中所有dependency相关的数据
    """
    url = '/api/internal/projects/{}/versions/{}/source-bom-entries?' \
          'limit={}'.format(project_id, project_version_id, limit)
    url = hub.get_urlbase() + url
    filters = {
        'filter': ['bomMatchType:file_dependency',
                   'bomMatchType:file_dependency_direct',
                   'bomMatchType:file_dependency_transitive',
                   ]
    }
    headers = hub.get_headers()
    r = requests.get(url, headers=headers, params=filters, verify=False)

    data = json.loads(r.text)
    return data


def analyze_source_bom_entries(data, scan_id_name_dict):
    """
    分析source bom entries数据，获取scan和BOM的对应表
    """
    component_version_list = []
    for it in data['items']:
        if 'fileDependencyBomComponents' in it and \
                it['fileDependencyBomComponents']:
            scan_id = it['scanId']
            scan_name = scan_id_name_dict[scan_id]
            for bom in it['fileDependencyBomComponents']:
                matchtype = bom['versionBomMatchType']
                component_name = bom['project']['name']
                component_id = bom['project']['id']
                if 'release' in bom:
                    version_name = bom['release']['version']
                    version_id = bom['release']['id']
                else:
                    version_name = ''
                    version_id = ''
                component_version_list.append(
                    (scan_name, scan_id, component_name, component_id,
                     version_name, version_id, matchtype)
                )

    return list(set(component_version_list))


def generate_scan_bom_mapping_table(project_id, project_version_id):
    """
    生成scan和bom的映射表
    """
    table = list()
    data = get_source_bom_entries_data(project_id, project_version_id)
    scan_id_name_dict = get_scan_id_name_dict(project_id, project_version_id)
    component_version_list = analyze_source_bom_entries(data, scan_id_name_dict)
    for c_v in component_version_list:
        table.append([c_v[0], c_v[1], c_v[2], c_v[3], c_v[4], c_v[5], c_v[6]])
    table = sorted(table, key=lambda x: x[0])
    return table


if __name__ == '__main__':
    username = 'sysadmin'
    password = 'blackduck'
    urlbase = 'https://sup-cn-hub01.dc1.lan/'
    project_id = '304f4d2a-b625-4c86-b727-26ee2ba70474'
    project_version_id = '3d24c8c5-8042-451f-a6f1-af80ac4ca027'

    hub = HubInstance(urlbase, username, password, insecure=True)
    table = generate_scan_bom_mapping_table(
        project_id=project_id,
        project_version_id=project_version_id
    )

    table_title = ['scan name', 'scan id', 'component name', 'component id',
                   'version name', 'version id', 'match type']
    table.insert(0, table_title)
    print(AsciiTable(table).table)
