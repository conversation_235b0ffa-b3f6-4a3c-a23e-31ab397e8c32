#!/bin/bash

# Configuration
server_url="https://test.app.blackduck.com"
token="NTY3ZDZlNTgtNmQ0ZS00ODkyLWJiMGUtZjhiMmNjZmQwYjJlOmU3YjAxMWIwLWZmZjYtNDY3My1iNzc4LWQyMDdlMTg5ZjViNA=="
limit=1000

# Get Bearer Token
response=$(curl -X POST "$server_url/api/tokens/authenticate" \
  -H "Accept: application/vnd.blackducksoftware.user-4+json" \
  -H "Authorization: token $token")
bearerToken=$(echo $response | jq -r '.bearerToken')
echo "Bearer Token: $bearerToken"

# Get users with limit and write to JSON file
curl -H "Accept: application/vnd.blackducksoftware.user-4+json" \
  -H "Authorization: Bearer $bearerToken" \
  "$server_url/api/users?limit=$limit" -o users.json
