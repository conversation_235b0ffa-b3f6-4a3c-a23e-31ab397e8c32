#!/bin/bash
while true
do
    echo "$(date) - Testing proxy connection..."
    curl -m 5 \
         -x proxy.bmwbrill.cn:8080 \
         -U username:password \
         -H 'Accept-Language: en' \
         -H 'Accept: application/vnd.blackducksoftware.kb-authenticate-1+json' \
         -H 'Authorization: bdsLicenseKey test_license_key' \
         -H 'Cache-Control: no-cache, no-store, must-revalidate' \
         -H 'Pragma: no-cache' \
         -H 'Expires: 0' \
         -H 'Connection: Keep-Alive' \
         -H 'User-Agent: Hub/2024.1.1 (Linux 4.4.0-1128-aws amd64)' \
         -X POST \
         https://kb-na.blackducksoftware.com/api/authenticate \
         -o /dev/null \
         -s \
         -w '%{http_code}\n'
    if [ $? -ne 0 ]; then
        echo "Connection failed"
    fi
    sleep 0.3  # 每0.3秒测试一次
done