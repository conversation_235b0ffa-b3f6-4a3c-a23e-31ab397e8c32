# !/bin/bash
# Download report

# Configuration
server_url="https://hub_url"
token="token"
project_id="bbb1a89a-a017-4885-912a-1c9ea8144c90"
project_version_id="221c358a-4aa8-4451-9857-7bea2d1a55a5"

# Get Bearer Token
response=$(curl -X POST "$server_url/api/tokens/authenticate" \
  -H "Accept: application/vnd.blackducksoftware.user-4+json" \
  -H "Authorization: token $token")
bearerToken=$(echo $response | jq -r '.bearerToken')
echo "Bearer Token: $bearerToken"

# Get report ID
report_id=$(curl -i -X POST "$server_url/api/versions/$project_version_id/reports" \
  -H "content-type: application/vnd.blackducksoftware.report-4+json" \
  -H "Authorization: Bearer $bearerToken" \
  --data-raw '{"reportFormat":"CSV","reportType":"VERSION","categories":["COMPONENTS"],"includeSubprojects":true}' \
  | grep -i location | sed 's/.*\/reports\///' | sed 's/[^a-zA-Z0-9-].*//')

echo "Report ID: $report_id"

# Check report status
status="PENDING"
while [ "$status" != "COMPLETED" ]; do
  echo "Checking report status..."
  response=$(curl -H "Accept: application/vnd.blackducksoftware.report-4+json" \
      -H "Authorization: Bearer $bearerToken" \
      "$server_url/api/projects/$project_id/versions/$project_version_id/reports/$report_id")
  status=$(echo $response | jq -r '.status')
  echo "Current status: $status"
  [ "$status" != "COMPLETED" ] && sleep 10
done

echo "Report is completed, downloading..."

# Donwload report
curl -H "Accept: application/vnd.blackducksoftware.report-4+json" \
     -H "Authorization: Bearer $bearerToken" \
     -o report.zip \
     "$server_url/api/projects/$project_id/versions/$project_version_id/reports/$report_id/download"
