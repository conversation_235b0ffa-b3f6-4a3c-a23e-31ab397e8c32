#!/bin/bash

# Configuration
server_url="https://127.0.0.1"
token="NjVhZDljZjQtZjcyNS00YTFmLWI2ZTgtMTNjNGU1ZWYxMjRlOjU2ZTNlYTY1LTdkN2MtNGViYS04NThkLTVhMzBkYzVmNjg4MA=="
project_id="c208f49c-d2d7-4d71-aec9-4c4613e23bb6"
project_version_id="32c55fac-9ac1-4ec6-aa74-572d971ca3ea"

echo "=============================="
echo "Step 1: Getting Bearer Token"
echo "=============================="
response=$(curl -k -X POST "$server_url/api/tokens/authenticate" \
  -H "Accept: application/vnd.blackducksoftware.user-4+json" \
  -H "Authorization: token $token")
bearerToken=$(echo "$response" | jq -r '.bearerToken')
echo "Bearer Token: $bearerToken"
echo ""

echo "=============================="
echo "Step 2: Calling API with v6 headers"
echo "=============================="
curl -k -H "Accept: application/vnd.blackducksoftware.bill-of-materials-6+json" \
  -H "Authorization: Bearer $bearerToken" \
  "$server_url/api/projects/$project_id/versions/$project_version_id/vulnerable-bom-components?limit=1000"
echo ""
echo ""

echo "=============================="
echo "Step 3: Calling API with v8 headers"
echo "=============================="
curl -k -H "Accept: application/vnd.blackducksoftware.bill-of-materials-8+json" \
  -H "Authorization: Bearer $bearerToken" \
  "$server_url/api/projects/$project_id/versions/$project_version_id/vulnerable-bom-components?limit=1000"
echo ""
echo "=============================="
echo "Script execution completed"
echo "=============================="
