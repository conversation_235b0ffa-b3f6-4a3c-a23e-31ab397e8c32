import re


def parse_memory(s):
    """
    Parses a memory string (e.g., '1024M', '4G') and returns the value in MB.
    Assumes G = 1024 M.
    """
    s = str(s).strip().upper()
    if s.endswith('G'):
        return float(s[:-1]) * 1024
    if s.endswith('M'):
        return float(s[:-1])
    if s.endswith('K'):
        return float(s[:-1]) / 1024
    return float(s)


def analyze_docker_compose_manually(file_path, memory_unit='MB'):
    """
    Analyzes a Docker Compose YAML file to calculate CPU and memory resource allocations
    by manually parsing the file without external libraries.

    Args:
        file_path (str): The path to the docker-compose YAML file.
        memory_unit (str): The unit for memory display ('MB' or 'GB').
    """
    unit = memory_unit.upper()
    if unit not in ['MB', 'GB']:
        print("Error: Invalid memory_unit. Please choose 'MB' or 'GB'.")
        return

    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"Error: The file '{file_path}' was not found.")
        return

    services_data = {}
    current_service = None
    context = None  # To track if we are in 'reservations' or 'limits'

    for line in lines:
        stripped_line = line.strip()
        indentation = len(line) - len(line.lstrip())

        # Skip comment lines
        if stripped_line.startswith('#'):
            continue

        if stripped_line.startswith('services:'):
            continue

        if indentation == 2 and stripped_line.endswith(':'):
            current_service = stripped_line[:-1]
            services_data[current_service] = {
                'replicas': 1,
                'reservations_cpus': '0',
                'reservations_memory': '0M',
                'limits_cpus': '0',
                'limits_memory': '0M'
            }
            context = None
            continue

        if not current_service:
            continue

        key, _, value = map(str.strip, stripped_line.partition(':'))
        value = value.replace("'", "").replace('"', '')

        if 'replicas' in key and indentation > 2:
            services_data[current_service]['replicas'] = int(value)
        elif 'reservations' in key and indentation > 2:
            context = 'reservations'
        elif 'limits' in key and indentation > 2:
            context = 'limits'

        if context and indentation > 4:
            if 'cpus' in key:
                services_data[current_service][f'{context}_cpus'] = value
            elif 'memory' in key:
                services_data[current_service][f'{context}_memory'] = value

    total_cpu_reservations = 0
    total_cpu_limits = 0
    total_mem_reservations_mb = 0
    total_mem_limits_mb = 0

    print(f"\n--- Resource Analysis (Memory Unit: {unit}) ---")
    header = (f"{'Service':<20} | {'Replicas':<10} | {'CPU Reservation':<20} | {'CPU Limit':<15} | "
              f"{'Mem Reservation (' + unit + ')':<25} | {'Mem Limit (' + unit + ')':<20}")
    print(header)
    print("-" * len(header))

    mem_format = ',.0f' if unit == 'MB' else ',.3f'
    divisor = 1 if unit == 'MB' else 1024

    for service_name, data in services_data.items():
        replicas = data['replicas']

        cpu_res = float(data['reservations_cpus'])
        mem_res_mb = parse_memory(data['reservations_memory'])

        cpu_lim = float(data['limits_cpus'])
        mem_lim_mb = parse_memory(data['limits_memory'])

        total_cpu_reservations += cpu_res * replicas
        total_cpu_limits += cpu_lim * replicas
        total_mem_reservations_mb += mem_res_mb * replicas
        total_mem_limits_mb += mem_lim_mb * replicas

        display_mem_res = mem_res_mb / divisor
        display_mem_lim = mem_lim_mb / divisor

        print(f"{service_name:<20} | {replicas:<10} | {f'{cpu_res:.3f} ({cpu_res * replicas:.3f})':<20} | "
              f"{f'{cpu_lim:.3f} ({cpu_lim * replicas:.3f})':<15} | "
              f"{f'{display_mem_res:{mem_format}} ({display_mem_res * replicas:{mem_format}})':<25} | "
              f"{f'{display_mem_lim:{mem_format}} ({display_mem_lim * replicas:{mem_format}})':<20}")

    print("-" * len(header))
    total_display_mem_res = total_mem_reservations_mb / divisor
    total_display_mem_lim = total_mem_limits_mb / divisor

    print(f"{'TOTAL':<20} | {'':<10} | {f'{total_cpu_reservations:.3f}':<20} | "
          f"{f'{total_cpu_limits:.3f}':<15} | "
          f"{f'{total_display_mem_res:{mem_format}}':<25} | "
          f"{f'{total_display_mem_lim:{mem_format}}':<20}")

    # Calculate and display container count information
    total_services = len(services_data)
    total_containers = sum(data['replicas'] for data in services_data.values())

    print(f"\n--- Container Count Information ---")
    print(f"Total Services: {total_services}")
    print(f"Total Containers (including replicas): {total_containers}")
    print(f"\nContainer breakdown by service:")
    for service_name, data in services_data.items():
        replicas = data['replicas']
        if replicas > 1:
            print(f"  {service_name}: {replicas} containers")
        else:
            print(f"  {service_name}: {replicas} container")

# --- HOW TO USE ---
# To run this code, save it as a Python file (e.g., analyze.py) and place it
# in the same directory as your '120sph.yaml' file.
# Then run from your terminal: python analyze.py

# Example 1: Analyze with memory unit as MB (default)
analyze_docker_compose_manually('docker-compose.local-overrides-96C-1TB-2024.1.0-zhex.yml', memory_unit='MB')

# Example 2: Analyze with memory unit as GB
# analyze_docker_compose_manually('120sph.yaml', memory_unit='GB')