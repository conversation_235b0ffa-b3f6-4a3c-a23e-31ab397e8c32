version: '3.6'
services:
  authentication:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "3464M"
        reservations:
          cpus: "0.100"
          memory: "1024M"
      replicas: 1
  bomengine:
    environment:
      HUB_MAX_MEMORY: 17408m
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "18432M"
        reservations:
          cpus: "1"
          memory: "14336M"
      replicas: 5
  cfssl:
    deploy:
      resources:
        limits:
          cpus: "0.500"
          memory: "1024M"
        reservations:
          cpus: "0.300"
          memory: "1024M"
      replicas: 1
  documentation:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "2048M"
        reservations:
          cpus: "0.100"
          memory: "768M"
      replicas: 1
  jobrunner:
    environment:
      HUB_MAX_MEMORY: 17408m
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "18432M"
        reservations:
          cpus: "1"
          memory: "12288M"
      replicas: 5
  logstash:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "6144M"
        reservations:
          cpus: "0.100"
          memory: "1536M"
      replicas: 1
  matchengine:
    environment:
      HUB_MAX_MEMORY: 17408m
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "18432M"
        reservations:
          cpus: "1"
          memory: "12288M"
      replicas: 5
  postgres:
    environment:
      HUB_PG_SHARED_BUFFERS: '12GB'
      HUB_PG_EFFECTIVE_CACHE_SIZE: '37GB'
      HUB_PG_AUTOVACUUM_MAX_WORKERS: 8
      HUB_PG_MAINTENANCE_WORK_MEM: '2GB'
      HUB_PG_MAX_CONNECTIONS: 300
      HUB_PG_WORK_MEM: '32MB'
    deploy:
      resources:
        limits:
          cpus: "32.000"
          memory: "50G"
        reservations:
          cpus: "24.000"
          memory: "36G"
      replicas: 1
  postgres-upgrader:
    deploy:
      resources:
        limits:
          cpus: "2.000"
          memory: "4G"
        reservations:
          cpus: "1.000"
          memory: "4G"
  rabbitmq:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "16384M"
        reservations:
          cpus: "1"
          memory: "10240M"
      replicas: 1
  redis:
    environment:
      BLACKDUCK_REDIS_MAXMEMORY: "9216mb"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "10240M"
        reservations:
          cpus: "0.500"
          memory: "5120M"
      replicas: 1
  registration:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "3072M"
        reservations:
          cpus: "0.100"
          memory: "1024M"
      replicas: 1
  scan:
    environment:
      HUB_MAX_MEMORY: 17408m
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "18432M"
        reservations:
          cpus: "1"
          memory: "12288M"
      replicas: 4
  storage:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "10240M"
        reservations:
          cpus: "0.700"
          memory: "3072M"
      replicas: 1
  uploadcache:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "2048M"
        reservations:
          cpus: ".300"
          memory: "1024M"
      replicas: 1
  webapp:
    deploy:
      resources:
        limits:
          cpus: "4.000"
          memory: "20480M"
        reservations:
          cpus: "3.000"
          memory: "15360M"
      replicas: 1
  webserver:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "2048M"
        reservations:
          cpus: "0.400"
          memory: "1024M"
      replicas: 1
  webui:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "2048M"
        reservations:
          cpus: ".300"
          memory: "1024M"
      replicas: 1