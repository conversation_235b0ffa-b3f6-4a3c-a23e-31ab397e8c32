version: '3.6'
services:
  authentication:
    environment:
      HUB_MAX_MEMORY: "3118M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "3464M"
        reservations:
          cpus: "0.100"
          memory: "1024M"
      replicas: 1
  bomengine:
    environment:
      HUB_MAX_MEMORY: "16384M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "18432M"
        reservations:
          cpus: "0.100"
          memory: "1024M"
      replicas: 3
  cfssl:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "1024M"
        reservations:
          cpus: "0.300"
          memory: "1024M"
      replicas: 1
  documentation:
    environment:
      HUB_MAX_MEMORY: "2048M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "3072M"
        reservations:
          cpus: "0.100"
          memory: "768M"
      replicas: 1
  jobrunner:
    environment:
      HUB_MAX_MEMORY: "18432M"
      BLACKDUCK_DEPLOYMENT_BASE_SIZE: "sizes-gen05/2000sph_updated.yaml"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "20480M"
        reservations:
          cpus: "0.200"
          memory: "1280M"
      replicas: 3
  logstash:
    environment:
      HUB_MAX_MEMORY: "3686M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "4096M"
        reservations:
          cpus: "0.100"
          memory: "1536M"
      replicas: 1
  matchengine:
    environment:
      HUB_MAX_MEMORY: "16384M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "18432M"
        reservations:
          cpus: "0.100"
          memory: "1280M"
      replicas: 3
  postgres:
    environment:
      HUB_PG_SHARED_BUFFERS: '16GB'
      HUB_PG_EFFECTIVE_CACHE_SIZE: '48GB'
      HUB_PG_AUTOVACUUM_MAX_WORKERS: 8
      HUB_PG_MAINTENANCE_WORK_MEM: '2GB'
      HUB_PG_MAX_CONNECTIONS: 300
      HUB_PG_WORK_MEM: '32MB'
      HUB_PG_RANDOM_PAGE_COST: 1.1
      HUB_PG_CHECKPOINT_COMPLETION_TARGET: 0.9
    deploy:
      resources:
        limits:
          cpus: "32.000"
          memory: "64G"
        reservations:
          cpus: "24.000"
          memory: "64G"
      replicas: 1
  postgres-upgrader:
    deploy:
      resources:
        limits:
          cpus: "2.000"
          memory: "4G"
        reservations:
          cpus: "1.000"
          memory: "4G"
  rabbitmq:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "16384M"
        reservations:
          cpus: "0.500"
          memory: "1433M"
      replicas: 1
  redis:
    environment:
      BLACKDUCK_REDIS_MAXMEMORY: "9216mb"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "10240M"
        reservations:
          cpus: "0.500"
          memory: "5120M"
      replicas: 1
  registration:
    environment:
      HUB_MAX_MEMORY: "2765M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "3072M"
        reservations:
          cpus: "0.100"
          memory: "1024M"
      replicas: 1
  scan:
    environment:
      HUB_MAX_MEMORY: "16384M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "18432M"
        reservations:
          cpus: "0.100"
          memory: "1024M"
      replicas: 3
  storage:
    environment:
      HUB_MAX_MEMORY: "8192M"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "10240M"
        reservations:
          cpus: "0.700"
          memory: "3072M"
      replicas: 1
  webapp:
    environment:
      HUB_MAX_MEMORY: "18432M"
    deploy:
      resources:
        limits:
          cpus: "4.000"
          memory: "20480M"
        reservations:
          cpus: "3.000"
          memory: "15360M"
      replicas: 1
  webserver:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "2048M"
        reservations:
          cpus: "0.400"
          memory: "1024M"
      replicas: 1
