# The comments in this file show via example how to override some of the most popular set of options.
# However, it is possible to override any Docker configuration setting, for example Port mappings, by adding the override here.
version: '3.6'
services:
  postgres:
    deploy:
      resources:
        limits: {cpus: '36', memory: 36864M}
        reservations: {cpus: '36', memory: 36864M}
    #deploy:
    ## This placement constraint example requires running:  docker node update --label-add type=db xxxNodeId
    ## Note: postgres and postgres-upgrader must be deployed to the name node
    #  placement:
    #    constraints:
    #    - node.labels.type == db
  #postgres-upgrader:
    #deploy:
    ## This placement constraint example requires running:  docker node update --label-add type=db xxxNodeId
    ## Note: postgres and postgres-upgrader must be deployed to the name node
    #  placement:
    #    constraints:
    #    - node.labels.type == db
  authentication:
    environment:
      HUB_MAX_MEMORY: 1900m
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    deploy:
      resources:
        limits: {cpus: '1', memory: 2048M}
        reservations: {cpus: '1', memory: 2048M}
  webapp:
    environment:
      HUB_MAX_MEMORY: 63000m
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    #environment:
    #  HUB_MAX_MEMORY: REPLACE_WITH_NEW_HUB_MAX_HEAP_MEMORYm
    #  HUB_MIN_MEMORY: REPLACE_WITH_NEW_HUB_MIN_HEAP_MEMORYm
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    deploy:
      resources:
        limits: {cpus: '10', memory: 65536M}
        reservations: {cpus: '10', memory: 65536M}
    #deploy:
    #  resources:
    #    limits: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #    reservations: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #  placement:
    #    constraints:
    #    - node.labels.type == db
  scan:
    environment:
      HUB_MAX_MEMORY: 15000m
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    deploy:
      replicas: 6
      resources:
        limits: {cpus: '1', memory: 16384M}
        reservations: {cpus: '1', memory: 16384M}
    #deploy:
    #  resources:
    #    limits: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #    reservations: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #  replicas: REPLACE_WITH_NUM_REPLICAS
  storage:
    environment:
      HUB_MAX_MEMORY: 7000m
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    deploy:
      replicas: 1
      resources:
        limits: {cpus: '1', memory: 8192M}
        reservations: {cpus: '1', memory: 8192M}
    #deploy:
    #  resources:
    #    limits: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #    reservations: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #  placement:
    #    constraints:
    #    - node.labels.type == db
  jobrunner:
    environment:
      HUB_MAX_MEMORY: 19000m
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    #environment:
    #  HUB_MAX_MEMORY: REPLACE_WITH_NEW_HUB_MAX_HEAP_MEMORYm
    #  HUB_MIN_MEMORY: REPLACE_WITH_NEW_HUB_MIN_HEAP_MEMORYm
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    deploy:
      endpoint_mode: dnsrr
      replicas: 6
      resources:
        limits: {cpus: '1', memory: 20480M}
        reservations: {cpus: '1', memory: 20480M}
    #deploy:
    #  resources:
    #    limits: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #    reservations: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #  replicas: REPLACE_WITH_NUM_REPLICAS
  cfssl:
    deploy:
      resources:
        limits: {cpus: '1', memory: 1024M}
        reservations: {cpus: '1', memory: 1024M}  
  logstash:
    #environment:
    #  HUB_MAX_MEMORY: REPLACE_WITH_NEW_HUB_MAX_HEAP_MEMORYm
    #  HUB_MIN_MEMORY: REPLACE_WITH_NEW_HUB_MIN_HEAP_MEMORYm
    deploy:
      resources:
        limits: {cpus: '1', memory: 4096M}
        reservations: {cpus: '1', memory: 4096M}
    #deploy:
    #  placement:
    #    constraints:
    #    - node.labels.type == db
    #  resources:
    #    limits: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #    reservations: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
  registration:
    environment: {REGISTRATION_SERVICE_OPTS: '-Dblackduck.registration.server.external.access.protocol=https -Dblackduck.registration.server.host=updates.suite.blackducksoftware.com -Djdk.http.auth.tunneling.disabledSchemes=""'}
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    deploy:
      resources:
        limits: {cpus: '1', memory: 4096M}
        reservations: {cpus: '1', memory: 4096M}
    #deploy:
    #  placement:
    #    constraints:
    #    - node.labels.type == db
  webserver:
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    #  - WEBSERVER_CUSTOM_CERT_FILE
    #  - WEBSERVER_CUSTOM_KEY_FILE
    deploy:
      resources:
        limits: {cpus: '1', memory: 2048M}
        reservations: {cpus: '1', memory: 2048M}
  bomengine:
    environment:
      HUB_MAX_MEMORY: 19000m
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    #environment:
    #  HUB_MAX_MEMORY: REPLACE_WITH_NEW_HUB_MAX_HEAP_MEMORYm
    #  HUB_MIN_MEMORY: REPLACE_WITH_NEW_HUB_MIN_HEAP_MEMORYm
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    deploy:
        endpoint_mode: dnsrr
        replicas: 6
        resources:
          limits: {cpus: '1', memory: 20480M}
          reservations: {cpus: '1', memory: 20480M}
    #deploy:
    #  resources:
    #    limits: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #    reservations: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #  replicas: REPLACE_WITH_NUM_REPLICAS
  rabbitmq:
    deploy:
      resources:
        limits: {cpus: '1', memory: 16384M}
        reservations: {cpus: '1', memory: 16384M}
  webui:
    deploy:
      resources:
        limits: {cpus: '5', memory: 10240M}
        reservations: {cpus: '5', memory: 10240M}
  matchengine:
    environment:
      HUB_MAX_MEMORY: 19000m
    volumes: ['/directory/where/the/HUB_PROXY_PASSWORD_FILE/is:/run/secrets']
    #environment:
    #  HUB_MAX_MEMORY: REPLACE_WITH_NEW_HUB_MAX_HEAP_MEMORYm
    #  HUB_MIN_MEMORY: REPLACE_WITH_NEW_HUB_MIN_HEAP_MEMORYm
    #secrets:
    #  - HUB_PROXY_PASSWORD_FILE
    deploy:
      replicas: 10
      resources:
        limits: {cpus: '1', memory: 20480M}
        reservations: {cpus: '1', memory: 20480M} 
    #deploy:
    #  resources:
    #    limits: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #    reservations: {cpus: 'REPLACE_WITH_NUM_CPUS', memory: REPLACE_WITH_NEW_MEM_LIMITm}
    #  replicas: REPLACE_WITH_NUM_REPLICAS
  documentation:
    deploy:
      resources:
        limits: {cpus: '1', memory: 4096M}
        reservations: {cpus: '1', memory: 4096M}
  redis:
    deploy:
      resources:
        limits: {cpus: '1', memory: 16384M}
        reservations: {cpus: '1', memory: 16384M}
#secrets:
#  HUB_PROXY_PASSWORD_FILE:
#    external: true
#    name: "hub_PROXY_PASSWORD_FILE"
#  WEBSERVER_CUSTOM_CERT_FILE:
#    external: true
#    name: "hub_WEBSERVER_CUSTOM_CERT_FILE"
#  WEBSERVER_CUSTOM_KEY_FILE:
#    external: true
#    name: "hub_WEBSERVER_CUSTOM_KEY_FILE"
