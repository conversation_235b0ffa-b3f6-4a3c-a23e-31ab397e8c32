#cat sizes-gen04/250sph-modify-20250301.yaml
version: '3.6'
services:
  authentication:
    environment:
      HUB_MAX_MEMORY: "1843m"
    deploy:
      resources:
        limits:
          cpus: ".500"
          memory: "2048M"
        reservations:
          cpus: ".300"
          memory: "1638M"
      replicas: 1
  bomengine:
    environment:
      HUB_MAX_MEMORY: "10g"
    deploy:
      resources:
        limits:
          cpus: "2.000"
          memory: "12G"
        reservations:
          cpus: "2.000"
          memory: "12G"
      replicas: 2
  cfssl:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "2048M"
        reservations:
          cpus: ".200"
          memory: "260M"
      replicas: 1
  documentation:
    environment:
      HUB_MAX_MEMORY: "1333m"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "2048M"
        reservations:
          cpus: ".200"
          memory: "1024M"
      replicas: 1
  jobrunner:
    environment:
      HUB_MAX_MEMORY: "16g"
      BLACKDUCK_DEPLOYMENT_BASE_SIZE: "sizes-gen04/250sph.yaml"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "18G"
        reservations:
          cpus: "1.000"
          memory: "14G"
      replicas: 2
  logstash:
    environment:
      HUB_MAX_MEMORY: "2765m"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "3072M"
        reservations:
          cpus: ".500"
          memory: "2048M"
      replicas: 1
  matchengine:
    environment:
      HUB_MAX_MEMORY: "7373m"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "8192M"
        reservations:
          cpus: "1.000"
          memory: "6144M"
      replicas: 3
  postgres:
    environment:
    HUB_PG_SHARED_BUFFERS: '6GB'
    HUB_PG_EFFECTIVE_CACHE_SIZE: '18GB'
    HUB_PG_WORK_MEM: '16MB'
    HUB_PG_MAINTENANCE_WORK_MEM: '1GB'
    HUB_PG_MAX_CONNECTIONS: 300
    HUB_PG_AUTOVACUUM_MAX_WORKERS: 3         

    # --- 并行查询 (Parallelism) for 6 Cores ---
    HUB_PG_MAX_WORKER_PROCESSES: 6               # 保持: 等于CPU核心数
    HUB_PG_MAX_PARALLEL_WORKERS: 6               # 保持: 等于CPU核心数
    HUB_PG_MAX_PARALLEL_WORKERS_PER_GATHER: 3    # 保持: 单个查询最多使用3个并行进程
    deploy:
      resources:
        limits:
          cpus: "6.000"
          memory: "24G"
        reservations:
          cpus: "3.000"
          memory: "24G"
      replicas: 1
  postgres-upgrader:
    deploy:
      resources:
        limits:
          cpus: "2.000"
          memory: "4G"
        reservations:
          cpus: "1.000"
          memory: "4G"
  rabbitmq:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "8192M"
        reservations:
          cpus: ".200"
          memory: "2048M"
      replicas: 1
  redis:
    environment:
      BLACKDUCK_REDIS_MAXMEMORY: "1844mb"
    deploy:
      resources:
        limits:
          cpus: ".500"
          memory: "2048M"
        reservations:
          cpus: ".200"
          memory: "512M"
      replicas: 1
  registration:
    environment:
      HUB_MAX_MEMORY: "2048m"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "3072M"
        reservations:
          cpus: ".200"
          memory: "1331M"
      replicas: 1
  scan:
    environment:
      HUB_MAX_MEMORY: "9216m"
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "10240M"
        reservations:
          cpus: ".700"
          memory: "10240M"
      replicas: 2
  storage:
    environment:
      HUB_MAX_MEMORY: "5530m"
    deploy:
      resources:
        limits:
          cpus: "4.000"
          memory: "6144M"
        reservations:
          cpus: ".700"
          memory: "2048M"
      replicas: 1
  webapp:
    environment:
      HUB_MAX_MEMORY: "18432m"
    deploy:
      resources:
        limits:
          cpus: "2.000"
          memory: "20480M"
        reservations:
          cpus: "1.000"
          memory: "20480M"
      replicas: 1
  webserver:
    deploy:
      resources:
        limits:
          cpus: "1.000"
          memory: "1024M"
        reservations:
          cpus: ".200"
          memory: "1024M"
      replicas: 1
--- #分割
#cat docker-compose.local-overrides.yml
version: '3.6'
services:
  storage:
    environment:
      BINARY_UPLOAD_MAX_SIZE: 20480m
  scan:
    environment:
      BINARY_UPLOAD_MAX_SIZE: 20480m
  webserver:
    environment:
      BINARY_UPLOAD_MAX_SIZE: 20480m
    healthcheck:
      test: [CMD, /usr/local/bin/docker-healthcheck.sh, 'https://localhost:8443/health-checks/liveness', /run/secrets/WEBSERVER_CUSTOM_CERT_FILE]
    secrets:
    #  - HUB_PROXY_PASSWORD_FILE
      - WEBSERVER_CUSTOM_CERT_FILE
      - WEBSERVER_CUSTOM_KEY_FILE
secrets:
#  HUB_PROXY_PASSWORD_FILE:
#    external: true
#    name: "hub_PROXY_PASSWORD_FILE"
  WEBSERVER_CUSTOM_CERT_FILE:
    external: true
    name: "hub_WEBSERVER_CUSTOM_CERT_FILE"
  WEBSERVER_CUSTOM_KEY_FILE:
    external: true
    name: "hub_WEBSERVER_CUSTOM_KEY_FILE"
--- #分割
# cat docker-compose-modify-20241123.bdba.yml
version: '3.6'
# Starting with the 2019.2.0 release: DO NOT EDIT THIS FILE!!!
# ADD ANY OF YOUR OVERRIDES IN THE docker-compose.local-overrides.yml FILE
# Refer to the Release Notes or Installation Guide for more information.

services:
  binaryscanner:
    image: sigsynopsys/bdba-worker:2024.3.1
    env_file: [hub-bdba.env]
    entrypoint: /docker-entrypoint.sh
    healthcheck:
       test:  [CMD, '/docker-healthcheck.sh', '${BDBA_HEALTH_CHECK_TIMEOUT:-30}']
       interval: 120s
       timeout: ${BDBA_HEALTH_CHECK_TIMEOUT:-30}s
       retries: 5
       start_period: 300s
    deploy:
      mode: replicated
      restart_policy: {condition: on-failure, delay: 5s, window: 60s}
      resources:
        limits: {memory: 20480M, cpus: '10'}
        reservations: {memory: 8192M, cpus: '2'}
  rabbitmq:
    env_file: [hub-bdba.env]
